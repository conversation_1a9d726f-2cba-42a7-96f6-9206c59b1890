part of 'model.dart';

class CustomFieldModel extends Equatable {
  final String id;
  final bool enabled;
  final CustomFieldDataTypeModel dataType;
  final CustomFieldTypeModel type;
  final String label;
  final int order;
  final bool showOnCustomerPortal;
  final bool isEmpty;
  final bool isNotEmpty;
  final List<CustomFieldOptionModel> options;
  final bool isAdditionalField;
  final CustomFieldOemModel? oem;
  final String value;
  final String? color;
  final String? description;
  final String? slug;
  final VisibilityScopeModel? visibilityScope;
  final String? createdBy;
  final String? createdAt;
  final String? updatedAt;

  const CustomFieldModel({
    required this.id,
    required this.enabled,
    required this.dataType,
    required this.type,
    required this.label,
    required this.order,
    required this.showOnCustomerPortal,
    required this.isEmpty,
    required this.isNotEmpty,
    required this.value,
    this.options = const [],
    this.isAdditionalField = false,
    this.oem,
    this.color,
    this.description,
    this.slug,
    this.visibilityScope,
    this.createdBy,
    this.createdAt,
    this.updatedAt,
  });

  factory CustomFieldModel.fromServiceModel(CustomField model) {
    return CustomFieldModel(
      id: model.id,
      enabled: model.enabled,
      dataType: CustomFieldDataTypeModel.fromServiceModel(model.dataType),
      type: CustomFieldTypeModel.fromServiceModel(model.type),
      label: model.label,
      order: model.order,
      oem: model.oem != null
          ? CustomFieldOemModel.fromServiceModel(model.oem!)
          : null,
      showOnCustomerPortal: model.showOnCustomerPortal,
      isEmpty: model.isEmpty,
      isNotEmpty: model.isNotEmpty,
      options:
          model.options.map(CustomFieldOptionModel.fromServiceModel).toList(),
      isAdditionalField: model.isAdditionalField,
      value: model.value,
      color: model.color,
      description: model.description,
      slug: model.slug,
      visibilityScope:
          VisibilityScopeModel.fromServiceModel(model.visibilityScope),
      createdBy: model.createdBy,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
    );
  }

  CustomFieldModel copyWith({
    String? id,
    bool? enabled,
    CustomFieldDataTypeModel? dataType,
    CustomFieldTypeModel? type,
    String? label,
    int? order,
    CustomFieldOemModel? oem,
    bool? showOnCustomerPortal,
    bool? isEmpty,
    bool? isNotEmpty,
    List<CustomFieldOptionModel>? options,
    bool? isAdditionalField,
    String? value,
    String? color,
    String? description,
    String? slug,
    VisibilityScopeModel? visibilityScope,
    String? createdBy,
    String? createdAt,
    String? updatedAt,
  }) {
    return CustomFieldModel(
      id: id ?? this.id,
      enabled: enabled ?? this.enabled,
      dataType: dataType ?? this.dataType,
      type: type ?? this.type,
      label: label ?? this.label,
      order: order ?? this.order,
      oem: oem ?? this.oem,
      showOnCustomerPortal: showOnCustomerPortal ?? this.showOnCustomerPortal,
      isEmpty: isEmpty ?? this.isEmpty,
      isNotEmpty: isNotEmpty ?? this.isNotEmpty,
      options: options ?? this.options,
      isAdditionalField: isAdditionalField ?? this.isAdditionalField,
      value: value ?? this.value,
      color: color ?? this.color,
      description: description ?? this.description,
      slug: slug ?? this.slug,
      visibilityScope: visibilityScope ?? this.visibilityScope,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  bool get isTag => color != null;

  String getValue(AppLocalizations localization) =>
      isEmpty ? localization.empty : value;

  bool get isPhoneNumber {
    if (isTag) return false;
    int number = int.tryParse(value) ?? 0;
    return number.toString().length >= 6 && number.toString().length <= 15;
  }

  @override
  List<Object?> get props => [id, label, value, color];
}

class CustomFieldOemModel {
  final String id;
  final String name;

  const CustomFieldOemModel({required this.id, required this.name});

  factory CustomFieldOemModel.fromServiceModel(CustomFieldOem model) {
    return CustomFieldOemModel(id: model.id, name: model.name);
  }
}

class CustomFieldOptionModel {
  final String id;
  final String color;
  final String value;
  final String? description;

  const CustomFieldOptionModel({
    required this.id,
    required this.color,
    required this.value,
    this.description,
  });

  factory CustomFieldOptionModel.fromServiceModel(CustomFieldOption model) {
    return CustomFieldOptionModel(
      id: model.id,
      color: model.color,
      value: model.value,
      description: model.description,
    );
  }
}

enum CustomFieldDataTypeModel {
  text,
  number,
  date,
  singleSelect,
  invalid;

  factory CustomFieldDataTypeModel.fromServiceModel(CustomFieldDataType type) {
    switch (type) {
      case CustomFieldDataType.text:
        return CustomFieldDataTypeModel.text;
      case CustomFieldDataType.number:
        return CustomFieldDataTypeModel.number;
      case CustomFieldDataType.date:
        return CustomFieldDataTypeModel.date;
      case CustomFieldDataType.singleSelect:
        return CustomFieldDataTypeModel.singleSelect;
      case CustomFieldDataType.invalid:
        return CustomFieldDataTypeModel.invalid;
    }
  }
}

enum CustomFieldTypeModel {
  connections,
  assets,
  workOrders,
  parts,
  invalid;

  factory CustomFieldTypeModel.fromServiceModel(CustomFieldType type) {
    switch (type) {
      case CustomFieldType.connections:
        return CustomFieldTypeModel.connections;
      case CustomFieldType.assets:
        return CustomFieldTypeModel.assets;
      case CustomFieldType.workOrders:
        return CustomFieldTypeModel.workOrders;
      case CustomFieldType.parts:
        return CustomFieldTypeModel.parts;
      case CustomFieldType.invalid:
        return CustomFieldTypeModel.invalid;
    }
  }

  CustomFieldType get toServiceModel {
    switch (this) {
      case CustomFieldTypeModel.connections:
        return CustomFieldType.connections;
      case CustomFieldTypeModel.assets:
        return CustomFieldType.assets;
      case CustomFieldTypeModel.workOrders:
        return CustomFieldType.workOrders;
      case CustomFieldTypeModel.parts:
        return CustomFieldType.parts;
      case CustomFieldTypeModel.invalid:
        return CustomFieldType.invalid;
    }
  }
}

enum VisibilityScopeModel {
  internal,
  external,
  invalid;

  factory VisibilityScopeModel.fromServiceModel(
    VisibilityScope visibilityScope,
  ) {
    switch (visibilityScope) {
      case VisibilityScope.internal:
        return VisibilityScopeModel.internal;
      case VisibilityScope.external:
        return VisibilityScopeModel.external;
      case VisibilityScope.invalid:
        return VisibilityScopeModel.invalid;
    }
  }
}
