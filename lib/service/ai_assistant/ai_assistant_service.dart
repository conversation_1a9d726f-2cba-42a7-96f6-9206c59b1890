import 'package:makula_flutter/client/graphql/model/request_exception.dart';
import 'package:makula_flutter/service/oem/oem_service.dart';
import 'package:makula_flutter/service/service.dart';
import 'package:makula_flutter/service/user/user_service.dart';
import 'package:rxdart/rxdart.dart';

import 'model/model.dart';
import 'repository/ai_assistant_repository.dart';

export 'model/model.dart';

class AiAssitantException implements Exception {}

class NoDocumentsUploadedException implements AiAssitantException {}

class QuotaExceededException implements AiAssitantException {}

class UnauthorizedOem implements AiAssitantException {}

class UnauthorizedUser implements AiAssitantException {}

enum AiAssistantListType {
  my,
  shared;

  AiAssistantListTypeEntity get entity {
    switch (this) {
      case AiAssistantListType.my:
        return AiAssistantListTypeEntity.my;
      case AiAssistantListType.shared:
        return AiAssistantListTypeEntity.shared;
    }
  }
}

class AiAssistantListFilter {
  final AiAssistantListType type;

  AiAssistantListFilter({required this.type});
}

class AiAssistantService {
  final AiAssistantRepository _repository;
  final UserService _userService;
  final OemService _oemService;
  final Map<AiAssistantListFilter, int> _lastOffset = {};
  final Map<String, AiAssistantDetails> _assistants = {};
  final List<AiChatHistory> _chatHistory = [];

  String? _chatId;
  Map<String, BoxDocument> _fileMap = {};
  String? _historyId;

  final _aiAssistantUsageController = BehaviorSubject<bool>();
  final _aiChatHistoryController = BehaviorSubject<List<AiChatHistory>>();

  AiAssistantService({
    required AiAssistantRepository repisitory,
    required UserService userService,
    required OemService oemService,
  })  : _repository = repisitory,
        _userService = userService,
        _oemService = oemService;

  Future<AiAssistantDetails> fetchAiAssistantDetails(String id) async {
    final stretagy = ApiHandlingStretegy(
      request: () async {
        final entity = await _repository.getAiAssistantDetails(id: id);

        final model = AiAssistantDetails.fromRepositoryModel(entity);

        _assistants[id] = model;

        return model;
      },
    );
    return stretagy.execute();
  }

  Future<void> intializeAiAssistant({
    required String assistantId,
    String? chatHistoryId,
    String? chatId,
  }) async {
    _fileMap = {};
    _chatId = chatId;
    _historyId = chatHistoryId;

    final responses = await Future.wait([
      oemHasAiAssistantsAccess(),
      userHasAiAssistantsAccess(),
    ]);

    final oemHasAccess = responses[0];

    if (!oemHasAccess) throw UnauthorizedOem();

    final hasAiAccess = responses[1];

    if (!hasAiAccess) throw UnauthorizedUser();

    await intializeFileMap(assistantId);
  }

  Future<bool> userHasAiAssistantsAccess() => _userService.hasAiAccess();

  Future<void> intializeFileMap(String assistantId) async {
    final assistant =
        _assistants[assistantId] ?? await fetchAiAssistantDetails(assistantId);

    final List<AiAssistantDocument> documents = [
      ...assistant.externalDocuments,
      ...assistant.internalDocuments,
    ].where((e) => e.isIndexed).toList();

    if (documents.isEmpty && !assistant.isDemo) {
      throw NoDocumentsUploadedException();
    }

    final responses = await Future.wait([
      getFolderAccessToken(assistantId),
      aiAssistantUsageExceeded(assistantId),
    ]);

    final folderAccessToken = responses[0] as String;

    final usageExceeded = responses[1] as bool;

    if (usageExceeded) {
      throw QuotaExceededException();
    }

    final Map<String, String> idMap = Map.fromEntries(
      documents.map(
        (e) => MapEntry(e.externalStorageServiceDocumentId!, e.id),
      ),
    );

    final allFiles = await Future.wait(
      documents.map(
        (e) => _repository.getFileDetials(
          fileId: e.externalStorageServiceDocumentId ?? "",
          accessToken: folderAccessToken,
        ),
      ),
    );

    final files = allFiles.where((e) => e != null).map((e) => e!).toList();

    _fileMap = Map.fromEntries(
      files.map(
        (e) => MapEntry(idMap[e.id]!, BoxDocument(id: e.id, name: e.name)),
      ),
    );
  }

  Future<bool> aiAssistantUsageExceeded(String assistantId) async {
    var oemId = _assistants[assistantId]!.oem?.id;

    oemId ??= (await _oemService.getOem()).id;

    final entity = await _repository.getAiAssistantUsage(oemId);

    final usage = AiAssistantUsage.fromRepositoryModel(entity);

    final exceeded = usage.consumedQueries >= usage.allowedQueries;

    _aiAssistantUsageController.add(exceeded);

    return exceeded;
  }

  Stream<bool> get aiAssistantUsage => _aiAssistantUsageController.stream;

  bool doAllFilesExist(List<String> fileIds) {
    return fileIds.every((element) => _fileMap.containsKey(element));
  }

  Future<AiChatResponse> sendPrompt(
    AiChatPrompt prompt, {
    required bool isFirstMessage,
  }) async {
    try {
      if (isFirstMessage && _historyId == null) {
        _historyId = await _repository.createAiAssistantChat(prompt.id);
      }

      final response = await _repository.sendPrompt(
        prompt.toEntity(_chatId, _historyId),
      );

      _chatId = response.chatId;

      final resultIds =
          response.searchResults?.map((e) => e.documentId!).toList() ?? [];

      if (!doAllFilesExist(resultIds)) {
        await intializeFileMap(prompt.id);
      }

      if (_historyId != null) {
        final index = _chatHistory.indexWhere((chat) => chat.id == _historyId);
        if (index != -1 && index != 0) {
          final chatToMove = _chatHistory.removeAt(index);
          _chatHistory.insert(0, chatToMove);
          _aiChatHistoryController.add(_chatHistory);
        }
      }

      return AiChatResponse.fromRepositoryModel(response, _fileMap);
    } on InternetException {
      throw NoInternetException();
    } catch (e) {
      rethrow;
    } finally {
      await aiAssistantUsageExceeded(prompt.id);
    }
  }

  Future<bool> oemHasAiAssistantsAccess() async {
    final features = await _oemService.getPaidFeatures();

    return features.contains(PaidFeature.aiAssistants);
  }

  Stream<List<AiAssistant>> getAssistants(AiAssistantListType type) {
    return _repository.getAssistants(type.entity).map((event) {
      return event.map(AiAssistant.fromRepositoryModel).toList();
    });
  }

  Future<bool> fetchAssistants(
    AiAssistantListFilter filter, {
    bool forceRefresh = false,
    int limit = 100,
  }) async {
    final stretagy = ApiHandlingStretegy<bool>(
      request: () async {
        int lastOffset = _lastOffset[filter] ?? 0;

        List<String> recentlyAddedIds = [];

        if (forceRefresh) {
          lastOffset = 0;
        }

        recentlyAddedIds = filter.type == AiAssistantListType.my
            ? await _repository.fetchAiAssistants()
            : await _repository.fetchSharedAssistant(
                offset: lastOffset,
                limit: limit,
              );

        _lastOffset[filter] = lastOffset + limit;

        return filter.type == AiAssistantListType.my
            ? false
            : recentlyAddedIds.isEmpty;
      },
    );

    return await stretagy.execute();
  }

  Future<String> getFolderAccessToken(String assistantId) async {
    final strategy = ApiHandlingStretegy<String>(
      request: () async {
        final folderAccessToken = await _repository.getFolderAccessToken(
          assistantId,
        );

        return folderAccessToken;
      },
    );

    return strategy.execute();
  }

  Future<bool> fetchChatHistory({
    bool forceRefresh = false,
    int limit = 100,
  }) async {
    final strategy = ApiHandlingStretegy<bool>(
      request: () async {
        final chatHistory = await _repository.getChatHistory(
          limit: limit,
          offset: forceRefresh ? 0 : _chatHistory.length,
        );

        if (forceRefresh) _chatHistory.clear();

        _chatHistory.addAll(
          chatHistory.map(AiChatHistory.fromRepositoryModel).toList(),
        );

        _aiChatHistoryController.add(_chatHistory);

        return chatHistory.length == limit;
      },
    );

    return strategy.execute();
  }

  Stream<List<AiChatHistory>> getChatHistory() =>
      _aiChatHistoryController.stream;

  Future<AiChatHistoryDetails> fetchAiChatMessages(String historyId) async {
    final response = await _repository.getAiChatHistory(historyId);
    _chatId = response.chatId;

    return AiChatHistoryDetails.fromRepositoryModel(response);
  }

  Future<void> deleteChatHistory(AiChatHistory item) async {
    await _repository.deleteChatHistory(item.id);

    _chatHistory.removeWhere((e) => e.id == item.id);

    _aiChatHistoryController.add(_chatHistory);
  }

  Future<void> renameChatHistory(
      AiChatHistory serviceModel, String name) async {
    await _repository.renameChatHistory(serviceModel.id, name);
    final index = _chatHistory.indexWhere((e) => e.id == serviceModel.id);
    if (index != -1) {
      _chatHistory[index] = _chatHistory[index].copyWith(title: name);
    }
    _aiChatHistoryController.add(_chatHistory);
  }
}
