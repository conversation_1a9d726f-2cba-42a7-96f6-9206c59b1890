import 'dart:convert';

import 'package:http/http.dart' as http;

import 'package:makula_flutter/client/graphql/graph_client.dart';
import 'package:makula_flutter/client/persistence/drift/database.dart';
import 'package:makula_flutter/client/persistence/model/ai_assistant_table.dart';

import 'graphql/graphql.dart';
import 'model/model.dart';

export 'model/model.dart';

class AiAssistantRepository {
  final GraphClient _client;
  final AppDatabase _database;

  AiAssistantRepository({
    required GraphClient client,
    required AppDatabase database,
  })  : _client = client,
        _database = database;

  Future<CopilotReponseEntity> sendPrompt(CopilotPromptEntity prompt) {
    return _client.query(AiAssistantQueries.queryAiAssitant, {
      "input": prompt.toJson(),
    }).then((response) {
      final json = response["queryAiAssistant"];

      return CopilotReponseEntity.fromJson(json);
    });
  }

  Future<String> createAiAssistantChat(String assistantId) async {
    final response = await _client.mutation(
      AiAssistantMutations.createAiAssistantChat,
      {"aiAssistantId": assistantId},
    );

    final json = response["createAiAssistantChat"];

    return json["_id"];
  }

  Future<AiAssistantDetailsEntity> getAiAssistantDetails(
      {required String id}) async {
    final response = await _client.query(
      AiAssistantQueries.aiAssistantDetails,
      {"id": id},
    );

    final json = response["getAiAssistant"];

    return AiAssistantDetailsEntity.fromJson(json);
  }

  Future<AiAssistantUsageEntity> getAiAssistantUsage(String oemId) async {
    final response = await _client.query(
      AiAssistantQueries.aiAssistantUsage,
      {"oemId": oemId},
    );

    final json = response["getAiAssistantUsage"];

    return AiAssistantUsageEntity.fromJson(json);
  }

  Future<FileEntity?> getFileDetials({
    required String fileId,
    required String accessToken,
  }) async {
    final response = await http.get(
      Uri.parse(
        "https://api.box.com/2.0/files/$fileId?fields=id,item_collection,name",
      ),
      headers: {"authorization": "Bearer $accessToken"},
    );

    if (response.statusCode != 200) return null;

    final jsonData = jsonDecode(response.body);

    return FileEntity.fromJson(jsonData);
  }

  Stream<List<AiAssistantEntity>> getAssistants(
    AiAssistantListTypeEntity type,
  ) {
    return _database.getAiAssistants(type);
  }

  Future<List<String>> fetchAiAssistants({String searchQuery = ""}) async {
    final response = await _client.query(
      AiAssistantQueries.listAiAssistants,
      {
        "params": {"searchQuery": searchQuery}
      },
    );

    final jsonData = response["listAiAssistants"]["aiAssistants"];

    final assistants =
        (jsonData as List).map((e) => AiAssistantEntity.fromJson(e)).toList();

    await _database.addAiAssistants(
      assistants,
      AiAssistantListTypeEntity.my,
      prune: true,
    );

    return assistants.where((e) => e.id != null).map((e) => e.id!).toList();
  }

  Future<List<String>> fetchSharedAssistant({
    required int offset,
    String searchQuery = "",
    int limit = 100,
  }) async {
    final response = await _client.query(
      AiAssistantQueries.listSharedAiAssistants,
      {
        "params": {
          "limit": limit,
          "skip": offset,
          "where": {"searchQuery": searchQuery}
        }
      },
    );

    final jsonData = response["listSharedAiAssistants"]["assistants"];

    final assistants =
        (jsonData as List).map((e) => AiAssistantEntity.fromJson(e)).toList();

    await _database.addAiAssistants(
      assistants,
      AiAssistantListTypeEntity.shared,
      prune: offset == 0,
    );

    return assistants.where((e) => e.id != null).map((e) => e.id!).toList();
  }

  Future<String> getFolderAccessToken(String assistantId) {
    return _client.query(
      AiAssistantQueries.getFolderAccessToken,
      {"id": assistantId},
    ).then((response) {
      final json = response["getAiAssistant"];

      return json["boxAccessToken"];
    });
  }

  Future<List<AiChatHistoryEntity>> getChatHistory({
    required int limit,
    required int offset,
  }) async {
    final response = await _client.query(
      AiAssistantQueries.listAiAssistantChats,
      {
        "params": {"limit": limit, "skip": offset}
      },
    );

    final jsonData = response["listAiAssistantChats"]["aiAssistantChats"];

    return (jsonData as List)
        .map((e) => AiChatHistoryEntity.fromJson(e))
        .toList();
  }

  Future<AiChatHistoryDetailsEntity> getAiChatHistory(
    String historyId,
  ) async {
    final response = await _client.query(
      AiAssistantQueries.getAiChatHistory,
      {"id": historyId},
    );

    final jsonData = response["getAiAssistantChat"];

    return AiChatHistoryDetailsEntity.fromJson(jsonData);
  }

  Future<void> deleteChatHistory(String id) async {
    await _client.mutation(
      AiAssistantMutations.deleteAiChatHistory,
      {"aiAssistantChatId": id},
    );
  }

  Future<void> renameChatHistory(String id, String name) {
    return _client.mutation(
      AiAssistantMutations.renameAiChatHistory,
      {
        "input": {"aiAssistantChatId": id, "aiAssistantChatName": name}
      },
    );
  }
}

enum AiAssistantListTypeEntity { my, shared }

class FileEntity {
  final String id;
  final String name;

  FileEntity({
    required this.id,
    required this.name,
  });

  factory FileEntity.fromJson(Map<String, dynamic> json) {
    return FileEntity(
      id: json["id"],
      name: json["name"],
    );
  }
}
