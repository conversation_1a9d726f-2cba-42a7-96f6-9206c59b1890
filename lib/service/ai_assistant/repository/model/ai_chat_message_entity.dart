part of 'model.dart';

class AiChatHistoryDetailsEntity {
  final String? id;
  final String? chatId;
  final List<AiChatMessageEntity>? turns;

  const AiChatHistoryDetailsEntity({
    required this.id,
    required this.chatId,
    required this.turns,
  });

  factory AiChatHistoryDetailsEntity.fromJson(Map<String, dynamic> json) {
    return AiChatHistoryDetailsEntity(
      id: json['_id'],
      chatId: json['chatId'],
      turns: (json['turns'] as List<dynamic>?)
          ?.map((e) => AiChatMessageEntity.fromJson(e))
          .toList(),
    );
  }
}

class AiChatMessageEntity {
  final String? query;
  final String? answer;

  const AiChatMessageEntity({
    required this.query,
    required this.answer,
  });

  factory AiChatMessageEntity.fromJson(Map<String, dynamic> json) {
    return AiChatMessageEntity(
      query: json['query'],
      answer: json['answer'],
    );
  }
}
