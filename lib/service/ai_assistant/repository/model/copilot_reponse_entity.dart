part of 'model.dart';

class CopilotReponseEntity {
  final String? answer;
  final String? chatId;
  final List<SearchResultEntity>? searchResults;
  final String? turnId;

  CopilotReponseEntity({
    this.answer,
    this.chatId,
    this.searchResults,
    this.turnId,
  });

  factory CopilotReponseEntity.fromJson(Map<String, dynamic> json) =>
      CopilotReponseEntity(
        answer: json["answer"],
        chatId: json["chat_id"],
        searchResults: json["search_results"] == null
            ? []
            : List<SearchResultEntity>.from(json["search_results"]!.map(
                (x) => SearchResultEntity.fromJson(x),
              )),
        turnId: json["turn_id"],
      );
}

class SearchResultEntity {
  final String? documentId;
  final PartMetadataEntity? partMetadata;
  final double? score;
  final String? text;

  SearchResultEntity({
    this.documentId,
    this.partMetadata,
    this.score,
    this.text,
  });

  factory SearchResultEntity.fromJson(Map<String, dynamic> json) =>
      SearchResultEntity(
        documentId: json["document_id"],
        partMetadata: json["part_metadata"] == null
            ? null
            : PartMetadataEntity.fromJson(json["part_metadata"]),
        score: json["score"]?.toDouble(),
        text: json["text"],
      );
}

class PartMetadataEntity {
  final int? page;

  PartMetadataEntity({this.page});

  factory PartMetadataEntity.fromJson(Map<String, dynamic> json) =>
      PartMetadataEntity(
        page: json["page"],
      );
}
