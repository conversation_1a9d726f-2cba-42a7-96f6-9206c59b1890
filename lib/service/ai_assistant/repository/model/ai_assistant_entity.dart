part of 'model.dart';

class AiAssistantEntity {
  final String? id;
  final String? assistantName;
  final String? assistantType;
  final AiAssistantMachineEntity? machine;
  final AiAssistantOemEntity? oemDetails;
  final bool? isSharedAssistant;
  final String? templateId;
  final AiAssistantTemplateEntity? template;
  final String? oem;

  AiAssistantEntity({
    this.id,
    this.assistantName,
    this.assistantType,
    this.machine,
    this.oemDetails,
    this.isSharedAssistant,
    this.templateId,
    this.template,
    this.oem,
  });

  factory AiAssistantEntity.fromJson(Map<String, dynamic> json) =>
      AiAssistantEntity(
        id: json["_id"],
        assistantName: json["assistantName"],
        assistantType: json["assistantType"],
        machine: json["machine"] == null
            ? null
            : AiAssistantMachineEntity.fromJson(json["machine"]),
        oemDetails: json["oemDetails"] == null
            ? null
            : AiAssistantOemEntity.fromJson(json["oemDetails"]),
        isSharedAssistant: json["isSharedAssistant"],
        templateId: json["templateId"],
        template: json["template"] == null
            ? null
            : AiAssistantTemplateEntity.fromJson(json["template"]),
        oem: json["oem"],
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "assistantName": assistantName,
        "assistantType": assistantType,
        "oemDetails": oemDetails?.toJson(),
        "isSharedAssistant": isSharedAssistant,
        "machine": machine?.toJson(),
        "templateId": templateId,
        "template": template?.toJson(),
        "oem": oem,
      };
}

class AiAssistantTemplateEntity {
  final String? id;
  final String? title;

  const AiAssistantTemplateEntity({this.id, this.title});

  factory AiAssistantTemplateEntity.fromJson(Map<String, dynamic> json) =>
      AiAssistantTemplateEntity(id: json["_id"], title: json["title"]);

  Map<String, dynamic> toJson() => {"_id": id, "title": title};
}

class AiAssistantEntityConverter
    extends TypeConverter<AiAssistantEntity, String> {
  @override
  AiAssistantEntity fromSql(String fromDb) {
    final Map<String, dynamic> data =
        json.decode(fromDb) as Map<String, dynamic>;
    return AiAssistantEntity.fromJson(data);
  }

  @override
  String toSql(AiAssistantEntity value) {
    return json.encode(value.toJson());
  }
}
