part of 'model.dart';

class CopilotPromptEntity {
  final String? query;
  final String? chatId;
  final String? dbChatId;
  final String? id;

  CopilotPromptEntity({
    this.query,
    this.chatId,
    this.dbChatId,
    this.id,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['query'] = query;
    data['chatID'] = chatId;
    data['id'] = id;
    data['dbChatId'] = dbChatId;
    return data;
  }
}
