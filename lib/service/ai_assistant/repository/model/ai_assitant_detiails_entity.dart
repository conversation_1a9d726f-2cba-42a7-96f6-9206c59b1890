part of 'model.dart';

class AiAssistantDetailsEntity {
  final String? id;
  final String? assistantName;
  final AiAssistantMachineEntity? machine;
  final AiAssistantOemEntity? oemDetails;
  final DocumentsEntity? documents;
  final String? typename;
  final bool? isSharedAssistant;
  final String? templateId;
  final AiAssistantTemplateEntity? template;
  final String? oem;

  AiAssistantDetailsEntity({
    this.id,
    this.assistantName,
    this.machine,
    this.oemDetails,
    this.documents,
    this.typename,
    this.isSharedAssistant,
    this.templateId,
    this.template,
    this.oem,
  });

  factory AiAssistantDetailsEntity.fromJson(Map<String, dynamic> json) =>
      AiAssistantDetailsEntity(
        id: json["_id"],
        assistantName: json["assistantName"],
        machine: json["machine"] == null
            ? null
            : AiAssistantMachineEntity.fromJson(json["machine"]),
        oemDetails: json["oemDetails"] == null
            ? null
            : AiAssistantOemEntity.fromJson(json["oemDetails"]),
        documents: json["documents"] == null
            ? null
            : DocumentsEntity.fromJson(json["documents"]),
        isSharedAssistant: json["isSharedAssistant"],
        templateId: json["templateId"],
        template: json["template"] == null
            ? null
            : AiAssistantTemplateEntity.fromJson(json["template"]),
        oem: json["oem"],
      );
}

class DocumentsEntity {
  final List<TernalDocumentEntity>? externalDocuments;
  final List<TernalDocumentEntity>? internalDocuments;

  DocumentsEntity({
    this.externalDocuments,
    this.internalDocuments,
  });

  factory DocumentsEntity.fromJson(Map<String, dynamic> json) =>
      DocumentsEntity(
        externalDocuments: json["externalDocuments"] == null
            ? []
            : List<TernalDocumentEntity>.from(json["externalDocuments"]!
                .map((x) => TernalDocumentEntity.fromJson(x))),
        internalDocuments: json["internalDocuments"] == null
            ? []
            : List<TernalDocumentEntity>.from(json["internalDocuments"]!
                .map((x) => TernalDocumentEntity.fromJson(x))),
      );
}

class TernalDocumentEntity {
  final String? id;
  final String? externalStorageServiceDocumentId;
  final int? status;

  TernalDocumentEntity({
    this.id,
    this.externalStorageServiceDocumentId,
    this.status,
  });

  factory TernalDocumentEntity.fromJson(Map<String, dynamic> json) =>
      TernalDocumentEntity(
        id: json["_id"],
        externalStorageServiceDocumentId:
            json["externalStorageServiceDocumentID"],
        status: json["status"],
      );
}

class AiAssistantUsageEntity {
  final num? allowedQueries;
  final num? consumedQueries;

  AiAssistantUsageEntity({this.allowedQueries, this.consumedQueries});

  factory AiAssistantUsageEntity.fromJson(Map<String, dynamic> json) =>
      AiAssistantUsageEntity(
        allowedQueries: json["allowedQueries"],
        consumedQueries: json["consumedQueries"],
      );

  Map<String, dynamic> toJson() => {
        "allowedQueries": allowedQueries,
        "consumedQueries": consumedQueries,
      };
}

class AiAssistantMachineEntity {
  final String? id;
  final String? name;

  AiAssistantMachineEntity({this.id, this.name});

  factory AiAssistantMachineEntity.fromJson(Map<String, dynamic> json) =>
      AiAssistantMachineEntity(id: json["_id"], name: json["name"]);

  Map<String, dynamic> toJson() => {"_id": id, "name": name};
}

class AiAssistantOemEntity {
  final String? id;
  final String? name;
  final String? brandLogo;

  AiAssistantOemEntity({
    this.id,
    this.name,
    this.brandLogo,
  });

  factory AiAssistantOemEntity.fromJson(Map<String, dynamic> json) =>
      AiAssistantOemEntity(
        id: json["_id"],
        name: json["name"],
        brandLogo: json["brandLogo"],
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "name": name,
        "brandLogo": brandLogo,
      };
}
