part of 'model.dart';

class AiChatHistoryEntity {
  final String? id;
  final String? title;
  final String? createdAt;
  final bool? isSharedAssistantChat;
  final AiChatHistoryAssistantEntity? aiAssistant;

  const AiChatHistoryEntity({
    this.id,
    this.title,
    this.createdAt,
    this.isSharedAssistantChat,
    this.aiAssistant,
  });

  factory AiChatHistoryEntity.fromJson(Map<String, dynamic> json) {
    return AiChatHistoryEntity(
      id: json["_id"],
      title: json["title"],
      createdAt: json["createdAt"],
      isSharedAssistantChat: json["isSharedAssistantChat"],
      aiAssistant: AiChatHistoryAssistantEntity.fromJson(json["aiAssistant"]),
    );
  }
}

class AiChatHistoryAssistantEntity {
  final String? id;
  final String? name;

  const AiChatHistoryAssistantEntity({this.id, this.name});

  factory AiChatHistoryAssistantEntity.fromJson(Map<String, dynamic> json) {
    return AiChatHistoryAssistantEntity(
      id: json["_id"],
      name: json["assistantName"],
    );
  }
}
