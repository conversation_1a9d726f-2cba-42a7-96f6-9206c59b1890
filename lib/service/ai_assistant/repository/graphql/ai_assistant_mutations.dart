part of 'graphql.dart';

class AiAssistantMutations {
  const AiAssistantMutations._();

  static String createAiAssistantChat = r'''
    mutation createAiAssistantChat($aiAssistantId: ID!) {
      createAiAssistantChat(aiAssistantId: $aiAssistantId) {
        _id
      }
    }
  ''';

  static String deleteAiChatHistory = r'''
    mutation deleteAiAssistantChat($aiAssistantChatId: ID!) {
      deleteAiAssistantChat(aiAssistantChatId: $aiAssistantChatId)
    }
  ''';

  static String renameAiChatHistory = r'''
    mutation renameAiAssistantChat($input: InputRenameAiAssistantChat!) {
      renameAiAssistantChat(input: $input)
    }''';
}
