part of 'graphql.dart';

class AiAssistantQueries {
  const AiAssistantQueries._();

  static String queryAiAssitant = r'''
    query queryAiAssistant($input: InputQueryAiAssistant!) {
      queryAiAssistant(input: $input) {
        answer
        chat_id
        response_language
        search_results {
          document_id
          part_metadata {
            page
          }
          score
          text
        }
        turn_id
      }
    }
  ''';

  static String aiAssistantDetails = r'''
    query getAiAssistant($id: ID!) {
      getAiAssistant(id: $id) {
        _id
        assistantName
        assistantType
        isSharedAssistant
        machine {
          _id
          name
        }
        templateId
        template {
          _id
          title
        }
        oem
        oemDetails {
          _id
          name
          brandLogo
        }
        documents {
          externalDocuments {
            _id
            externalStorageServiceDocumentID
            status
          }
          internalDocuments {
            _id
            externalStorageServiceDocumentID
            status
          }
        }
      }
    }
  ''';

  static String aiAssistantUsage = r'''
    query getAiAssistantUsage($oemId: ID!) {
      getAiAssistantUsage(oemId: $oemId) {
        allowedQueries
        consumedQueries
      }
    }
  ''';

  static String listAiAssistants = r'''
    query listAiAssistants($limit: Int, $searchQuery: String) {
      listAiAssistants(limit: $limit, searchQuery: $searchQuery) {
        aiAssistants {
          _id
          assistantName
          assistantType
          description
          machine {
            _id
            serialNumber
            name
            image
          }
          isSharedAssistant
          templateId
          template {
            _id
            title
          }
          oemDetails {
            _id
            name
            brandLogo
          }
          oem
          createdBy
        }
        totalCount
      }
    }
  ''';

  static String listSharedAiAssistants = r'''
    query listSharedAiAssistants($params: InputQueryParams!) {
    listSharedAiAssistants(params: $params) {
      totalCount
      skip
      limit
      currentPage
      assistants {
        _id
        assistantName
        assistantType
        description
        machine {
          _id
          serialNumber
          name
          image
        }
        isSharedAssistant
        templateId
        template {
          _id
          title
        }
        oemDetails {
          _id
          name
          brandLogo
        }
        oem
        createdBy
      }
    }
  }''';

  static String getFolderAccessToken = r'''
    query getAiAssistant($id: ID!) {
      getAiAssistant(id: $id) {
        boxAccessToken
      }
    }
  ''';

  static String listAiAssistantChats = r'''
    query listAiAssistantChats($params: InputQueryParams!) {
      listAiAssistantChats(params: $params) {
        totalCount
        skip
        limit
        currentPage
        aiAssistantChats {
          _id
          createdAt
          title
          isSharedAssistantChat
          aiAssistant {
            _id
            assistantName
          }
        }
      }
    }
  ''';

  static String getAiChatHistory = r'''
    query getAiAssistantChat($id: ID!) {
      getAiAssistantChat(id: $id) {
        _id
        chatId
        turns {
          answer
          query
          __typename
        }
      }
    }
  ''';
}
