part of 'model.dart';

enum AiAssistantType {
  internalAssistant,
  externalAssistant,
  sharedAssistant,
  unknown;

  factory AiAssistantType.fromRepositoryModel({
    required String type,
    required bool isSharedAssistant,
  }) {
    if (isSharedAssistant) return AiAssistantType.sharedAssistant;

    switch (type) {
      case 'INTERNAL':
        return AiAssistantType.internalAssistant;
      case 'EXTERNAL':
        return AiAssistantType.externalAssistant;
      default:
        return AiAssistantType.unknown;
    }
  }
}

class AiAssistant {
  final String id;
  final String name;
  final AiAssistantAsset asset;
  final AiAssistantOem? oem;
  final AiAssistantType type;

  const AiAssistant({
    required this.id,
    required this.name,
    required this.asset,
    required this.type,
    this.oem,
  });

  bool get isShared => type == AiAssistantType.sharedAssistant;

  factory AiAssistant.fromRepositoryModel(AiAssistantEntity entity) {
    return AiAssistant(
      id: entity.id ?? "",
      name: entity.assistantName ?? "",
      type: AiAssistantType.fromRepositoryModel(
        type: entity.assistantType ?? "",
        isSharedAssistant: entity.isSharedAssistant ?? false,
      ),
      oem: entity.oemDetails == null
          ? null
          : AiAssistantOem.fromRepositoryModel(entity.oemDetails!),
      asset: entity.machine == null && entity.template == null
          ? AiAssistantAsset.empty
          : AiAssistantAsset.fromRepositoryModel(
              machine: entity.machine,
              template: entity.template,
            ),
    );
  }
}

class AiAssistantDetails extends AiAssistant {
  final List<AiAssistantDocument> internalDocuments;
  final List<AiAssistantDocument> externalDocuments;

  const AiAssistantDetails({
    required super.id,
    required super.name,
    required super.asset,
    required super.type,
    super.oem,
    this.internalDocuments = const [],
    this.externalDocuments = const [],
  });

  factory AiAssistantDetails.fromRepositoryModel(
    AiAssistantDetailsEntity entity,
  ) {
    return AiAssistantDetails(
      id: entity.id ?? "",
      name: entity.assistantName ?? "",
      oem: entity.oemDetails == null
          ? null
          : AiAssistantOem.fromRepositoryModel(entity.oemDetails!),
      asset: entity.machine == null && entity.template == null
          ? AiAssistantAsset.empty
          : AiAssistantAsset.fromRepositoryModel(
              machine: entity.machine,
              template: entity.template,
            ),
      internalDocuments: entity.documents?.internalDocuments
              ?.map((doc) => AiAssistantDocument.fromRepositoryModel(doc))
              .toList() ??
          const [],
      externalDocuments: entity.documents?.externalDocuments
              ?.map((doc) => AiAssistantDocument.fromRepositoryModel(doc))
              .toList() ??
          const [],
      type: AiAssistantType.fromRepositoryModel(
        type: entity.typename ?? "",
        isSharedAssistant: entity.isSharedAssistant ?? false,
      ),
    );
  }

  bool get isDemo => oem == null;
}

class AiAssistantDocument {
  final String id;
  final String? externalStorageServiceDocumentId;
  final bool isIndexed;

  const AiAssistantDocument({
    required this.id,
    this.externalStorageServiceDocumentId,
    required this.isIndexed,
  });

  factory AiAssistantDocument.fromRepositoryModel(TernalDocumentEntity entity) {
    return AiAssistantDocument(
      id: entity.id ?? "",
      externalStorageServiceDocumentId: entity.externalStorageServiceDocumentId,
      isIndexed: entity.status == 2002,
    );
  }
}

class AiAssistantAsset {
  final String id;
  final String name;
  final bool isTemplate;

  const AiAssistantAsset({
    required this.id,
    required this.name,
    required this.isTemplate,
  });

  factory AiAssistantAsset.fromRepositoryModel({
    AiAssistantMachineEntity? machine,
    AiAssistantTemplateEntity? template,
  }) {
    return AiAssistantAsset(
      id: template?.id ?? machine?.id ?? "",
      name: template?.title ?? machine?.name ?? "",
      isTemplate: template != null,
    );
  }

  bool get isEmpty => id == "";

  static const empty = AiAssistantAsset(id: "", name: "", isTemplate: false);
}

class AiAssistantOem {
  final String id;
  final String? brandLogo;

  const AiAssistantOem({
    required this.id,
    this.brandLogo,
  });

  factory AiAssistantOem.fromRepositoryModel(AiAssistantOemEntity entity) {
    return AiAssistantOem(
      id: entity.id ?? "",
      brandLogo: entity.brandLogo,
    );
  }
}
