part of 'model.dart';

class AiChatHistory {
  final String id;
  final String title;
  final String assistantId;
  final String assistantName;
  final DateTime? dateTime;

  const AiChatHistory({
    required this.id,
    required this.title,
    required this.assistantId,
    required this.assistantName,
    required this.dateTime,
  });

  factory AiChatHistory.fromRepositoryModel(AiChatHistoryEntity entity) {
    return AiChatHistory(
      id: entity.id!,
      title: entity.title ?? '',
      assistantId: entity.aiAssistant!.id!,
      assistantName: entity.aiAssistant?.name ?? '',
      dateTime: entity.createdAt == null
          ? null
          : DateTime.tryParse(entity.createdAt!),
    );
  }

  AiChatHistory copyWith({
    String? id,
    String? title,
    String? assistantId,
    String? assistantName,
    DateTime? dateTime,
  }) {
    return AiChatHistory(
      id: id ?? this.id,
      title: title ?? this.title,
      assistantId: assistantId ?? this.assistantId,
      assistantName: assistantName ?? this.assistantName,
      dateTime: dateTime ?? this.dateTime,
    );
  }
}

class AiChatHistoryDetails {
  final String id;
  final String chatId;
  final List<AiChatMessage> messages;

  AiChatHistoryDetails({
    required this.id,
    required this.chatId,
    required this.messages,
  });

  factory AiChatHistoryDetails.fromRepositoryModel(
    AiChatHistoryDetailsEntity entity,
  ) {
    return AiChatHistoryDetails(
      id: entity.id ?? "",
      chatId: entity.chatId ?? "",
      messages: entity.turns
              ?.map((e) => AiChatMessage.fromRepositoryModel(e))
              .toList() ??
          [],
    );
  }
}
