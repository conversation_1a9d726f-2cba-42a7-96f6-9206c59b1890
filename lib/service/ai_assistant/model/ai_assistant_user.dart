part of 'model.dart';

class AiAssistantUser {
  final String id;
  final String name;
  final bool hasAccess;
  final int totalQuota;
  final int remainingQuota;

  const AiAssistantUser({
    required this.id,
    required this.name,
    required this.hasAccess,
    required this.totalQuota,
    required this.remainingQuota,
  });

  factory AiAssistantUser.fromUser(User user) {
    int totalQuota = user.aiAssistantConfiguration?.allowedQueries ?? 0;
    int consumedQuota = user.aiAssistantConfiguration?.consumedQueries ?? 0;

    return AiAssistantUser(
      id: user.id,
      name: user.name,
      hasAccess: user.hasAiAccess ?? false,
      totalQuota: totalQuota,
      remainingQuota: totalQuota - consumedQuota,
    );
  }

  static const empty = AiAssistantUser(
    id: "",
    name: "",
    hasAccess: false,
    totalQuota: 0,
    remainingQuota: 0,
  );
}
