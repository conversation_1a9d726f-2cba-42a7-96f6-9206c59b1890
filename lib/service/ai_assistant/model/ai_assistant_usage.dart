part of 'model.dart';

class AiAssistantUsage {
  final int allowedQueries;
  final int consumedQueries;

  AiAssistantUsage({
    required this.allowedQueries,
    required this.consumedQueries,
  });

  factory AiAssistantUsage.fromRepositoryModel(AiAssistantUsageEntity entity) {
    return AiAssistantUsage(
      allowedQueries: entity.allowedQueries?.toInt() ?? 0,
      consumedQueries: entity.consumedQueries?.toInt() ?? 0,
    );
  }
}
