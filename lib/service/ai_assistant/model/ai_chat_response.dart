part of 'model.dart';

class AiChatResponse {
  final String message;
  final List<Referenece> references;

  const AiChatResponse({
    required this.message,
    this.references = const [],
  });

  factory AiChatResponse.fromRepositoryModel(
    CopilotReponseEntity entity,
    Map<String, BoxDocument> fileMap,
  ) {
    return AiChatResponse(
      message: entity.answer ?? "",
      references: fileMap.isEmpty
          ? []
          : entity.searchResults
                  ?.map((e) => Referenece.fromRepositoryModel(e, fileMap))
                  .toList() ??
              [],
    );
  }

  factory AiChatResponse.fromChatHistoryModel(String answer) {
    return AiChatResponse(
      message: answer,
      references: [],
    );
  }
}

class Referenece {
  final String id;
  final String title;
  final String text;
  final int page;

  const Referenece({
    required this.id,
    required this.title,
    required this.text,
    required this.page,
  });

  factory Referenece.fromRepositoryModel(
    SearchResultEntity entity,
    Map<String, BoxDocument> fileMap,
  ) {
    return Referenece(
      id: fileMap[entity.documentId]!.id,
      title: fileMap[entity.documentId]?.name ?? "undefined",
      page: int.tryParse(entity.partMetadata?.page?.toString() ?? "") ?? 1,
      text: entity.text ?? "",
    );
  }
}

class BoxDocument {
  final String id;
  final String name;

  const BoxDocument({
    required this.id,
    required this.name,
  });
}
