part of 'model.dart';

class AiChatMessage {
  final String query;
  final AiChatResponse response;

  const AiChatMessage({
    required this.query,
    required this.response,
  });

  factory AiChatMessage.fromRepositoryModel(AiChatMessageEntity entity) {
    return AiChatMessage(
      query: entity.query ?? "",
      response: AiChatResponse.fromChatHistoryModel(entity.answer ?? ""),
    );
  }
}
