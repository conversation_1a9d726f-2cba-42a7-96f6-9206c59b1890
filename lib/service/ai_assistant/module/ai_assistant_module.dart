part of 'module.dart';

class AiAssistantModule {
  final GraphqlModule _graphqlModule;
  final OemModule _oemModule;
  final UserModule _userModule;
  final PersistenceModule _persistenceModule;

  AiAssistantModule({
    required GraphqlModule graphqlModule,
    required OemModule oemModule,
    required UserModule userModule,
    required PersistenceModule persistenceModule,
  })  : _graphqlModule = graphqlModule,
        _oemModule = oemModule,
        _userModule = userModule,
        _persistenceModule = persistenceModule;

  AiAssistantRepository provideAiAssistantRepository() {
    if (!store.containsKey(AiAssistantRepository)) {
      register<AiAssistantRepository>(
        () => AiAssistantRepository(
          client: _graphqlModule.providesGraphqlClient(),
          database: _persistenceModule.providesAppDatabaseInstance(),
        ),
      );
    }
    return get<AiAssistantRepository>()!;
  }

  AiAssistantService provideAiAssistantService() {
    if (!store.containsKey(AiAssistantService)) {
      final repository = provideAiAssistantRepository();
      register<AiAssistantService>(
        () => AiAssistantService(
          repisitory: repository,
          oemService: _oemModule.providesOemService(),
          userService: _userModule.providesUserService(),
        ),
      );
    }
    return get<AiAssistantService>()!;
  }
}
