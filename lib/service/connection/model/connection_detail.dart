part of "model.dart";

class ConnectionDetail extends Equatable {
  final String id;
  final String displayId;
  final String name;
  final String type;
  final List<ConnectionTeam> teams;
  final String? description;
  final ConnectionAddress address;
  final bool isOffline;
  final DateTime? syncDate;

  const ConnectionDetail({
    required this.id,
    required this.displayId,
    required this.name,
    required this.type,
    required this.address,
    this.isOffline = false,
    this.teams = const [],
    this.description,
    this.syncDate,
  });

  factory ConnectionDetail.fromEntity(ConnectionEntity entity) {
    return ConnectionDetail(
      id: entity.id ?? "",
      displayId: entity.facilityId ?? "",
      name: entity.name ?? "",
      type: entity.type ?? "",
      address: entity.address == null
          ? ConnectionAddress.empty
          : ConnectionAddress.fromRepositoryModel(entity.address!),
      teams: entity.teams?.map(ConnectionTeam.fromEntity).toList() ?? [],
      description: entity.description,
      isOffline: entity.isOffline ?? false,
      syncDate: entity.syncDate,
    );
  }

  static const empty = ConnectionDetail(
    id: "",
    displayId: "",
    name: "",
    type: "",
    address: ConnectionAddress.empty,
  );

  bool get isEmpty => this == empty;

  bool get isNotEmpty => !isEmpty;

  bool get hasDescription => description != null && description!.isNotEmpty;

  @override
  List<Object?> get props => [id, name, type, address, teams, description];
}

class ConnectionTeam extends Equatable {
  final String id;
  final String name;
  final String? color;

  const ConnectionTeam({
    required this.id,
    required this.name,
    required this.color,
  });

  factory ConnectionTeam.fromEntity(ConnectionTeamEntity entity) {
    return ConnectionTeam(
      id: entity.id ?? "",
      name: entity.name ?? "",
      color: entity.teamColor,
    );
  }

  @override
  List<Object?> get props => [id, name, color];
}

class ConnectionAddress extends Equatable {
  final String? address;
  final num? longitude;
  final num? latitude;
  final String? pinCode;

  const ConnectionAddress({
    this.address,
    this.longitude,
    this.latitude,
    this.pinCode,
  });

  factory ConnectionAddress.fromRepositoryModel(
    ConnectionAddressEntity entity,
  ) {
    return ConnectionAddress(
      address: entity.address ?? "",
      longitude: entity.longitude ?? 0.0,
      latitude: entity.latitude ?? 0.0,
      pinCode: entity.pinCode ?? "",
    );
  }

  static const empty = ConnectionAddress(
    address: "",
    longitude: 0.0,
    latitude: 0.0,
    pinCode: "",
  );

  bool get isEmpty => this == empty;

  @override
  List<Object?> get props => [address, longitude, latitude, pinCode];
}
