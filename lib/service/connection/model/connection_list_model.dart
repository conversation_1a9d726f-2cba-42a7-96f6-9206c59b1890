part of 'model.dart';

class ConnectionListModel extends Equatable {
  final String id;
  final String name;
  final String? facilityId;
  final String? address;

  const ConnectionListModel({
    required this.id,
    required this.name,
    this.facilityId,
    this.address,
  });

  factory ConnectionListModel.fromRepositoryModel(ConnectionEntity entity) {
    return ConnectionListModel(
      id: entity.id ?? '',
      name: entity.name ?? '',
      facilityId: entity.facilityId,
      address: entity.address?.address,
    );
  }

  @override
  List<Object?> get props => [id, name, facilityId];
}

class PaginatedConnectionList extends Equatable {
  final List<ConnectionListModel> connections;
  final int totalCount;

  const PaginatedConnectionList({
    required this.connections,
    required this.totalCount,
  });

  factory PaginatedConnectionList.fromRepositoryModel(
    ConnectionListEntity entity,
  ) =>
      PaginatedConnectionList(
        connections: entity.items
                ?.map((e) => ConnectionListModel.fromRepositoryModel(e))
                .toList() ??
            [],
        totalCount: entity.totalCount ?? 0,
      );

  @override
  List<Object?> get props => [connections, totalCount];
}

class ConnectionPageInfo extends Equatable {
  final int limit;
  final int skip;
  final String? searchQuery;

  const ConnectionPageInfo({
    this.limit = 100,
    required this.skip,
    this.searchQuery,
  });

  @override
  List<Object?> get props => [limit, skip, searchQuery];
}
