part of 'model.dart';

class ConnectionAsset extends Equatable {
  final String id;
  final String? typeId;
  final String name;
  final String serialNumber;
  final String? image;
  final int subAssetCount;
  final ConnectionAsset? parent;
  final ConnectionAssetFacility facility;
  final ConnectionAssetOem? oem;

  const ConnectionAsset({
    required this.id,
    required this.name,
    required this.serialNumber,
    required this.facility,
    required this.image,
    required this.subAssetCount,
    required this.parent,
    required this.typeId,
    required this.oem,
  });

  factory ConnectionAsset.fromAssetServiceModel(Asset asset) {
    return ConnectionAsset(
      id: asset.id,
      name: asset.name,
      serialNumber: asset.serialNumber,
      image: asset.image,
      subAssetCount: asset.subAssetCount,
      parent: asset.parent != null
          ? ConnectionAsset.fromAssetServiceModel(asset.parent!)
          : null,
      facility: ConnectionAssetFacility.fromAssetServiceModel(asset.facility),
      typeId: asset.typeId,
      oem: asset.oem != null
          ? ConnectionAssetOem.fromAssetServiceModel(asset.oem!)
          : null,
    );
  }

  @override
  List<Object?> get props => [id, name, serialNumber, oem];
}

class ConnectionAssetOem extends Equatable {
  final String id;
  final String name;
  final String? logo;
  final String? brandLogo;

  const ConnectionAssetOem({
    required this.id,
    required this.name,
    this.logo,
    this.brandLogo,
  });

  factory ConnectionAssetOem.fromAssetServiceModel(AssetOem assetOem) {
    return ConnectionAssetOem(
      id: assetOem.id,
      name: assetOem.name,
      logo: assetOem.logo,
      brandLogo: assetOem.brandLogo,
    );
  }

  @override
  List<Object?> get props => [id, name, logo, brandLogo];
}

class ConnectionAssetFacility extends Equatable {
  final String id;
  final String name;

  const ConnectionAssetFacility({
    required this.id,
    required this.name,
  });

  factory ConnectionAssetFacility.fromAssetServiceModel(
      AssetFacility assetFacility) {
    return ConnectionAssetFacility(
      id: assetFacility.id,
      name: assetFacility.name,
    );
  }

  static const empty = ConnectionAssetFacility(
    id: "",
    name: "",
  );

  bool get isEmpty => this == empty;

  @override
  List<Object?> get props => [id, name];
}

class ConnectionAssetPageInfo extends Equatable {
  final int limit;
  final int skip;
  final String? searchQuery;

  const ConnectionAssetPageInfo({
    this.limit = 100,
    required this.skip,
    this.searchQuery,
  });

  AssetPageInfo toAssetServiceModel() {
    return AssetPageInfo(
      limit: limit,
      skip: skip,
      searchQuery: searchQuery,
    );
  }

  @override
  List<Object?> get props => [limit, skip, searchQuery];
}

class PaginatedConnectionAssetList extends Equatable {
  final List<ConnectionAsset> assets;
  final int totalCount;

  const PaginatedConnectionAssetList({
    required this.assets,
    required this.totalCount,
  });

  factory PaginatedConnectionAssetList.fromAssetServiceModel(
    PaginatedAssetList assetList,
  ) =>
      PaginatedConnectionAssetList(
        assets: assetList.assets
            .map((e) => ConnectionAsset.fromAssetServiceModel(e))
            .toList(),
        totalCount: assetList.totalCount,
      );

  @override
  List<Object?> get props => [assets, totalCount];
}
