export 'model/model.dart';
export 'module/module.dart';

import 'package:makula_flutter/service/analytics/analytics_service.dart';
import 'package:makula_flutter/service/custom_fields/custom_fields_service.dart';
import 'package:makula_flutter/service/oem/model/model.dart';
import 'package:makula_flutter/service/service.dart';
import 'package:makula_flutter/service/assets/assets_service.dart';
import 'package:rxdart/rxdart.dart';

import 'model/model.dart';
import 'repository/repository.dart';

class ConnectionService {
  final ConnectionRepository repository;
  final AssetsService assetsService;
  final CustomFieldsService customFieldsService;

  final _assets = <String, List<ConnectionAsset>>{};

  final _assetsController =
      BehaviorSubject<Map<String, List<ConnectionAsset>>>();

  ConnectionService({
    required this.repository,
    required this.assetsService,
    required this.customFieldsService,
  });

  void dispose() {
    _assetsController.close();
  }

  Future<ConnectionDetail> getConnectionDetail(String id) async {
    final stretagy = ApiHandlingStretegy(
      request: () async {
        final response = await repository.getConnectionDetail(id);
        return ConnectionDetail.fromEntity(response);
      },
    );

    return stretagy.execute();
  }

  Stream<List<CustomField>> getCustomFields(String connectionId) =>
      customFieldsService.streamCustomFields(connectionId);

  Future<List<CustomField>> fetchCustomFields(
    String connectionId,
  ) async {
    final data = await customFieldsService.getCustomFields(
      id: connectionId,
      type: CustomFieldType.connections,
    );

    return data;
  }

  Future<void> updateCustomField({
    required String connectionId,
    required List<CustomFieldRequest> fields,
  }) async {
    final stretagy = ApiHandlingStretegy(
      request: () async {
        await repository.updateCustomField(
          connectionId: connectionId,
          fields: fields.map((e) => e.toRepositoryModel()).toList(),
        );

        await fetchCustomFields(connectionId);
      },
    );

    await stretagy.execute();
  }

  Future<PaginatedConnectionAssetList> fetchAssets(
    ConnectionAssetPageInfo pageInfo, {
    String? facilityId,
    String? assetTypeId,
  }) async {
    final stretagy = ApiHandlingStretegy(
      request: () async {
        final assets = await assetsService.fetchAssets(
          pageInfo.toAssetServiceModel(),
          facilityId: facilityId,
          assetTypeId: assetTypeId,
        );

        return PaginatedConnectionAssetList.fromAssetServiceModel(assets);
      },
    );

    return stretagy.execute();
  }

  Stream<List<ConnectionAsset>> getAssets(String connectionId) =>
      _assetsController.map((assets) => assets[connectionId] ?? []);

  Future<bool> fetchConnectionAssets(
    String connectionId, {
    bool forceRefresh = false,
    int limit = 100,
    String? assetTypeId,
  }) {
    final stretagy = ApiHandlingStretegy(
      request: () async {
        int lastOffset = _assets[connectionId]?.length ?? 0;

        final pageInfo = ConnectionAssetPageInfo(
          limit: limit,
          skip: forceRefresh ? 0 : lastOffset,
        );

        if (forceRefresh) lastOffset = 0;

        final assetList = await fetchAssets(
          pageInfo,
          facilityId: connectionId,
          assetTypeId: assetTypeId,
        );

        _assets[connectionId] = forceRefresh
            ? assetList.assets
            : [...?_assets[connectionId], ...assetList.assets];

        _assetsController.add(_assets);

        return assetList.assets.length == limit;
      },
    );

    return stretagy.execute();
  }

  Future<void> downloadAllAssetsOffline(String connectionId) async {
    final stretagy = ApiHandlingStretegy(
      request: () async {
        final assetsToDownload = await assetsService
            .getAllAssetsToDownloadForConnection(connectionId: connectionId);

        int progress = 0;

        for (final id in assetsToDownload) {
          await assetsService.saveAssetOffline(id, subAssets: false);
          progress++;

          if (progress % 4 == 0) {
            await Future.delayed(const Duration(milliseconds: 500));
          }
        }

        await repository.updateConnectionLastAssetSync(
          connectionId,
          DateTime.now(),
        );

        AnalyticsService.instance.sendEvent(
          "Connection Assets Downloaded Offline",
          {
            "connectionId": connectionId,
            "assetsCount": assetsToDownload.length,
          },
        );
      },
    );

    await stretagy.execute();
  }

  Future<PaginatedConnectionList> fetchConnections(
    ConnectionPageInfo pageInfo,
  ) async {
    final stretagy = ApiHandlingStretegy(
      request: () async {
        final connections = await repository.fetchConnections(
          limit: pageInfo.limit,
          skip: pageInfo.skip,
          searchQuery: pageInfo.searchQuery,
        );

        return PaginatedConnectionList.fromRepositoryModel(connections);
      },
    );

    return stretagy.execute();
  }

  Future<List<String>> getAllConnectionsWithOfflineData() async {
    final stretagy = ApiHandlingStretegy(
      request: () async {
        final connections = await repository.getAllOfflineConnections();
        return connections.map((connection) => connection.id).toList();
      },
    );

    return stretagy.execute();
  }
}
