part of 'repository.dart';

class ConnectionRepository {
  final GraphClient client;
  final AppDatabase _appDatabase;

  ConnectionRepository({
    required this.client,
    required AppDatabase appDatabase,
  }) : _appDatabase = appDatabase;

  Future<ConnectionEntity> getConnectionDetail(String connectionId) async {
    try {
      final response = await client.query(
        ConnectionQueries.getConnectionDetail,
        {'id': connectionId},
      );

      final connection =
          ConnectionEntity.fromJson(response['getOwnOemCustomerById']);

      _appDatabase.addOrUpdateConnection(connection);

      final lastSyncDate = await _appDatabase.getLastAssetSync(connectionId);

      return connection.setSyncDate(lastSyncDate);
    } on InternetException {
      final cachedConnection =
          await _appDatabase.getConnectionById(connectionId);

      if (cachedConnection != null) {
        return cachedConnection.setToOffline();
      }

      rethrow;
    }
  }

  Future<void> updateCustomField({
    required String connectionId,
    required List<CustomFieldRequestEntity> fields,
  }) async {
    await client.mutation(
      ConnectionMutations.updateOwnOemConnection,
      {
        "input": {
          "_id": connectionId,
          "customFields": fields.map((e) => e.toJson()).toList(),
        }
      },
    );
  }

  Future<void> updateConnectionLastAssetSync(
    String connectionId,
    DateTime lastAssetSync,
  ) async {
    await _appDatabase.updateConnectionLastAssetSync(
      connectionId,
      lastAssetSync,
    );
  }

  Future<ConnectionListEntity> fetchConnections({
    int limit = 100,
    int skip = 0,
    String? searchQuery,
  }) async {
    try {
      final response = await client.query(
        ConnectionQueries.getConnections,
        {
          "params": {
            "limit": limit,
            "skip": skip,
            "where": {
              "searchQuery": searchQuery ?? "",
            },
          },
        },
      );

      final json = response["listAllOwnOemCustomers"];
      final connectionList = ConnectionListEntity.fromJson(json);

      // Store connections in local database
      if (connectionList.items != null) {
        for (final connection in connectionList.items!) {
          await _appDatabase.addOrUpdateConnection(connection);
        }
      }

      return connectionList;
    } on InternetException {
      final offlineConnections = await _appDatabase.getConnectionsWithLastSync(
        searchQuery: searchQuery,
      );
      return ConnectionListEntity(
        items: offlineConnections,
        totalCount: offlineConnections.length,
        skip: 0,
        limit: offlineConnections.length,
        currentPage: 1,
      );
    }
  }

  Future<List<ConnectionTableData>> getAllOfflineConnections() async {
    return await _appDatabase.getAllOfflineConnections();
  }
}
