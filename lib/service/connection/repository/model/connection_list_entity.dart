part of 'model.dart';

class ConnectionListEntity {
  final List<ConnectionEntity>? items;
  final int? totalCount;
  final int? skip;
  final int? limit;
  final int? currentPage;

  const ConnectionListEntity({
    this.items,
    this.totalCount,
    this.skip,
    this.limit,
    this.currentPage,
  });

  factory ConnectionListEntity.fromJson(Map<String, dynamic> json) {
    return ConnectionListEntity(
      items: json['customers'] != null
          ? List<ConnectionEntity>.from(
              json['customers']!.map((x) => ConnectionEntity.fromJson(x)),
            )
          : null,
      totalCount: json['totalCount'],
      skip: json['skip'],
      limit: json['limit'],
      currentPage: json['currentPage'],
    );
  }

  ConnectionListEntity setToOffline(List<ConnectionEntity> offlineConnections) {
    return ConnectionListEntity(
      items: offlineConnections,
      totalCount: offlineConnections.length,
      skip: 0,
      limit: offlineConnections.length,
      currentPage: 1,
    );
  }
}
