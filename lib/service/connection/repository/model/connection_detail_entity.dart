part of 'model.dart';

class ConnectionEntity {
  final String? id;
  final String? facilityId;
  final String? name;
  final bool? isQrCodeEnabled;
  final String? qrCodeAccess;
  final String? linkedOrg;
  final bool? isMachineDocumentationEnabled;
  final bool? isPreventiveMaintenanceEventsEnabled;
  final String? generalAccessUrl;
  final int? totalMachines;
  final int? totalUsersWithAccess;
  final int? totalUsers;
  final String? createdBy;
  final DateTime? createdAt;
  final String? description;
  final String? type;
  final List<ConnectionTeamEntity>? teams;
  final ConnectionAddressEntity? address;
  final bool? isOffline;
  final DateTime? syncDate;

  ConnectionEntity({
    this.id,
    this.facilityId,
    this.name,
    this.isQrCodeEnabled,
    this.qrCodeAccess,
    this.linkedOrg,
    this.isMachineDocumentationEnabled,
    this.isPreventiveMaintenanceEventsEnabled,
    this.generalAccessUrl,
    this.totalMachines,
    this.totalUsersWithAccess,
    this.totalUsers,
    this.createdBy,
    this.createdAt,
    this.description,
    this.type,
    this.teams,
    this.address,
    this.isOffline,
    this.syncDate,
  });

  bool meetsSearchQuery(String? query) {
    if (query == null || query.isEmpty) return true;

    final lowerQuery = query.toLowerCase();
    return name?.toLowerCase().contains(lowerQuery) == true ||
        facilityId?.toLowerCase().contains(lowerQuery) == true;
  }

  factory ConnectionEntity.fromJson(Map<String, dynamic> json) {
    final address = json["customFields"] != null
        ? (json["customFields"] as List).firstWhere(
            (field) => field["fieldId"]?["fieldType"] == "address",
            orElse: () => null)
        : null;

    return ConnectionEntity(
      id: json["_id"],
      facilityId: json["facilityId"],
      name: json["name"],
      isQrCodeEnabled: json["isQRCodeEnabled"],
      qrCodeAccess: json["qrCodeAccess"],
      linkedOrg: json["linkedOrg"],
      isMachineDocumentationEnabled: json["isMachineDocumentationEnabled"],
      isPreventiveMaintenanceEventsEnabled:
          json["isPreventiveMaintenanceEventsEnabled"],
      generalAccessUrl: json["generalAccessUrl"],
      totalMachines: json["totalMachines"],
      totalUsersWithAccess: json["totalUsersWithAccess"],
      totalUsers: json["totalUsers"],
      createdBy: json["createdBy"],
      createdAt:
          json["createdAt"] == null ? null : DateTime.parse(json["createdAt"]),
      description: json["description"],
      type: json["type"],
      teams: json["teams"] == null
          ? []
          : List<ConnectionTeamEntity>.from(
              json["teams"]!.map((x) => ConnectionTeamEntity.fromJson(x)),
            ),
      address: address?['value'] is Map
          ? ConnectionAddressEntity.fromJson(address['value'])
          : json['address'] != null
              ? ConnectionAddressEntity.fromJson(json['address'])
              : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      "_id": id,
      "facilityId": facilityId,
      "name": name,
      "isQRCodeEnabled": isQrCodeEnabled,
      "qrCodeAccess": qrCodeAccess,
      "linkedOrg": linkedOrg,
      "isMachineDocumentationEnabled": isMachineDocumentationEnabled,
      "isPreventiveMaintenanceEventsEnabled":
          isPreventiveMaintenanceEventsEnabled,
      "generalAccessUrl": generalAccessUrl,
      "totalMachines": totalMachines,
      "totalUsersWithAccess": totalUsersWithAccess,
      "totalUsers": totalUsers,
      "createdBy": createdBy,
      "createdAt": createdAt?.toIso8601String(),
      "description": description,
      "type": type,
      "teams": teams?.map((team) => team.toJson()).toList(),
      "address": address?.toJson(),
    };
  }

  ConnectionEntity setSyncDate(DateTime? syncDate) {
    return ConnectionEntity(
      id: id,
      facilityId: facilityId,
      name: name,
      isQrCodeEnabled: isQrCodeEnabled,
      qrCodeAccess: qrCodeAccess,
      linkedOrg: linkedOrg,
      isMachineDocumentationEnabled: isMachineDocumentationEnabled,
      isPreventiveMaintenanceEventsEnabled:
          isPreventiveMaintenanceEventsEnabled,
      generalAccessUrl: generalAccessUrl,
      totalMachines: totalMachines,
      totalUsersWithAccess: totalUsersWithAccess,
      totalUsers: totalUsers,
      createdBy: createdBy,
      createdAt: createdAt,
      description: description,
      type: type,
      teams: teams,
      address: address,
      isOffline: isOffline,
      syncDate: syncDate,
    );
  }

  ConnectionEntity setToOffline() {
    return ConnectionEntity(
      id: id,
      facilityId: facilityId,
      name: name,
      isQrCodeEnabled: isQrCodeEnabled,
      qrCodeAccess: qrCodeAccess,
      linkedOrg: linkedOrg,
      isMachineDocumentationEnabled: isMachineDocumentationEnabled,
      isPreventiveMaintenanceEventsEnabled:
          isPreventiveMaintenanceEventsEnabled,
      generalAccessUrl: generalAccessUrl,
      totalMachines: totalMachines,
      totalUsersWithAccess: totalUsersWithAccess,
      totalUsers: totalUsers,
      createdBy: createdBy,
      createdAt: createdAt,
      description: description,
      type: type,
      teams: teams,
      address: address,
      isOffline: true,
    );
  }
}

class ConnectionTeamEntity {
  final String? id;
  final String? name;
  final String? teamColor;

  ConnectionTeamEntity({
    this.id,
    this.name,
    this.teamColor,
  });

  factory ConnectionTeamEntity.fromJson(Map<String, dynamic> json) =>
      ConnectionTeamEntity(
        id: json["_id"],
        name: json["name"],
        teamColor: json["teamColor"],
      );

  Map<String, dynamic> toJson() {
    return {
      "_id": id,
      "name": name,
      "teamColor": teamColor,
    };
  }
}

class ConnectionAddressEntity {
  final String? address;
  final num? longitude;
  final num? latitude;
  final String? pinCode;

  ConnectionAddressEntity({
    this.address,
    this.longitude,
    this.latitude,
    this.pinCode,
  });

  factory ConnectionAddressEntity.fromJson(Map<String, dynamic> json) =>
      ConnectionAddressEntity(
        address: json["address"],
        longitude: json["longitude"],
        latitude: json["latitude"],
        pinCode: json["pinCode"],
      );

  Map<String, dynamic> toJson() {
    return {
      "address": address,
      "longitude": longitude,
      "latitude": latitude,
      "pinCode": pinCode,
    };
  }
}

class ConnectionEntityConverter
    extends TypeConverter<ConnectionEntity, String> {
  @override
  ConnectionEntity fromSql(String fromDb) {
    final Map<String, dynamic> data =
        json.decode(fromDb) as Map<String, dynamic>;
    return ConnectionEntity.fromJson(data);
  }

  @override
  String toSql(ConnectionEntity value) {
    return json.encode(value.toJson());
  }
}
