part of 'graphql.dart';

class ConnectionQueries {
  static const String getConnectionDetail = r"""
    query getOwnOemCustomerById($id: ID!) {
      getOwnOemCustomerById(id: $id) {
        _id
        facilityId
        name
        isQRCodeEnabled
        qrCodeAccess
        linkedOrg
        isMachineDocumentationEnabled
        isPreventiveMaintenanceEventsEnabled
        generalAccessUrl
        totalMachines
        totalUsersWithAccess
        totalUsers
        createdBy
        createdAt
        description
        type
        customFields {
          _id
          fieldId {
            _id
            fieldType
            isAdditionalField
          }
          value
        }
        teams {
          _id
          name
          teamColor
          __typename
        }
        __typename
      }
    }
  """;

  static const String getConnections = r"""
    query listAllOwnOemCustomers($params: InputQueryParams) {
      listAllOwnOemCustomers(params: $params) {
        currentPage
        limit
        skip
        totalCount
        customers {
          _id
          name
          facilityId
          customFields {
            _id
            fieldId {
              _id
              fieldType
              isAdditionalField
            }
            value
          }
        }
      }
    }
  """;
}

class ConnectionMutations {
  const ConnectionMutations._();

  static String updateOwnOemConnection = r'''
    mutation updateOemCustomer($input: InputUpdateOemCustomer!) {
      updateOemCustomer(input: $input) {
        _id
      }
    }
  ''';
}
