part of 'module.dart';

class ConnectionModule {
  final GraphqlModule _graphqlModule;
  final PersistenceModule _persistenceModule;
  final AssetsModule _assetsModule;
  final CustomFieldsModule _customFieldsModule;

  ConnectionModule(
    this._graphqlModule,
    this._persistenceModule,
    this._assetsModule,
    this._customFieldsModule,
  );

  ConnectionRepository provideConnectionRepository() {
    if (!store.containsKey(ConnectionRepository)) {
      register<ConnectionRepository>(
        () => ConnectionRepository(
          client: _graphqlModule.providesGraphqlClient(),
          appDatabase: _persistenceModule.providesAppDatabaseInstance(),
        ),
      );
    }
    return get<ConnectionRepository>()!;
  }

  ConnectionService provideConnectionService() {
    if (!store.containsKey(ConnectionService)) {
      final repository = provideConnectionRepository();
      final assetsService = _assetsModule.provideAssetsService();
      final customFieldsService =
          _customFieldsModule.provideCustomFieldsService();
      register<ConnectionService>(
        () {
          return ConnectionService(
            repository: repository,
            assetsService: assetsService,
            customFieldsService: customFieldsService,
          );
        },
      );
    }
    return get<ConnectionService>()!;
  }
}
