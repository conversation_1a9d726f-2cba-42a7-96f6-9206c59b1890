import 'package:makula_flutter/client/graphql/graphql_module.dart';
import 'package:makula_flutter/client/persistence/persistence_module.dart';
import 'package:makula_flutter/core/dependency_provider.dart';
import 'package:makula_flutter/service/connection/connection_service.dart';
import 'package:makula_flutter/service/assets/module/module.dart';
import 'package:makula_flutter/service/custom_fields/module/module.dart';

import '../repository/repository.dart';

part 'connection_module.dart';
