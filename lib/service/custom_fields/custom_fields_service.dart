import 'package:makula_flutter/core/utils/utils.dart';
import 'package:rxdart/rxdart.dart';

import 'repository/repository.dart';
import 'model/model.dart';

export 'model/model.dart';

class CustomFieldsService {
  final CustomFieldsRepository _repository;

  CustomFieldsService({required CustomFieldsRepository repository})
      : _repository = repository;

  final _customFields = <String, List<CustomField>>{};

  final _customFieldsController =
      BehaviorSubject<Map<String, List<CustomField>>>();

  void dispose() {
    _customFieldsController.close();
  }

  Stream<List<CustomField>> streamCustomFields(String id) =>
      _customFieldsController.map((customFields) => customFields[id] ?? []);

  Future<List<CustomField>> getCustomFields({
    required String id,
    required CustomFieldType type,
  }) async {
    final fields = await _repository.getCustomFields(type.toRepositoryModel);

    var data = <CustomField>[];

    switch (type) {
      case CustomFieldType.connections:
        data =
            await _getConnectionCustomFields(connectionId: id, fields: fields);
        break;
      case CustomFieldType.assets:
        data = await _getAssetCustomFields(assetId: id, fields: fields);
        break;
      case CustomFieldType.parts:
        data = await _getPartCustomFields(partId: id, fields: fields);
        break;
      case CustomFieldType.workOrders:
        data = await _getWorkOrderCustomFields(workOrderId: id, fields: fields);
        break;
      case CustomFieldType.invalid:
        data = [];
        break;
    }

    final refinedData = data.where((field) => field.dataType.isValid).toList()
      ..sort((a, b) => a.order.compareTo(b.order));

    _customFields[id] = refinedData;
    _customFieldsController.add(_customFields);

    return refinedData;
  }

  Future<List<CustomField>> getSharedCustomFields({
    required String id,
    required CustomFieldType type,
  }) async {
    var data = <CustomField>[];

    switch (type) {
      case CustomFieldType.assets:
        data = await _getSharedAssetCustomFields(id);
        break;
      case CustomFieldType.parts:
        data = await _getSharedPartCustomFields(id);
        break;
      case CustomFieldType.workOrders:
        data = await _getRequestCustomFields(id);
        break;
      case CustomFieldType.connections:
      case CustomFieldType.invalid:
        data = [];
        break;
    }

    final refinedData = data.where((field) => field.dataType.isValid).toList()
      ..sort((a, b) => a.order.compareTo(b.order));

    final filteredData = refinedData
        .where((e) => e.isNotEmpty)
        .toList()
        .where((e) => e.showOnCustomerPortal)
        .toList();

    _customFields[id] = filteredData;
    _customFieldsController.add(_customFields);

    return filteredData;
  }

  Future<List<CustomField>> _getConnectionCustomFields({
    required String connectionId,
    required List<CustomFieldEntity> fields,
  }) async {
    final values =
        await _repository.getConnectionCustomFieldValues(connectionId);

    return fields
        .map(
          (field) => CustomField.fromRepositoryModel(
            entity: field,
            value: values
                .firstWhereOrNull((e) => e.fieldId!.id == field.id)
                ?.value,
          ),
        )
        .toList();
  }

  Future<List<CustomField>> _getAssetCustomFields({
    required String assetId,
    required List<CustomFieldEntity> fields,
  }) async {
    final values = await _repository.getAssetCustomFieldValues(assetId);

    return fields
        .map(
          (field) => CustomField.fromRepositoryModel(
            entity: field,
            value: values
                .firstWhereOrNull((e) => e.fieldId!.id == field.id)
                ?.value,
          ),
        )
        .toList();
  }

  Future<List<CustomField>> _getPartCustomFields({
    required String partId,
    required List<CustomFieldEntity> fields,
  }) async {
    final values = await _repository.getPartCustomFieldValues(partId);

    return fields
        .map(
          (field) => CustomField.fromRepositoryModel(
            entity: field,
            value: values
                .firstWhereOrNull((e) => e.fieldId!.id == field.id)
                ?.value,
          ),
        )
        .toList();
  }

  Future<List<CustomField>> _getWorkOrderCustomFields({
    required String workOrderId,
    required List<CustomFieldEntity> fields,
  }) async {
    final values = await _repository.getWorkOrderCustomFieldValues(workOrderId);

    return fields
        .map(
          (field) => CustomField.fromRepositoryModel(
            entity: field,
            value: values
                .firstWhereOrNull((e) => e.fieldId!.id == field.id)
                ?.value,
          ),
        )
        .toList();
  }

  Future<List<CustomField>> _getSharedAssetCustomFields(String assetId) async {
    final values = await _repository.getSharedAssetCustomFieldValues(assetId);

    return values
        .map(
          (valueEntity) => CustomField.fromRepositoryModel(
            entity: valueEntity.fieldId!,
            value: valueEntity.value,
          ),
        )
        .toList();
  }

  Future<List<CustomField>> _getSharedPartCustomFields(String partId) async {
    final values = await _repository.getSharedPartCustomFieldValues(partId);

    return values
        .map(
          (valueEntity) => CustomField.fromRepositoryModel(
            entity: valueEntity.fieldId!,
            value: valueEntity.value,
          ),
        )
        .toList();
  }

  Future<List<CustomField>> _getRequestCustomFields(String requestId) async {
    final values = await _repository.getRequestCustomFieldValues(requestId);

    return values
        .map(
          (valueEntity) => CustomField.fromRepositoryModel(
            entity: valueEntity.fieldId!,
            value: valueEntity.value,
          ),
        )
        .toList();
  }
}
