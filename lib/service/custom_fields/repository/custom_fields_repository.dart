part of 'repository.dart';

class CustomFieldsRepository {
  final GraphClient _client;
  final AppDatabase _appDatabase;

  const CustomFieldsRepository({
    required GraphClient client,
    required AppDatabase appDatabase,
  })  : _client = client,
        _appDatabase = appDatabase;

  Future<List<CustomFieldEntity>> getCustomFields(
    CustomFieldTypeEntity type,
  ) async {
    try {
      final response = await _client.query(
        CustomFieldsQueries.getCustomFields,
        {"type": type.name},
      );

      final jsonData = response["listOwnOemCustomFields"] as List? ?? [];

      final apiData =
          jsonData.map((e) => CustomFieldEntity.fromJson(e)).toList();

      await _appDatabase.saveCustomFields(apiData, type.name);

      return await _appDatabase.getCustomFields(type.name);
    } catch (_) {
      return await _appDatabase.getCustomFields(type.name);
    }
  }

  Future<List<CustomFieldValueEntity>> getConnectionCustomFieldValues(
    String connectionId,
  ) async {
    final type = CustomFieldTypeEntity.facilities.name;
    try {
      final response = await _client.query(
        CustomFieldsQueries.getConnectionCustomFieldValues,
        {"id": connectionId},
      );

      final jsonData =
          response["getOwnOemCustomerById"]["customFields"] as List? ?? [];

      final apiData =
          jsonData.map((e) => CustomFieldValueEntity.fromJson(e)).toList();

      await _appDatabase.saveCustomFieldValues(
        resourceId: connectionId,
        dbModel: apiData,
        type: type,
        isShared: false,
      );

      return await _appDatabase.getCustomFieldValues(
        resourceId: connectionId,
        type: type,
        isShared: false,
      );
    } catch (_) {
      return await _appDatabase.getCustomFieldValues(
        resourceId: connectionId,
        type: type,
        isShared: false,
      );
    }
  }

  Future<List<CustomFieldValueEntity>> getAssetCustomFieldValues(
    String assetId,
  ) async {
    final type = CustomFieldTypeEntity.machines.name;
    try {
      final response = await _client.query(
        CustomFieldsQueries.getAssetCustomFieldValues,
        {"id": assetId},
      );

      final jsonData =
          response["getOwnOemAssetById"]["customFields"] as List? ?? [];

      final apiData =
          jsonData.map((e) => CustomFieldValueEntity.fromJson(e)).toList();

      await _appDatabase.saveCustomFieldValues(
        resourceId: assetId,
        dbModel: apiData,
        type: type,
        isShared: false,
      );

      return await _appDatabase.getCustomFieldValues(
        resourceId: assetId,
        type: type,
        isShared: false,
      );
    } catch (_) {
      return await _appDatabase.getCustomFieldValues(
        resourceId: assetId,
        type: type,
        isShared: false,
      );
    }
  }

  Future<List<CustomFieldValueEntity>> getSharedAssetCustomFieldValues(
    String assetId,
  ) async {
    final type = CustomFieldTypeEntity.machines.name;
    try {
      final response = await _client.query(
        CustomFieldsQueries.getSharedAssetCustomFieldValues,
        {"id": assetId, "isSharedAsset": true},
      );

      final jsonData =
          response["getOwnOemAssetById"]["customFields"] as List? ?? [];

      final apiData =
          jsonData.map((e) => CustomFieldValueEntity.fromJson(e)).toList();

      await _appDatabase.saveCustomFieldValues(
        resourceId: assetId,
        dbModel: apiData,
        type: type,
        isShared: true,
      );

      return await _appDatabase.getCustomFieldValues(
        resourceId: assetId,
        type: type,
        isShared: true,
      );
    } catch (_) {
      return await _appDatabase.getCustomFieldValues(
        resourceId: assetId,
        type: type,
        isShared: true,
      );
    }
  }

  Future<List<CustomFieldValueEntity>> getPartCustomFieldValues(
    String partId,
  ) async {
    final type = CustomFieldTypeEntity.parts.name;
    try {
      final response = await _client.query(
        CustomFieldsQueries.getPartCustomFieldValues,
        {"id": partId},
      );

      final jsonData =
          response["getInventoryPart"]["customFields"] as List? ?? [];

      final apiData =
          jsonData.map((e) => CustomFieldValueEntity.fromJson(e)).toList();

      await _appDatabase.saveCustomFieldValues(
        resourceId: partId,
        dbModel: apiData,
        type: type,
        isShared: false,
      );

      return await _appDatabase.getCustomFieldValues(
        resourceId: partId,
        type: type,
        isShared: false,
      );
    } catch (_) {
      return await _appDatabase.getCustomFieldValues(
        resourceId: partId,
        type: type,
        isShared: false,
      );
    }
  }

  Future<List<CustomFieldValueEntity>> getSharedPartCustomFieldValues(
    String partId,
  ) async {
    final type = CustomFieldTypeEntity.parts.name;
    try {
      final response = await _client.query(
        CustomFieldsQueries.getSharedPartCustomFieldValues,
        {"id": partId},
      );

      final jsonData =
          response["getInventoryPart"]["customFields"] as List? ?? [];

      final apiData =
          jsonData.map((e) => CustomFieldValueEntity.fromJson(e)).toList();

      await _appDatabase.saveCustomFieldValues(
        resourceId: partId,
        dbModel: apiData,
        type: type,
        isShared: true,
      );

      return await _appDatabase.getCustomFieldValues(
        resourceId: partId,
        type: type,
        isShared: true,
      );
    } catch (_) {
      return await _appDatabase.getCustomFieldValues(
        resourceId: partId,
        type: type,
        isShared: true,
      );
    }
  }

  Future<List<CustomFieldValueEntity>> getWorkOrderCustomFieldValues(
    String workOrderId,
  ) async {
    final type = CustomFieldTypeEntity.tickets.name;
    try {
      final response = await _client.query(
        CustomFieldsQueries.getWorkOrderCustomFieldValues,
        {"id": workOrderId},
      );

      final jsonData =
          response["getOwnOemTicketById"]["customFields"] as List? ?? [];

      final apiData =
          jsonData.map((e) => CustomFieldValueEntity.fromJson(e)).toList();

      await _appDatabase.saveCustomFieldValues(
        resourceId: workOrderId,
        dbModel: apiData,
        type: type,
        isShared: false,
      );

      return await _appDatabase.getCustomFieldValues(
        resourceId: workOrderId,
        type: type,
        isShared: false,
      );
    } catch (_) {
      return await _appDatabase.getCustomFieldValues(
        resourceId: workOrderId,
        type: type,
        isShared: false,
      );
    }
  }

  Future<List<CustomFieldValueEntity>> getRequestCustomFieldValues(
    String requestId,
  ) async {
    final type = CustomFieldTypeEntity.tickets.name;
    try {
      final response = await _client.query(
        CustomFieldsQueries.getRequestCustomFieldValues,
        {"id": requestId},
      );

      final jsonData = response["getRequest"]["customFields"] as List? ?? [];

      final apiData =
          jsonData.map((e) => CustomFieldValueEntity.fromJson(e)).toList();

      await _appDatabase.saveCustomFieldValues(
        resourceId: requestId,
        dbModel: apiData,
        type: type,
        isShared: true,
      );

      return await _appDatabase.getCustomFieldValues(
        resourceId: requestId,
        type: type,
        isShared: true,
      );
    } catch (_) {
      return await _appDatabase.getCustomFieldValues(
        resourceId: requestId,
        type: type,
        isShared: true,
      );
    }
  }
}
