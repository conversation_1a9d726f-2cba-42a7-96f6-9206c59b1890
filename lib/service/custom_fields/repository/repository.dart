import 'package:makula_flutter/client/graphql/graph_client.dart';
import 'package:makula_flutter/client/persistence/drift/database.dart';
import 'package:makula_flutter/client/persistence/model/custom_field_values_table.dart';
import 'package:makula_flutter/client/persistence/model/custom_fields_table.dart';

import 'graphql/graphql.dart';
import 'model/model.dart';

export 'model/model.dart';

part 'custom_fields_repository.dart';
