part of 'graphql.dart';

class CustomFieldsQueries {
  static String getCustomFields = r'''
    query listOwnOemCustomFields($type: Types) {
      listOwnOemCustomFields(type: $type) {
        _id
        createdBy
        created_at
        description
        enabled
        fieldType
        isAdditionalField
        label
        order
        slug
        type
        updated_at
        visibilityScope
        oem {
          _id
          name
          __typename
        }
        options {
          _id
          color
          description
          value
          __typename
        }
        __typename
      }
    }
  ''';

  static String getConnectionCustomFieldValues = r'''
    query getOwnOemCustomerById($id: ID!) {
      getOwnOemCustomerById(id: $id) {
        customFields {
          _id
          fieldId {
            _id
          }
          value
        }
      }
    }
  ''';

  static String getAssetCustomFieldValues = r'''
    query getOwnOemAssetById($id: ID!) {
      getOwnOemAssetById(id: $id) {
        customFields {
          _id
          fieldId {
            _id
          }
          value
        }
      }
    }
  ''';

  static String getSharedAssetCustomFieldValues = r'''
    query getOwnOemAssetById($id: ID!, $isSharedAsset: Boolean) {
      getOwnOemAssetById(id: $id, isSharedAsset: $isSharedAsset) {
        customFields {
          _id
          fieldId {
            _id
            createdBy
            created_at
            description
            enabled
            fieldType
            isAdditionalField
            label
            order
            slug
            type
            updated_at
            visibilityScope
            oem {
              _id
              name
              __typename
            }
            options {
              _id
              color
              description
              value
              __typename
            }
            __typename
          }
          value
        }
      }
    }
  ''';

  static String getPartCustomFieldValues = r'''
    query getInventoryPart($id: ID!) {
      getInventoryPart(id: $id) {
        customFields {
          _id
          fieldId {
            _id
          }
          value
        }
      }
    }
  ''';

  static String getSharedPartCustomFieldValues = r'''
    query getInventoryPart($id: ID!) {
      getInventoryPart(id: $id) {
        customFields {
          _id
          fieldId {
            _id
            createdBy
            created_at
            description
            enabled
            fieldType
            isAdditionalField
            label
            order
            slug
            type
            updated_at
            visibilityScope
            oem {
              _id
              name
              __typename
            }
            options {
              _id
              color
              description
              value
              __typename
            }
            __typename
          }
          value
        }
      }
    }
  ''';

  static String getWorkOrderCustomFieldValues = r'''
    query getOwnOemTicketById($id: ID!) {
      getOwnOemTicketById(id: $id) {
        customFields {
          _id
          fieldId {
            _id
          }
          value
        }
      }
    }
  ''';

  static String getRequestCustomFieldValues = r'''
    query getRequest($id: ID!) {
      getRequest(requestId: $id) {
        customFields {
          _id
          fieldId {
            _id
            createdBy
            created_at
            description
            enabled
            fieldType
            isAdditionalField
            label
            order
            slug
            type
            updated_at
            visibilityScope
            oem {
              _id
              name
              __typename
            }
            options {
              _id
              color
              description
              value
              __typename
            }
            __typename
          }
          value
        }
      }
    }
  ''';
}
