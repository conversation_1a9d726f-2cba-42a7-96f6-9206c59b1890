part of 'model.dart';

class CustomFieldEntity {
  final String? id;
  final String? createdBy;
  final String? createdAt;
  final String? description;
  final bool? enabled;
  final String? fieldType;
  final bool? isAdditionalField;
  final String? label;
  final int? order;
  final String? slug;
  final String? type;
  final String? updatedAt;
  final String? visibilityScope;
  final CustomFieldOemEntity? oem;
  final List<CustomFieldOptionEntity>? options;

  const CustomFieldEntity({
    this.id,
    this.createdBy,
    this.createdAt,
    this.description,
    this.enabled,
    this.fieldType,
    this.isAdditionalField,
    this.label,
    this.order,
    this.slug,
    this.type,
    this.updatedAt,
    this.visibilityScope,
    this.oem,
    this.options,
  });

  factory CustomFieldEntity.fromJson(Map<String, dynamic> json) {
    return CustomFieldEntity(
      id: json["_id"],
      createdBy: json["createdBy"],
      createdAt: json["created_at"],
      description: json["description"],
      enabled: json["enabled"],
      fieldType: json["fieldType"],
      isAdditionalField: json["isAdditionalField"],
      label: json["label"],
      order: json["order"],
      slug: json["slug"],
      type: json["type"],
      updatedAt: json["updated_at"],
      visibilityScope: json["visibilityScope"],
      oem: json["oem"] == null
          ? null
          : CustomFieldOemEntity.fromJson(json["oem"]),
      options: json["options"] == null
          ? []
          : List<CustomFieldOptionEntity>.from(
              json["options"]!.map((x) => CustomFieldOptionEntity.fromJson(x)),
            ),
    );
  }

  Map<String, dynamic> toJson() => {
        "_id": id,
        "createdBy": createdBy,
        "created_at": createdAt,
        "description": description,
        "enabled": enabled,
        "fieldType": fieldType,
        "isAdditionalField": isAdditionalField,
        "label": label,
        "order": order,
        "slug": slug,
        "type": type,
        "updated_at": updatedAt,
        "visibilityScope": visibilityScope,
        "oem": oem?.toJson(),
        "options": options?.map((e) => e.toJson()).toList(),
      };
}

class CustomFieldOemEntity {
  final String? id;
  final String? name;

  const CustomFieldOemEntity({
    this.id,
    this.name,
  });

  factory CustomFieldOemEntity.fromJson(Map<String, dynamic> json) {
    return CustomFieldOemEntity(
      id: json["_id"],
      name: json["name"],
    );
  }

  Map<String, dynamic> toJson() => {"_id": id, "name": name};
}

class CustomFieldOptionEntity {
  final String? id;
  final String? color;
  final String? description;
  final String? value;

  const CustomFieldOptionEntity({
    this.id,
    this.color,
    this.description,
    this.value,
  });

  factory CustomFieldOptionEntity.fromJson(Map<String, dynamic> json) {
    return CustomFieldOptionEntity(
      id: json["_id"],
      color: json["color"],
      description: json["description"],
      value: json["value"],
    );
  }

  Map<String, dynamic> toJson() => {
        "_id": id,
        "color": color,
        "description": description,
        "value": value,
      };
}

class CustomFieldEntityConvertor
    extends TypeConverter<List<CustomFieldEntity>, String> {
  const CustomFieldEntityConvertor();

  @override
  List<CustomFieldEntity> fromSql(String fromDb) {
    final jsonList = json.decode(fromDb) as List<dynamic>;

    return jsonList.map((e) => CustomFieldEntity.fromJson(e)).toList();
  }

  @override
  String toSql(List<CustomFieldEntity> value) {
    return json.encode(value.map((e) => e.toJson()).toList());
  }
}
