part of 'model.dart';

class CustomFieldValueEntity {
  final String? id;
  final CustomFieldEntity? fieldId;
  final String? value;

  const CustomFieldValueEntity({
    this.id,
    this.fieldId,
    this.value,
  });

  factory CustomFieldValueEntity.fromJson(Map<String, dynamic> json) {
    return CustomFieldValueEntity(
      id: json["_id"],
      fieldId: json["fieldId"] != null
          ? CustomFieldEntity.fromJson(json["fieldId"])
          : null,
      value: json["value"]?.toString(),
    );
  }

  Map<String, dynamic> toJson() => {
        "_id": id,
        "fieldId": fieldId?.toJson(),
        "value": value,
      };
}

class CustomFieldValueEntityConvertor
    extends TypeConverter<List<CustomFieldValueEntity>, String> {
  const CustomFieldValueEntityConvertor();

  @override
  List<CustomFieldValueEntity> fromSql(String fromDb) {
    final jsonList = json.decode(fromDb) as List<dynamic>;

    return jsonList.map((e) => CustomFieldValueEntity.fromJson(e)).toList();
  }

  @override
  String toSql(List<CustomFieldValueEntity> value) {
    return json.encode(value.map((e) => e.toJson()).toList());
  }
}
