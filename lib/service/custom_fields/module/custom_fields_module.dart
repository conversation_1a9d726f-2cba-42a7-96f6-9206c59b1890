part of 'module.dart';

class CustomFieldsModule {
  final GraphqlModule _graphqlModule;
  final PersistenceModule _persistenceModule;

  const CustomFieldsModule({
    required GraphqlModule graphqlModule,
    required PersistenceModule persistenceModule,
  })  : _graphqlModule = graphqlModule,
        _persistenceModule = persistenceModule;

  CustomFieldsRepository provideCustomFieldsRepository() {
    if (!store.containsKey(CustomFieldsRepository)) {
      register<CustomFieldsRepository>(
        () => CustomFieldsRepository(
          client: _graphqlModule.providesGraphqlClient(),
          appDatabase: _persistenceModule.providesAppDatabaseInstance(),
        ),
      );
    }
    return get<CustomFieldsRepository>()!;
  }

  CustomFieldsService provideCustomFieldsService() {
    if (!store.containsKey(CustomFieldsService)) {
      final repository = provideCustomFieldsRepository();

      register<CustomFieldsService>(
        () => CustomFieldsService(repository: repository),
      );
    }
    return get<CustomFieldsService>()!;
  }
}
