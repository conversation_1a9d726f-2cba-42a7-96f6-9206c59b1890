part of 'model.dart';

class CustomField {
  final String id;
  final bool enabled;
  final CustomFieldDataType dataType;
  final CustomFieldType type;
  final String label;
  final int order;
  final CustomFieldOem? oem;
  final VisibilityScope visibilityScope;
  final List<CustomFieldOption> options;
  final bool isAdditionalField;
  final String? _value;
  final String? description;
  final String? slug;
  final String? createdBy;
  final String? createdAt;
  final String? updatedAt;

  const CustomField({
    String? value,
    required this.id,
    required this.enabled,
    required this.dataType,
    required this.type,
    required this.label,
    required this.order,
    this.oem,
    required this.visibilityScope,
    this.options = const [],
    this.isAdditionalField = false,
    this.description,
    this.slug,
    this.createdBy,
    this.createdAt,
    this.updatedAt,
  }) : _value = value;

  factory CustomField.fromRepositoryModel({
    required CustomFieldEntity entity,
    required String? value,
  }) {
    return CustomField(
      value: value,
      id: entity.id!,
      enabled: entity.enabled ?? false,
      dataType: CustomFieldDataType.fromRepositoryModel(entity.fieldType),
      type: CustomFieldType.fromRepositoryModel(entity.type),
      label: entity.label ?? '-',
      order: entity.order ?? 1,
      oem: CustomFieldOem.fromRepositoryModel(entity.oem!),
      options:
          entity.options?.map(CustomFieldOption.fromRepositoryModel).toList() ??
              [],
      isAdditionalField: entity.isAdditionalField ?? false,
      description: entity.description,
      slug: entity.slug,
      visibilityScope:
          VisibilityScope.fromRepositoryModel(entity.visibilityScope),
      createdBy: entity.createdBy,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    );
  }

  String? get color =>
      options.firstWhereOrNull((option) => option.value == value)?.color;

  String get value => isEmpty ? 'Empty' : _value!;

  bool get isEmpty => _value == null || _value.isEmpty;

  bool get isNotEmpty => !isEmpty;

  bool get showOnCustomerPortal => visibilityScope == VisibilityScope.external;
}

class CustomFieldOem {
  final String id;
  final String name;

  const CustomFieldOem({required this.id, required this.name});

  factory CustomFieldOem.fromRepositoryModel(CustomFieldOemEntity entity) {
    return CustomFieldOem(id: entity.id!, name: entity.name!);
  }
}

class CustomFieldOption {
  final String id;
  final String color;
  final String value;
  final String? description;

  const CustomFieldOption({
    required this.id,
    required this.color,
    required this.value,
    this.description,
  });

  factory CustomFieldOption.fromRepositoryModel(
    CustomFieldOptionEntity entity,
  ) {
    return CustomFieldOption(
      id: entity.id!,
      color: entity.color!,
      value: entity.value!,
      description: entity.description,
    );
  }
}

enum CustomFieldDataType {
  text,
  number,
  date,
  singleSelect,
  invalid;

  factory CustomFieldDataType.fromRepositoryModel(String? type) {
    switch (type) {
      case 'text':
        return CustomFieldDataType.text;
      case 'number':
        return CustomFieldDataType.number;
      case 'date':
        return CustomFieldDataType.date;
      case 'singleSelect':
        return CustomFieldDataType.singleSelect;
      default:
        return CustomFieldDataType.invalid;
    }
  }

  bool get isValid => this != CustomFieldDataType.invalid;
}

enum CustomFieldType {
  connections,
  assets,
  workOrders,
  parts,
  invalid;

  factory CustomFieldType.fromRepositoryModel(String? type) {
    switch (type) {
      case 'facilities':
        return CustomFieldType.connections;
      case 'machines':
        return CustomFieldType.assets;
      case 'tickets':
        return CustomFieldType.workOrders;
      case 'parts':
        return CustomFieldType.parts;
      default:
        return CustomFieldType.invalid;
    }
  }

  CustomFieldTypeEntity get toRepositoryModel {
    switch (this) {
      case CustomFieldType.connections:
        return CustomFieldTypeEntity.facilities;
      case CustomFieldType.assets:
        return CustomFieldTypeEntity.machines;
      case CustomFieldType.workOrders:
        return CustomFieldTypeEntity.tickets;
      case CustomFieldType.parts:
        return CustomFieldTypeEntity.parts;
      default:
        return CustomFieldTypeEntity.tickets;
    }
  }

  bool get isValid => this != CustomFieldType.invalid;
}

enum VisibilityScope {
  internal,
  external,
  invalid;

  factory VisibilityScope.fromRepositoryModel(String? visibilityScope) {
    switch (visibilityScope) {
      case 'internal':
        return VisibilityScope.internal;
      case 'external':
        return VisibilityScope.external;
      default:
        return VisibilityScope.invalid;
    }
  }

  bool get isValid => this != VisibilityScope.invalid;
}
