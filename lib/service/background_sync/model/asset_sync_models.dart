/// Progress tracking for asset sync operations
class AssetSyncProgress {
  final int totalAssets;
  final int syncedAssets;
  final String? currentAssetId;
  final String? currentAssetName;
  final DateTime startTime;

  const AssetSyncProgress({
    required this.totalAssets,
    required this.syncedAssets,
    this.currentAssetId,
    this.currentAssetName,
    required this.startTime,
  });

  /// Create initial progress state
  factory AssetSyncProgress.initial() {
    return AssetSyncProgress(
      totalAssets: 0,
      syncedAssets: 0,
      startTime: DateTime.now(),
    );
  }

  /// Calculate progress percentage (0.0 to 1.0)
  double get progressPercentage {
    if (totalAssets == 0) return 0.0;
    return syncedAssets / totalAssets;
  }

  /// Check if sync is completed
  bool get isCompleted => syncedAssets >= totalAssets;

  /// Get elapsed time since sync started
  Duration get elapsedTime => DateTime.now().difference(startTime);

  /// Estimate remaining time based on current progress
  Duration? get estimatedRemainingTime {
    if (syncedAssets == 0 || isCompleted) return null;

    final avgTimePerAsset = elapsedTime.inMilliseconds / syncedAssets;
    final remainingAssets = totalAssets - syncedAssets;

    return Duration(milliseconds: (avgTimePerAsset * remainingAssets).round());
  }

  @override
  String toString() {
    return 'AssetSyncProgress(totalAssets: $totalAssets, syncedAssets: $syncedAssets, currentAsset: $currentAssetName)';
  }

  AssetSyncProgress copyWith({
    int? totalAssets,
    int? syncedAssets,
    String? currentAssetId,
    String? currentAssetName,
    DateTime? startTime,
  }) {
    return AssetSyncProgress(
      totalAssets: totalAssets ?? this.totalAssets,
      syncedAssets: syncedAssets ?? this.syncedAssets,
      currentAssetId: currentAssetId ?? this.currentAssetId,
      currentAssetName: currentAssetName ?? this.currentAssetName,
      startTime: startTime ?? this.startTime,
    );
  }
}

/// Represents the current state of background sync operation
enum BackgroundSyncState {
  /// Service is idle and not syncing
  idle,

  /// Currently syncing assets
  syncing,

  /// Sync completed successfully
  completed,

  /// Sync failed due to an error
  failed,

  /// Device is offline, cannot sync
  offline,

  /// Sync was manually stopped
  stopped,
}

/// Represents an asset item for sync operations
class AssetSyncItem {
  final String assetId;
  final String assetName;
  final DateTime? lastSyncDate;

  const AssetSyncItem({
    required this.assetId,
    required this.assetName,
    this.lastSyncDate,
  });

  @override
  String toString() {
    return 'AssetSyncItem(id: $assetId, name: $assetName, lastSync: $lastSyncDate)';
  }
}
