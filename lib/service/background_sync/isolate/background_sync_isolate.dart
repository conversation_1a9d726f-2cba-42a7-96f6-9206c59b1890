import 'dart:async';
import 'dart:isolate';

import 'package:makula_flutter/core/utils/utils.dart';

/// Isolate worker for background asset sync operations
/// This class handles the heavy lifting of asset synchronization in a separate isolate
/// to prevent blocking the main UI thread
class BackgroundSyncIsolate {
  static const int _batchSize = 5; // Process assets in small batches
  static const Duration _batchDelay =
      Duration(milliseconds: 200); // Delay between batches

  /// Entry point for the isolate worker
  /// This method will be called when the isolate is spawned
  static void isolateEntry(SendPort sendPort) async {
    // Create a receive port for the isolate
    final receivePort = ReceivePort();

    // Send the receive port back to the main isolate
    sendPort.send(receivePort.sendPort);

    // Listen for messages from the main isolate
    await for (final message in receivePort) {
      if (message is BackgroundSyncRequest) {
        try {
          await _processSyncRequest(message, sendPort);
        } catch (error, stackTrace) {
          sendPort.send(BackgroundSyncError(
            error: error.toString(),
            stackTrace: stackTrace.toString(),
          ));
        }
      } else if (message == 'shutdown') {
        break;
      }
    }
  }

  /// Process the sync request in batches
  static Future<void> _processSyncRequest(
    BackgroundSyncRequest request,
    SendPort sendPort,
  ) async {
    console(
        "BackgroundSyncIsolate: Starting sync for ${request.assetIds.length} assets");

    final totalAssets = request.assetIds.length;
    int processedCount = 0;

    // Process assets in batches to avoid overwhelming the system
    for (int i = 0; i < totalAssets; i += _batchSize) {
      final batchEnd = (i + _batchSize).clamp(0, totalAssets);
      final batch = request.assetIds.sublist(i, batchEnd);

      // Send progress update
      sendPort.send(BackgroundSyncProgress(
        totalAssets: totalAssets,
        processedAssets: processedCount,
        currentBatchSize: batch.length,
        currentBatch: batch,
      ));

      // Process batch
      await _processBatch(batch, request, sendPort);
      processedCount += batch.length;

      // Add delay between batches to prevent overwhelming the system
      if (i + _batchSize < totalAssets) {
        await Future.delayed(_batchDelay);
      }
    }

    // Send completion signal
    sendPort.send(BackgroundSyncComplete(
      totalProcessed: processedCount,
      duration: DateTime.now().difference(request.startTime),
    ));

    console("BackgroundSyncIsolate: Sync completed for $processedCount assets");
  }

  /// Process a batch of assets
  static Future<void> _processBatch(
    List<String> assetIds,
    BackgroundSyncRequest request,
    SendPort sendPort,
  ) async {
    for (final assetId in assetIds) {
      try {
        console("BackgroundSyncIsolate: Processing asset $assetId");

        // Send individual asset progress
        sendPort.send(BackgroundSyncAssetProgress(
          assetId: assetId,
          status: AssetSyncStatus.syncing,
        ));

        // Note: We can't directly call service methods from isolate
        // Instead, we send commands back to main isolate to execute
        sendPort.send(BackgroundSyncCommand(
          type: SyncCommandType.syncAsset,
          assetId: assetId,
          includeSubAssets: true,
        ));

        // Wait for confirmation from main isolate
        // This will be handled by the main isolate communication pattern
      } catch (error) {
        console(
            "BackgroundSyncIsolate: Error processing asset $assetId: $error");

        sendPort.send(BackgroundSyncAssetProgress(
          assetId: assetId,
          status: AssetSyncStatus.failed,
          error: error.toString(),
        ));
      }
    }
  }
}

/// Request message sent to the isolate
class BackgroundSyncRequest {
  final List<String> assetIds;
  final List<String> connectionIds;
  final DateTime startTime;

  BackgroundSyncRequest({
    required this.assetIds,
    required this.connectionIds,
    required this.startTime,
  });
}

/// Progress update from the isolate
class BackgroundSyncProgress {
  final int totalAssets;
  final int processedAssets;
  final int currentBatchSize;
  final List<String> currentBatch;

  BackgroundSyncProgress({
    required this.totalAssets,
    required this.processedAssets,
    required this.currentBatchSize,
    required this.currentBatch,
  });

  double get percentComplete =>
      totalAssets > 0 ? (processedAssets / totalAssets) * 100 : 0;
}

/// Individual asset progress
class BackgroundSyncAssetProgress {
  final String assetId;
  final AssetSyncStatus status;
  final String? error;

  BackgroundSyncAssetProgress({
    required this.assetId,
    required this.status,
    this.error,
  });
}

/// Command sent from isolate to main thread
class BackgroundSyncCommand {
  final SyncCommandType type;
  final String? assetId;
  final String? connectionId;
  final bool? includeSubAssets;

  BackgroundSyncCommand({
    required this.type,
    this.assetId,
    this.connectionId,
    this.includeSubAssets,
  });
}

/// Completion message from the isolate
class BackgroundSyncComplete {
  final int totalProcessed;
  final Duration duration;

  BackgroundSyncComplete({
    required this.totalProcessed,
    required this.duration,
  });
}

/// Error message from the isolate
class BackgroundSyncError {
  final String error;
  final String stackTrace;

  BackgroundSyncError({
    required this.error,
    required this.stackTrace,
  });
}

/// Asset sync status
enum AssetSyncStatus {
  pending,
  syncing,
  completed,
  failed,
}

/// Command types for isolate communication
enum SyncCommandType {
  syncAsset,
  syncConnection,
  updateProgress,
}
