import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:makula_flutter/core/utils/utils.dart';

/// Performance optimization utilities for background processing
/// Uses Flutter's compute function to run heavy operations in isolates
class PerformanceOptimizer {
  /// Process a list of items in background using compute
  /// This runs the processing function in a separate isolate
  static Future<List<T>> processInBackground<T, U>(
    List<U> items,
    T Function(U) processor, {
    int batchSize = 5,
    Duration delayBetweenBatches = const Duration(milliseconds: 100),
  }) async {
    if (items.isEmpty) return [];

    // For small lists, process directly to avoid isolate overhead
    if (items.length <= batchSize) {
      return await compute(_processItems, ProcessingData(items, processor));
    }

    // For larger lists, process in batches using isolates
    final List<T> results = [];

    for (int i = 0; i < items.length; i += batchSize) {
      final batchEnd = (i + batchSize).clamp(0, items.length);
      final batch = items.sublist(i, batchEnd);

      console(
          "PerformanceOptimizer: Processing batch ${(i / batchSize).floor() + 1}/${((items.length / batchSize).ceil())}");

      // Process batch in isolate
      final List<T> batchResults =
          await compute(_processItems<T, U>, ProcessingData(batch, processor));
      results.addAll(batchResults);

      // Add delay between batches to prevent overwhelming the system
      if (i + batchSize < items.length) {
        await Future.delayed(delayBetweenBatches);
      }
    }

    return results;
  }

  /// Run a heavy computation in background with progress updates
  static Stream<ProgressUpdate<T>> processWithProgress<T, U>(
    List<U> items,
    T Function(U) processor, {
    int batchSize = 3,
    Duration delayBetweenBatches = const Duration(milliseconds: 150),
  }) async* {
    if (items.isEmpty) return;

    for (int i = 0; i < items.length; i += batchSize) {
      final batchEnd = (i + batchSize).clamp(0, items.length);
      final batch = items.sublist(i, batchEnd);

      // Yield progress before processing batch
      yield ProgressUpdate(
        completed: i,
        total: items.length,
        currentBatch: batch.length,
        isProcessing: true,
      );

      // Process batch in isolate
      final List<T> batchResults =
          await compute(_processItems<T, U>, ProcessingData(batch, processor));

      // Yield progress after processing batch
      yield ProgressUpdate(
        completed: i + batch.length,
        total: items.length,
        currentBatch: batch.length,
        results: batchResults,
        isProcessing: false,
      );

      // Add delay between batches
      if (i + batchSize < items.length) {
        await Future.delayed(delayBetweenBatches);
      }
    }
  }

  /// Debounce function calls to prevent excessive processing
  static Timer? debounce(
    Timer? timer,
    Duration duration,
    VoidCallback callback,
  ) {
    timer?.cancel();
    return Timer(duration, callback);
  }

  /// Throttle function calls to limit execution frequency
  static bool shouldThrottle(
    Map<String, DateTime> lastExecutionMap,
    String key,
    Duration throttleInterval,
  ) {
    final now = DateTime.now();
    final lastExecution = lastExecutionMap[key];

    if (lastExecution == null ||
        now.difference(lastExecution) >= throttleInterval) {
      lastExecutionMap[key] = now;
      return false; // Don't throttle
    }

    return true; // Throttle
  }
}

/// Data structure for passing processing parameters to isolate
class ProcessingData<T, U> {
  final List<U> items;
  final T Function(U) processor;

  ProcessingData(this.items, this.processor);
}

/// Progress update structure for background processing
class ProgressUpdate<T> {
  final int completed;
  final int total;
  final int currentBatch;
  final List<T>? results;
  final bool isProcessing;

  ProgressUpdate({
    required this.completed,
    required this.total,
    required this.currentBatch,
    this.results,
    required this.isProcessing,
  });

  double get percentComplete => total > 0 ? (completed / total) * 100 : 0;

  bool get isComplete => completed >= total;
}

/// Top-level function for isolate processing (must be top-level for compute)
List<T> _processItems<T, U>(ProcessingData<T, U> data) {
  try {
    return data.items.map(data.processor).toList();
  } catch (e) {
    // If processing fails, return empty list
    // Individual error handling should be done in the processor function
    return <T>[];
  }
}
