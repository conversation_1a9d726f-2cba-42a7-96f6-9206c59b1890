import 'dart:async';

import 'package:makula_flutter/client/persistence/model/connection_table.dart';
import 'package:makula_flutter/core/utils/utils.dart';
import 'package:makula_flutter/service/analytics/analytics_service.dart';
import 'package:makula_flutter/service/assets/assets_service.dart';
import 'package:makula_flutter/service/config/config_service.dart';
import 'package:makula_flutter/service/connectivity/connectivity_service.dart';
import 'package:makula_flutter/service/connection/connection_service.dart';
import 'package:makula_flutter/client/persistence/drift/database.dart';
import 'package:makula_flutter/client/persistence/model/asset_table.dart';
import 'package:makula_flutter/core/ui/components/alert.dart';
import 'package:makula_flutter/service/crashlytics/crashlytics_service.dart';
import 'package:rxdart/rxdart.dart';

import 'model/model.dart';

export 'model/model.dart';

/// Background service for syncing offline assets when the app launches
/// Uses optimized batch processing with microtask scheduling to avoid blocking the main UI thread
class BackgroundAssetSyncService {
  final AssetsService _assetsService;
  final ConnectivityService _connectivityService;
  final ConfigService _configService;
  final ConnectionService _connectionService;
  final AppDatabase _appDatabase;

  // Stream controllers for sync progress
  final _syncProgressController = BehaviorSubject<AssetSyncProgress>();
  final _syncStateController = BehaviorSubject<BackgroundSyncState>();

  // Track current sync operation
  bool _isInitialized = false;
  bool _isSyncing = false;

  // Progress tracking
  Timer? _progressUpdateTimer;

  BackgroundAssetSyncService({
    required AssetsService assetsService,
    required ConnectivityService connectivityService,
    required ConfigService configService,
    required ConnectionService connectionService,
    required AppDatabase appDatabase,
  })  : _assetsService = assetsService,
        _connectivityService = connectivityService,
        _configService = configService,
        _connectionService = connectionService,
        _appDatabase = appDatabase;

  Stream<AssetSyncProgress> get syncProgressStream =>
      _syncProgressController.stream;

  Stream<BackgroundSyncState> get syncStateStream =>
      _syncStateController.stream;

  AssetSyncProgress get currentProgress =>
      _syncProgressController.valueOrNull ?? AssetSyncProgress.initial();

  BackgroundSyncState get currentState =>
      _syncStateController.valueOrNull ?? BackgroundSyncState.idle;

  Future<void> initialize() async {
    if (_isInitialized) return;

    _isInitialized = true;

    _connectivityService.connectivityState.listen((state) {
      if (state == ConnectivityState.online && !_isSyncing) {
        startBackgroundSync();
      }
    });

    _syncStateController.add(BackgroundSyncState.idle);
  }

  Future<void> startBackgroundSync({bool showNotifications = false}) async {
    if (_isSyncing) {
      return;
    }

    final isBackgroudSyncEnabled =
        await _configService.isBackgroundSyncEnabled();

    if (!isBackgroudSyncEnabled) {
      _syncStateController.add(BackgroundSyncState.idle);
      return;
    }

    if (_connectivityService.isOffline) {
      _syncStateController.add(BackgroundSyncState.offline);
      return;
    }

    _isSyncing = true;
    _syncStateController.add(BackgroundSyncState.syncing);

    try {
      final offlineAssets = await _getOfflineAssetsOrderedByOldest();

      if (offlineAssets.isEmpty) {
        _syncStateController.add(BackgroundSyncState.completed);
        _isSyncing = false;
        return;
      }

      if (showNotifications) {
        Alert.showLoading(
            message: "Syncing ${offlineAssets.length} offline assets...");
      }

      // Initialize progress
      final initialProgress = AssetSyncProgress(
        totalAssets: offlineAssets.length,
        syncedAssets: 0,
        currentAssetId: null,
        startTime: DateTime.now(),
      );
      _syncProgressController.add(initialProgress);

      // Use optimized batch processing with microtask scheduling
      await _syncAssetsInOptimizedBatches(offlineAssets, showNotifications);
      await _syncConnectionDetailsOptimized(showNotifications);
      await _updateAllConnectionLastAssetSync();

      AnalyticsService.instance.sendEvent(
        "Background Asset Sync Completed",
        {
          "totalAssets": offlineAssets.length,
          "duration":
              DateTime.now().difference(initialProgress.startTime).inSeconds,
        },
      );

      if (showNotifications) {
        Alert.hideLoading();
        Alert.showSuccess(
            message: "Successfully synced ${offlineAssets.length} assets");
      }

      _syncStateController.add(BackgroundSyncState.completed);
      console(
          "BackgroundAssetSyncService: Background sync completed successfully");
    } catch (e, s) {
      CrashlyticsService.instance.logError(e, s);

      if (showNotifications) {
        Alert.hideLoading();
      }

      rethrow;
    } finally {
      _isSyncing = false;
    }
  }

  Future<List<AssetSyncItem>> _getOfflineAssetsOrderedByOldest() async {
    try {
      final assetDetails = await _appDatabase.getAllOfflineAssetsWithDetails();

      final query = _appDatabase.select(_appDatabase.assetTable);
      final assetTableResults = await query.get();

      final syncInfoMap = <String, DateTime?>{};
      for (final asset in assetTableResults) {
        if (asset.lastSync != null) {
          syncInfoMap[asset.id] = asset.lastSync!.oldestDate;
        }
      }

      final assetSyncItems = assetDetails
          .map((asset) => AssetSyncItem(
                assetId: asset.id!,
                assetName: asset.name ?? 'Unknown Asset',
                lastSyncDate: syncInfoMap[asset.id!],
              ))
          .toList();

      assetSyncItems.removeWhere((asset) => asset.lastSyncDate == null);
      assetSyncItems.sort((a, b) => a.lastSyncDate!.compareTo(b.lastSyncDate!));

      console("BackgroundAssetSyncService: Assets ordered by sync date:");
      for (final asset in assetSyncItems) {
        console(
            "  - ${asset.assetName} (${asset.assetId}): ${asset.lastSyncDate ?? 'Never synced'}");
      }

      return assetSyncItems;
    } catch (e, s) {
      CrashlyticsService.instance.logError(e, s);
      return [];
    }
  }

  /// Sync assets using optimized batch processing with compute for heavy operations
  /// This prevents UI blocking by using isolates for CPU-intensive work
  Future<void> _syncAssetsInOptimizedBatches(
    List<AssetSyncItem> assets,
    bool showNotifications,
  ) async {
    const int batchSize = 3; // Small batch size to prevent UI blocking
    const Duration batchDelay =
        Duration(milliseconds: 100); // Short delay between batches

    console(
        "BackgroundAssetSyncService: Starting optimized sync for ${assets.length} assets");

    for (int i = 0; i < assets.length; i += batchSize) {
      final batchEnd = (i + batchSize).clamp(0, assets.length);
      final batch = assets.sublist(i, batchEnd);

      console(
          "BackgroundAssetSyncService: Processing batch ${(i / batchSize).floor() + 1}/${((assets.length / batchSize).ceil())} with ${batch.length} assets");

      // Process batch with controlled concurrency
      await _processBatchWithComputeOptimization(batch, i, assets.length);

      // Check connectivity after each batch
      if (_connectivityService.isOffline) {
        console("BackgroundAssetSyncService: Device went offline during sync");
        _syncStateController.add(BackgroundSyncState.offline);
        break;
      }

      // Yield control to the Flutter scheduler to prevent UI blocking
      await Future.delayed(batchDelay);

      // Use microtask to ensure UI thread gets priority
      await Future.microtask(() async {
        // This microtask will be executed after any pending UI updates
      });
    }

    // Update final progress
    final finalProgress = AssetSyncProgress(
      totalAssets: assets.length,
      syncedAssets: assets.length,
      currentAssetId: null,
      startTime: currentProgress.startTime,
    );
    _syncProgressController.add(finalProgress);

    console(
        "BackgroundAssetSyncService: Optimized sync completed for ${assets.length} assets");
  }

  /// Process a batch with compute optimization for heavy operations
  Future<void> _processBatchWithComputeOptimization(
    List<AssetSyncItem> batch,
    int batchStartIndex,
    int totalAssets,
  ) async {
    // For small batches, process sequentially to avoid isolate overhead
    if (batch.length <= 2) {
      for (int j = 0; j < batch.length; j++) {
        final asset = batch[j];
        final assetIndex = batchStartIndex + j;
        await _processSingleAssetOptimized(asset, assetIndex, totalAssets);
      }
      return;
    }

    // For larger batches, use controlled parallel processing
    final List<Future<void>> batchFutures = [];

    for (int j = 0; j < batch.length; j++) {
      final asset = batch[j];
      final assetIndex = batchStartIndex + j;

      // Create a future for each asset with staggered execution
      final assetFuture = Future.delayed(
        Duration(
            milliseconds: j * 50), // Stagger requests to avoid overwhelming
        () => _processSingleAssetOptimized(asset, assetIndex, totalAssets),
      );
      batchFutures.add(assetFuture);
    }

    // Wait for all assets in the batch to complete
    await Future.wait(batchFutures);
  }

  /// Process a single asset with optimized error handling and progress updates
  Future<void> _processSingleAssetOptimized(
    AssetSyncItem asset,
    int assetIndex,
    int totalAssets,
  ) async {
    console(
        "BackgroundAssetSyncService: Syncing asset ${assetIndex + 1}/$totalAssets: ${asset.assetName}");

    // Update progress
    final progress = AssetSyncProgress(
      totalAssets: totalAssets,
      syncedAssets: assetIndex,
      currentAssetId: asset.assetId,
      currentAssetName: asset.assetName,
      startTime: currentProgress.startTime,
    );
    _syncProgressController.add(progress);

    try {
      // Execute the heavy asset sync operation
      // This is where the actual network and database operations happen
      await _assetsService.saveAssetOffline(
        asset.assetId,
        subAssets: true,
      );

      console(
          "BackgroundAssetSyncService: Successfully synced asset ${asset.assetName}");
    } catch (e, s) {
      CrashlyticsService.instance.logError(e, s);
      console(
          "BackgroundAssetSyncService: Failed to sync asset ${asset.assetId}: $e");

      // Continue with other assets even if one fails
      // This prevents a single asset failure from stopping the entire sync
    }
  }

  /// Sync connection details with optimized batch processing
  Future<void> _syncConnectionDetailsOptimized(bool showNotifications) async {
    try {
      final connectionIds =
          await _connectionService.getAllConnectionsWithOfflineData();

      if (connectionIds.isEmpty) {
        return;
      }

      console(
          "BackgroundAssetSyncService: Starting connection sync for ${connectionIds.length} connections");

      // Process connections in smaller batches to avoid overwhelming the system
      const int connectionBatchSize = 2;
      const Duration connectionBatchDelay = Duration(milliseconds: 200);

      for (int i = 0; i < connectionIds.length; i += connectionBatchSize) {
        final batchEnd =
            (i + connectionBatchSize).clamp(0, connectionIds.length);
        final batch = connectionIds.sublist(i, batchEnd);

        // Check connectivity before processing batch
        if (_connectivityService.isOffline) {
          console(
              "BackgroundAssetSyncService: Device went offline during connection sync");
          _syncStateController.add(BackgroundSyncState.offline);
          break;
        }

        // Process connection batch concurrently
        final connectionFutures = batch.map((connectionId) =>
            _processSingleConnection(connectionId,
                i + batch.indexOf(connectionId) + 1, connectionIds.length));

        await Future.wait(connectionFutures);

        // Yield control to UI thread between batches
        if (i + connectionBatchSize < connectionIds.length) {
          await Future.delayed(connectionBatchDelay);
          await Future.microtask(() {});
        }
      }

      AnalyticsService.instance.sendEvent(
        "Background Connection Sync Completed",
        {
          "totalConnections": connectionIds.length,
        },
      );

      console("BackgroundAssetSyncService: Connection details sync completed");
    } catch (e, s) {
      console(
          "BackgroundAssetSyncService: Failed to sync connection details: $e");
      CrashlyticsService.instance.logError(e, s);
    }
  }

  /// Process a single connection with error handling
  Future<void> _processSingleConnection(
    String connectionId,
    int connectionIndex,
    int totalConnections,
  ) async {
    try {
      console(
          "BackgroundAssetSyncService: Syncing connection $connectionIndex/$totalConnections: $connectionId");

      // Fetch fresh connection details to update local cache
      await _connectionService.getConnectionDetail(connectionId);
    } catch (e, s) {
      CrashlyticsService.instance.logError(e, s);
      console(
          "BackgroundAssetSyncService: Failed to sync connection $connectionId: $e");
    }
  }

  /// Stop any ongoing sync operation
  Future<void> stopSync() async {
    if (!_isSyncing) return;

    console("BackgroundAssetSyncService: Stopping sync operation");

    // Clean up any pending timers
    _progressUpdateTimer?.cancel();
    _progressUpdateTimer = null;

    _isSyncing = false;
    _syncStateController.add(BackgroundSyncState.stopped);
    Alert.hideLoading();
  }

  Future<void> _updateAllConnectionLastAssetSync() async {
    try {
      await _appDatabase.updateAllConnectionsLastAssetSync(DateTime.now());
    } catch (e, s) {
      CrashlyticsService.instance.logError(e, s);
    }
  }

  /// Dispose of resources
  void dispose() {
    console("BackgroundAssetSyncService: Disposing");

    // Cancel timers
    _progressUpdateTimer?.cancel();

    // Close stream controllers
    _syncProgressController.close();
    _syncStateController.close();
  }
}
