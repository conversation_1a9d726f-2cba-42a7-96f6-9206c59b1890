import 'dart:async';

import 'package:makula_flutter/core/ui/components/alert.dart';
import 'package:makula_flutter/service/background_sync/background_asset_sync_service.dart';

/// Notification service for background sync progress
/// Shows non-intrusive notifications to keep users informed about sync progress
class BackgroundSyncNotificationService {
  static BackgroundSyncNotificationService? _instance;

  static BackgroundSyncNotificationService get instance {
    _instance ??= BackgroundSyncNotificationService._();
    return _instance!;
  }

  BackgroundSyncNotificationService._();

  StreamSubscription<BackgroundSyncState>? _syncStateSubscription;
  StreamSubscription<AssetSyncProgress>? _syncProgressSubscription;
  bool _isShowingNotification = false;

  /// Initialize notifications for background sync service
  void initialize(BackgroundAssetSyncService syncService) {
    dispose(); // Clean up any existing subscriptions

    _syncStateSubscription =
        syncService.syncStateStream.listen(_handleSyncState);
    _syncProgressSubscription =
        syncService.syncProgressStream.listen(_handleSyncProgress);
  }

  /// Handle sync state changes
  void _handleSyncState(BackgroundSyncState state) {
    switch (state) {
      case BackgroundSyncState.idle:
        _hideNotificationIfShowing();
        break;

      case BackgroundSyncState.syncing:
        if (!_isShowingNotification) {
          _showSyncStartNotification();
        }
        break;

      case BackgroundSyncState.completed:
        _hideNotificationIfShowing();
        _showSyncCompletedNotification();
        break;

      case BackgroundSyncState.offline:
        _hideNotificationIfShowing();
        _showOfflineNotification();
        break;

      case BackgroundSyncState.stopped:
        _hideNotificationIfShowing();
        break;

      case BackgroundSyncState.failed:
        _hideNotificationIfShowing();
        _showSyncFailedNotification();
        break;
    }
  }

  /// Handle sync progress updates
  void _handleSyncProgress(AssetSyncProgress progress) {
    if (_isShowingNotification && progress.totalAssets > 0) {
      // Update progress notification periodically (not on every asset to avoid spam)
      if (progress.syncedAssets % 5 == 0 || progress.isCompleted) {
        _updateProgressNotification(progress);
      }
    }
  }

  /// Show initial sync start notification
  void _showSyncStartNotification() {
    _isShowingNotification = true;

    Alert.showInfo(message: "Syncing offline data in background...");
  }

  /// Update progress notification
  void _updateProgressNotification(AssetSyncProgress progress) {
    final percentComplete = (progress.progressPercentage * 100).toInt();
    final currentAsset = progress.currentAssetName ?? 'Unknown';

    // Only show progress updates for longer syncs to avoid notification spam
    if (progress.totalAssets > 10) {
      Alert.showInfo(
          message: "Syncing: $percentComplete% complete\n$currentAsset");
    }
  }

  /// Show sync completed notification
  void _showSyncCompletedNotification() {
    _isShowingNotification = false;

    Alert.showSuccess(message: "Background sync completed successfully!");
  }

  /// Show offline notification
  void _showOfflineNotification() {
    _isShowingNotification = false;

    Alert.showInfo(message: "Sync paused - device is offline");
  }

  /// Show sync failed notification
  void _showSyncFailedNotification() {
    _isShowingNotification = false;

    Alert.showError(
        errorMessage: "Background sync encountered issues. Will retry later.");
  }

  /// Hide notification if currently showing
  void _hideNotificationIfShowing() {
    if (_isShowingNotification) {
      Alert.hideLoading();
      _isShowingNotification = false;
    }
  }

  /// Dispose of subscriptions
  void dispose() {
    _syncStateSubscription?.cancel();
    _syncProgressSubscription?.cancel();
    _syncStateSubscription = null;
    _syncProgressSubscription = null;
    _hideNotificationIfShowing();
  }
}
