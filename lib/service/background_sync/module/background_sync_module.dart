import 'package:makula_flutter/service/background_sync/background_asset_sync_service.dart';
import 'package:makula_flutter/service/assets/assets_service.dart';
import 'package:makula_flutter/service/config/config_service.dart';
import 'package:makula_flutter/service/connectivity/connectivity_service.dart';
import 'package:makula_flutter/service/connection/connection_service.dart';
import 'package:makula_flutter/client/persistence/drift/database.dart';

class BackgroundSyncModule {
  final AssetsService _assetsService;
  final ConnectivityService _connectivityService;
  final ConfigService _configService;
  final ConnectionService _connectionService;
  final AppDatabase _appDatabase;

  BackgroundSyncModule({
    required AssetsService assetsService,
    required ConnectivityService connectivityService,
    required ConfigService configService,
    required ConnectionService connectionService,
    required AppDatabase appDatabase,
  })  : _assetsService = assetsService,
        _connectivityService = connectivityService,
        _configService = configService,
        _connectionService = connectionService,
        _appDatabase = appDatabase;

  BackgroundAssetSyncService providesBackgroundAssetSyncService() {
    return BackgroundAssetSyncService(
      assetsService: _assetsService,
      configService: _configService,
      connectivityService: _connectivityService,
      connectionService: _connectionService,
      appDatabase: _appDatabase,
    );
  }
}
