part of 'view.dart';

class CustomerRequestPartDetailsPage extends StatelessWidget {
  const CustomerRequestPartDetailsPage({super.key, required this.part});

  final CustomerRequestPartModel part;

  static Route route(CustomerRequestPartModel part) {
    return MaterialPageRoute<void>(
      builder: (_) => CustomerRequestPartDetailsPage(part: part),
    );
  }

  @override
  Widget build(BuildContext context) {
    return ViewModelProvider(
      create: () => CustomerRequestPartDetailsViewModel(
        workOrderService: get<FeatureModules>()!.workOrderService,
        part: part,
      )..getPartCustomFields(),
      child: const CustomerRequestPartDetailsView(),
    );
  }
}
