part of 'view.dart';

class CustomerRequestPartDetailsView extends StatelessWidget {
  const CustomerRequestPartDetailsView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.makulaBackgroundColors.accent,
      appBar: AppBar(
        title: Text(
          context.localization.partDetails,
          style: context.makulaTypography.headline.large.primary(context),
        ),
        backgroundColor: context.makulaBackgroundColors.primary,
        surfaceTintColor: context.makulaBackgroundColors.primary,
        leading: IconButton(
          icon: const MakulaIcon(MakulaIcons.back, size: 24),
          onPressed: Navigator.of(context).pop,
        ),
      ),
      body: ViewModelBuilder<CustomerRequestPartDetailsViewModel,
          CustomerRequestPartDetailsState>(
        builder: (context, state) {
          if (state.fetchPartCustomFieldsState.isLoading) {
            return const Center(child: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>());
          }
          return ListView(
            padding: EdgeInsets.symmetric(vertical: context.makulaPadding.xs),
            children: [
              DataListItem(
                title: context.localization.partName,
                subtitle: state.part.name,
              ),
              Divider(height: 1, color: context.makulaBorderColors.primary),
              DataListItem(
                title: context.localization.articleNumber,
                subtitle: state.part.articleNumber,
              ),
              Divider(height: 1, color: context.makulaBorderColors.primary),
              if (state.part.quantity > 0) ...[
                DataListItem(
                  title: context.localization.assignedQuantity,
                  subtitle: state.part.quantity.toString(),
                ),
                Divider(height: 1, color: context.makulaBorderColors.primary),
              ],
              SizedBox(height: context.makulaPadding.xs),
              DetailListItem(
                title: context.localization.description,
                icon: MakulaIcons.leftAlign,
                child: Container(
                  padding: EdgeInsetsDirectional.only(
                    bottom: context.makulaPadding.m,
                    start: context.makulaPadding.l,
                    end: context.makulaPadding.l,
                  ),
                  alignment: AlignmentDirectional.topStart,
                  child: Text(
                    state.part.description.isNotEmpty
                        ? state.part.description
                        : context.localization.noDescriptionAvailable,
                    style: context.makulaTypography.body.large.primary(context),
                  ),
                ),
              ),
              if (state.customFields.isNotEmpty) ...[
                Divider(height: 1, color: context.makulaBorderColors.primary),
                DetailListItem(
                  title: context.localization.customFields,
                  icon: MakulaIcons.customField,
                  child: MakulaCustomFields(customFields: state.customFields),
                ),
              ],
            ],
          );
        },
      ),
    );
  }
}
