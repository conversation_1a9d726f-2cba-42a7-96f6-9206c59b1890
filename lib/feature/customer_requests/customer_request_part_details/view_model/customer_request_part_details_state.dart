part of 'customer_request_part_details_view_model.dart';

typedef FetchPartCustomFieldsState
    = GeneralResponseState<List<CustomFieldModel>>;

class CustomerRequestPartDetailsState extends Equatable {
  final FetchPartCustomFieldsState fetchPartCustomFieldsState;
  final CustomerRequestPartModel part;

  const CustomerRequestPartDetailsState({
    required this.fetchPartCustomFieldsState,
    required this.part,
  });

  const CustomerRequestPartDetailsState.initial({
    this.fetchPartCustomFieldsState = const FetchPartCustomFieldsState(),
    required this.part,
  });

  CustomerRequestPartDetailsState copyWith({
    FetchPartCustomFieldsState? fetchPartCustomFieldsState,
  }) {
    return CustomerRequestPartDetailsState(
      fetchPartCustomFieldsState:
          fetchPartCustomFieldsState ?? this.fetchPartCustomFieldsState,
      part: part,
    );
  }

  List<CustomFieldModel> get customFields =>
      fetchPartCustomFieldsState.data ?? [];

  @override
  List<Object?> get props => [fetchPartCustomFieldsState, part];
}
