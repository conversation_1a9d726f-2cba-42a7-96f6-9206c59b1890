import 'package:equatable/equatable.dart';
import 'package:makula_flutter/core/core.dart';
import 'package:makula_flutter/core/utils/utils.dart';
import 'package:makula_flutter/feature/customer_requests/model/model.dart';
import 'package:makula_flutter/service/service.dart';
import 'package:makula_flutter/service/work_order/work_order_service.dart';

part 'customer_request_part_details_state.dart';

class CustomerRequestPartDetailsViewModel
    extends ObservableViewModel<CustomerRequestPartDetailsState> {
  CustomerRequestPartDetailsViewModel({
    required WorkOrderService workOrderService,
    required CustomerRequestPartModel part,
  })  : _workOrderService = workOrderService,
        super(CustomerRequestPartDetailsState.initial(part: part));

  final WorkOrderService _workOrderService;

  Future<void> getPartCustomFields() async {
    updateState(
      state.copyWith(
        fetchPartCustomFieldsState:
            state.fetchPartCustomFieldsState.toLoading(),
      ),
    );

    try {
      final fields =
          await _workOrderService.fetchSharedPartCustomFields(state.part.id);

      updateState(
        state.copyWith(
          fetchPartCustomFieldsState: state.fetchPartCustomFieldsState.toLoaded(
            data: fields
                .map((e) => CustomFieldModel.fromServiceModel(e))
                .toList(),
          ),
        ),
      );
    } on NoInternetException catch (_) {
      updateState(
        state.copyWith(
          fetchPartCustomFieldsState:
              state.fetchPartCustomFieldsState.toFailure(
            error: ExceptionMessage.noInternet,
          ),
        ),
      );
    } catch (error) {
      updateState(
        state.copyWith(
          fetchPartCustomFieldsState:
              state.fetchPartCustomFieldsState.toFailure(
            error: ExceptionMessage.general,
          ),
        ),
      );
    }
  }
}
