part of 'work_order_overview_view_model.dart';

typedef WorkOrderFetchState = GeneralResponseState<WorkOrderModel>;
typedef PaidFeaturesFetchState = GeneralResponseState<List<String>>;
typedef ProcedureTemplateFetchState
    = GeneralResponseState<List<ProcedureTemplateModel>>;
typedef CustomFieldsFetchState = GeneralResponseState<List<CustomFieldModel>>;
typedef FacilityCustomFieldsFetchState
    = GeneralResponseState<List<CustomFieldModel>>;
typedef AssetsCustomFieldsFetchState
    = GeneralResponseState<List<CustomFieldModel>>;
typedef PartsFetchState = GeneralResponseState<List<PartModel>>;
typedef WorkOrderStatusesFetchState
    = GeneralResponseState<List<WorkOrderStatusModel>>;
typedef ChangeWorkOrderStatusState = GeneralResponseState<void>;

class WorkOrderOverviewState extends Equatable {
  final String id;
  final WorkOrderFetchState workOrderFetchState;
  final PaidFeaturesFetchState paidFeaturesFetchState;
  final ProcedureTemplateFetchState procedureTemplateFetchState;
  final FacilityCustomFieldsFetchState facilityCustomFieldsFetchState;
  final AssetsCustomFieldsFetchState assetsCustomFieldsFetchState;
  final CustomFieldsFetchState customFieldsFetchState;
  final PartsFetchState assignedPartsFetchState;
  final PartsFetchState partsFetchState;
  final WorkOrderStatusesFetchState workOrderStatusesFetchState;
  final ChangeWorkOrderStatusState changeWorkOrderStatusState;
  final bool hideEmptyCustomFields;

  const WorkOrderOverviewState({
    required this.id,
    required this.workOrderFetchState,
    required this.paidFeaturesFetchState,
    required this.procedureTemplateFetchState,
    required this.facilityCustomFieldsFetchState,
    required this.assetsCustomFieldsFetchState,
    required this.customFieldsFetchState,
    required this.assignedPartsFetchState,
    required this.partsFetchState,
    required this.workOrderStatusesFetchState,
    required this.hideEmptyCustomFields,
    required this.changeWorkOrderStatusState,
  });

  const WorkOrderOverviewState.initial({
    required this.id,
    this.workOrderFetchState = const WorkOrderFetchState(),
    this.paidFeaturesFetchState = const PaidFeaturesFetchState(),
    this.procedureTemplateFetchState = const ProcedureTemplateFetchState(),
    this.facilityCustomFieldsFetchState =
        const FacilityCustomFieldsFetchState(),
    this.assetsCustomFieldsFetchState = const AssetsCustomFieldsFetchState(),
    this.customFieldsFetchState = const CustomFieldsFetchState(),
    this.assignedPartsFetchState = const PartsFetchState(),
    this.partsFetchState = const PartsFetchState(),
    this.workOrderStatusesFetchState = const WorkOrderStatusesFetchState(),
    this.changeWorkOrderStatusState = const ChangeWorkOrderStatusState(),
    this.hideEmptyCustomFields = true,
  });

  bool get proceduresEnabled =>
      paidFeaturesFetchState.data?.contains("procedures") ?? false;

  List<ProcedureTemplateModel> get procedureTemplates =>
      procedureTemplateFetchState.data ?? [];

  List<CustomFieldModel> get customFields =>
      (customFieldsFetchState.data ?? []);

  List<CustomFieldModel> get facilityCustomFields =>
      (facilityCustomFieldsFetchState.data ?? []);

  List<CustomFieldModel> get assetsCustomFields =>
      (assetsCustomFieldsFetchState.data ?? []);

  List<PartModel> get assignedParts => (assignedPartsFetchState.data ?? []);

  List<WorkOrderModel> get linkedWorkOrders =>
      (workOrderFetchState.data?.linkedWorkOrders ?? []);

  List<String> get alreadySelectedPartIds =>
      assignedParts.map((e) => e.id).toList();

  List<PartModel> get filteredParts =>
      parts.where((e) => !alreadySelectedPartIds.contains(e.id)).toList();

  List<PartModel> get parts => (partsFetchState.data ?? []);

  List<WorkOrderStatusModel> get workOrderStatuses =>
      workOrderStatusesFetchState.data ?? [];

  List<ProcedureModel> get procedures =>
      (workOrderFetchState.data?.procedures ?? []);

  bool get hasEmptyField =>
      (customFieldsFetchState.data ?? []).any((field) => field.value.isEmpty);

  bool get showEmptyCustomFields => !hideEmptyCustomFields;

  WorkOrderOverviewState copyWith({
    WorkOrderFetchState? workOrderFetchState,
    PaidFeaturesFetchState? paidFeaturesFetchState,
    ProcedureTemplateFetchState? procedureTemplateFetchState,
    FacilityCustomFieldsFetchState? facilityCustomFieldsFetchState,
    AssetsCustomFieldsFetchState? assetsCustomFieldsFetchState,
    CustomFieldsFetchState? customFieldsFetchState,
    PartsFetchState? assignedPartsFetchState,
    PartsFetchState? partsFetchState,
    WorkOrderStatusesFetchState? workOrderStatusesFetchState,
    bool? hideEmptyCustomFields,
    bool? isProcedureSheetOpen,
    ChangeWorkOrderStatusState? changeWorkOrderStatusState,
  }) {
    return WorkOrderOverviewState(
      id: id,
      workOrderFetchState: workOrderFetchState ?? this.workOrderFetchState,
      paidFeaturesFetchState:
          paidFeaturesFetchState ?? this.paidFeaturesFetchState,
      facilityCustomFieldsFetchState:
          facilityCustomFieldsFetchState ?? this.facilityCustomFieldsFetchState,
      assetsCustomFieldsFetchState:
          assetsCustomFieldsFetchState ?? this.assetsCustomFieldsFetchState,
      procedureTemplateFetchState:
          procedureTemplateFetchState ?? this.procedureTemplateFetchState,
      customFieldsFetchState:
          customFieldsFetchState ?? this.customFieldsFetchState,
      assignedPartsFetchState:
          assignedPartsFetchState ?? this.assignedPartsFetchState,
      workOrderStatusesFetchState:
          workOrderStatusesFetchState ?? this.workOrderStatusesFetchState,
      partsFetchState: partsFetchState ?? this.partsFetchState,
      hideEmptyCustomFields:
          hideEmptyCustomFields ?? this.hideEmptyCustomFields,
      changeWorkOrderStatusState:
          changeWorkOrderStatusState ?? this.changeWorkOrderStatusState,
    );
  }

  @override
  List<Object?> get props => [
        workOrderFetchState,
        paidFeaturesFetchState,
        procedureTemplateFetchState,
        assetsCustomFieldsFetchState,
        proceduresEnabled,
        facilityCustomFieldsFetchState,
        customFieldsFetchState,
        assignedPartsFetchState,
        workOrderStatusesFetchState,
        partsFetchState,
        hideEmptyCustomFields,
        changeWorkOrderStatusState,
      ];
}
