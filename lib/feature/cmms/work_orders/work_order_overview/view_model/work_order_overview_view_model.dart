import 'dart:async';

import 'package:makula_flutter/core/model/model.dart';
import 'package:makula_flutter/core/utils/utils.dart';
import 'package:makula_flutter/core/view_model/view_model.dart';
import 'package:makula_flutter/feature/cmms/work_orders/work_order_overview/procedure/model/procedure_model.dart';
import 'package:makula_flutter/feature/cmms/work_orders/work_order_overview/procedure/model/procedure_template_model.dart';
import 'package:makula_flutter/feature/cmms/work_orders/work_order_overview/model/model.dart';
import 'package:makula_flutter/feature/cmms/work_orders/work_orders.dart';
import 'package:makula_flutter/service/analytics/analytics_service.dart';
import 'package:makula_flutter/service/custom_fields/custom_fields_service.dart';
import 'package:makula_flutter/service/oem/oem_service.dart';
import 'package:makula_flutter/service/service.dart';
import 'package:makula_flutter/service/work_order/work_order_service.dart';
import 'package:equatable/equatable.dart';

part 'work_order_overview_state.dart';

class WorkOrderOverviewViewModel
    extends ObservableViewModel<WorkOrderOverviewState> {
  final WorkOrderService _workOrderService;

  WorkOrderOverviewViewModel({
    required String id,
    required WorkOrderService workOrderService,
  })  : _workOrderService = workOrderService,
        super(WorkOrderOverviewState.initial(id: id));

  StreamSubscription<WorkOrderModel>? _subscription;

  StreamSubscription<List<WorkOrderModel>>? _linkedWorkOrdersSubscription;

  StreamSubscription<List<PartModel>>? _partSubscription;

  StreamSubscription<List<CustomField>>? _assetsCustomFieldsSubscription;

  StreamSubscription<List<CustomField>>? _connectionCustomFieldsSubscription;

  @override
  void dispose() {
    _subscription?.cancel();
    _linkedWorkOrdersSubscription?.cancel();
    _partSubscription?.cancel();
    _assetsCustomFieldsSubscription?.cancel();
    _connectionCustomFieldsSubscription?.cancel();
    super.dispose();
  }

  void initializeAssetCustomFields(String assetId) {
    _assetsCustomFieldsSubscription?.cancel();
    _assetsCustomFieldsSubscription =
        _workOrderService.getAssetCustomFields(assetId).listen(
      (fields) {
        updateState(
          state.copyWith(
            assetsCustomFieldsFetchState:
                state.assetsCustomFieldsFetchState.toLoaded(
              data: fields
                  .map((e) => CustomFieldModel.fromServiceModel(e))
                  .toList(),
            ),
          ),
        );
      },
    );
  }

  void initializeConnectionCustomFields(String connectionId) {
    _connectionCustomFieldsSubscription?.cancel();
    _connectionCustomFieldsSubscription =
        _workOrderService.getConnectionCustomFields(connectionId).listen(
      (fields) {
        updateState(
          state.copyWith(
            facilityCustomFieldsFetchState:
                state.facilityCustomFieldsFetchState.toLoaded(
              data: fields
                  .map((e) => CustomFieldModel.fromServiceModel(e))
                  .toList(),
            ),
          ),
        );
      },
    );
  }

  void toggleHideEmptyCustomFields() {
    updateState(
      state.copyWith(hideEmptyCustomFields: !state.hideEmptyCustomFields),
    );
  }

  Future<void> changeWorkOrderStatus(WorkOrderStatusModel status) async {
    updateState(
      state.copyWith(
        changeWorkOrderStatusState:
            state.changeWorkOrderStatusState.toLoading(),
      ),
    );

    try {
      final oldStatus = state.workOrderFetchState.data?.status?.name;

      await _workOrderService.changeWorkOrderStatus(
        state.id,
        status.toServiceModel(),
      );

      await Future.delayed(const Duration(milliseconds: 600));

      AnalyticsService.instance.sendEvent(
        "Work Order Status Changed",
        {
          "work_order_id": state.id,
          "from": oldStatus,
          "to": status.name,
        },
      );

      updateState(
        state.copyWith(
          changeWorkOrderStatusState:
              state.changeWorkOrderStatusState.toLoaded(),
        ),
      );
    } on WorkOrderLimitExceededException catch (e) {
      updateState(
        state.copyWith(
          changeWorkOrderStatusState:
              state.changeWorkOrderStatusState.toFailure(
            error: e.message,
          ),
        ),
      );
    } on ApiException catch (e) {
      updateState(
        state.copyWith(
          changeWorkOrderStatusState:
              state.changeWorkOrderStatusState.toFailure(error: e.message),
        ),
      );
    } on NoInternetException {
      updateState(
        state.copyWith(
          changeWorkOrderStatusState:
              state.changeWorkOrderStatusState.toFailure(
            error: ExceptionMessage.noInternet,
          ),
        ),
      );
    } catch (e) {
      updateState(
        state.copyWith(
          changeWorkOrderStatusState: state.changeWorkOrderStatusState
              .toFailure(error: ExceptionMessage.general),
        ),
      );
    }
  }

  Future<void> getWorkOrderStatuses() async {
    updateState(
      state.copyWith(
        workOrderStatusesFetchState:
            state.workOrderStatusesFetchState.toLoading(),
      ),
    );

    try {
      final statuses = await _workOrderService.getStatusMap();
      updateState(
        state.copyWith(
          workOrderStatusesFetchState:
              state.workOrderStatusesFetchState.toLoaded(
            data: statuses.values
                .map((e) => WorkOrderStatusModel.fromServiceModel(e))
                .toList(),
          ),
        ),
      );
    } catch (e) {
      updateState(
        state.copyWith(
          workOrderStatusesFetchState:
              state.workOrderStatusesFetchState.toFailure(
            error: e.toString(),
          ),
        ),
      );
    }
  }

  Future<void> getWorkOrderDetails() async {
    updateState(
      state.copyWith(
        workOrderFetchState: state.workOrderFetchState.toLoading(),
      ),
    );

    _subscription?.cancel();

    _subscription = _workOrderService
        .getTicketDetails(state.id)
        .map((event) => WorkOrderModel.fromServiceModel(event!))
        .handleError(
      (error) {
        updateState(
          state.copyWith(
            workOrderFetchState: state.workOrderFetchState
                .toFailure(error: ExceptionMessage.general),
          ),
        );
      },
    ).listen(
      (workOrder) async {
        updateState(
          state.copyWith(
            workOrderFetchState:
                state.workOrderFetchState.toLoaded(data: workOrder),
          ),
        );

        if (workOrder.asset?.id != null && !workOrder.isAssetDeleted) {
          initializeAssetCustomFields(workOrder.asset!.id);
          await fetchAssetsCustomFields(workOrder.asset!.id);
        }
        if (workOrder.facility?.id != null && !workOrder.isFacilityDeleted) {
          initializeConnectionCustomFields(workOrder.facility!.id);
          await fetchConnectionCustomFields(workOrder.facility!.id);
        }
      },
    );
  }

  Future<void> getPaidFeatures() async {
    updateState(
      state.copyWith(
        paidFeaturesFetchState: state.paidFeaturesFetchState.toLoading(),
      ),
    );

    try {
      final features = await _workOrderService.getPaidFeatures();
      updateState(
        state.copyWith(
          paidFeaturesFetchState:
              state.paidFeaturesFetchState.toLoaded(data: features),
        ),
      );
    } catch (error) {
      updateState(
        state.copyWith(
          paidFeaturesFetchState: state.paidFeaturesFetchState
              .toFailure(error: ExceptionMessage.general),
        ),
      );
    }
  }

  Future<void> getProcedureTemplates() async {
    updateState(
      state.copyWith(
        procedureTemplateFetchState:
            state.procedureTemplateFetchState.toLoading(),
      ),
    );

    try {
      final templates = await _workOrderService.getProcedureTemplates();

      updateState(
        state.copyWith(
          procedureTemplateFetchState:
              state.procedureTemplateFetchState.toLoaded(
            data: templates
                .map((e) => ProcedureTemplateModel.fromModel(e))
                .toList(),
          ),
        ),
      );
    } on NoInternetException catch (_) {
      updateState(
        state.copyWith(
          procedureTemplateFetchState: state.procedureTemplateFetchState
              .toFailure(error: ExceptionMessage.noInternet),
        ),
      );
    } catch (error) {
      updateState(
        state.copyWith(
          procedureTemplateFetchState: state.procedureTemplateFetchState
              .toFailure(error: ExceptionMessage.general),
        ),
      );
    }
  }

  Future<void> getCustomFields() async {
    updateState(
      state.copyWith(
        customFieldsFetchState: state.customFieldsFetchState.toLoading(),
      ),
    );

    try {
      final fields = await _workOrderService.getWorkOrderCustomFields(state.id);

      updateState(
        state.copyWith(
          customFieldsFetchState: state.customFieldsFetchState.toLoaded(
            data: fields
                .map((e) => CustomFieldModel.fromServiceModel(e))
                .toList(),
          ),
        ),
      );
    } catch (e) {
      updateState(
        state.copyWith(
          customFieldsFetchState: state.customFieldsFetchState.toFailure(
            error: e.toString(),
          ),
        ),
      );
    }
  }

  Future<void> fetchConnectionCustomFields(String id) async {
    updateState(
      state.copyWith(
        facilityCustomFieldsFetchState:
            state.facilityCustomFieldsFetchState.toLoading(),
      ),
    );

    try {
      final fields = await _workOrderService.fetchConnectionCustomFields(id);

      updateState(
        state.copyWith(
          facilityCustomFieldsFetchState:
              state.facilityCustomFieldsFetchState.toLoaded(
            data: fields
                .map((e) => CustomFieldModel.fromServiceModel(e))
                .toList(),
          ),
        ),
      );
    } catch (e) {
      updateState(
        state.copyWith(
          facilityCustomFieldsFetchState:
              state.facilityCustomFieldsFetchState.toFailure(
            error: e.toString(),
          ),
        ),
      );
    }
  }

  Future<void> fetchAssetsCustomFields(String id) async {
    updateState(
      state.copyWith(
        assetsCustomFieldsFetchState:
            state.assetsCustomFieldsFetchState.toLoading(),
      ),
    );

    try {
      final fields = await _workOrderService.fetchAssetCustomFields(id);

      updateState(
        state.copyWith(
          assetsCustomFieldsFetchState:
              state.assetsCustomFieldsFetchState.toLoaded(
            data: fields
                .map((e) => CustomFieldModel.fromServiceModel(e))
                .toList(),
          ),
        ),
      );
    } catch (e) {
      updateState(
        state.copyWith(
          assetsCustomFieldsFetchState:
              state.assetsCustomFieldsFetchState.toFailure(
            error: e.toString(),
          ),
        ),
      );
    }
  }

  Future<void> getAttachedParts() async {
    updateState(
      state.copyWith(
        assignedPartsFetchState: state.assignedPartsFetchState.toLoading(),
      ),
    );

    _partSubscription?.cancel();

    _partSubscription = _workOrderService
        .getAttachedParts(state.id)
        .map(
          (event) => event.map(PartModel.fromServiceModel).toList(),
        )
        .handleError(
      (error) {
        updateState(
          state.copyWith(
            assignedPartsFetchState: state.assignedPartsFetchState
                .toFailure(error: ExceptionMessage.general),
          ),
        );
      },
    ).listen(
      (parts) {
        updateState(
          state.copyWith(
            assignedPartsFetchState:
                state.assignedPartsFetchState.toLoaded(data: parts),
          ),
        );
      },
    );
  }

  Future<void> getParts() async {
    updateState(
      state.copyWith(
        partsFetchState: state.partsFetchState.toLoading(),
      ),
    );

    try {
      final parts = await _workOrderService.fetchInvertoryPartsFor(
        state.workOrderFetchState.data!.asset!.id,
      );

      updateState(
        state.copyWith(
          partsFetchState: state.partsFetchState.toLoaded(
              data: parts.map((e) => PartModel.fromServiceModel(e)).toList()),
        ),
      );
    } catch (error) {
      updateState(
        state.copyWith(
          partsFetchState:
              state.partsFetchState.toFailure(error: ExceptionMessage.general),
        ),
      );
    }
  }

  Future<void> updateCustomField(
    List<CustomFieldModel> updatedFields,
  ) async {
    await _workOrderService.updateCustomField(
      workOrderId: state.id,
      fields: updatedFields
          .map((field) => CustomFieldRequest(id: field.id, value: field.value))
          .toList(),
    );

    await getCustomFields();
  }

  Future<void> updateConnectionCustomField(
      List<CustomFieldModel> editedFields) async {
    await _workOrderService.updateConnectionCustomField(
      connectionId: state.workOrderFetchState.data!.facility!.id,
      fields: editedFields
          .map((field) => CustomFieldRequest(id: field.id, value: field.value))
          .toList(),
    );
  }

  Future<void> updateAssetCustomField(
    List<CustomFieldModel> editedFields,
  ) async {
    await _workOrderService.updateAssetCustomField(
      assetId: state.workOrderFetchState.data!.asset!.id,
      fields: editedFields
          .map((field) => CustomFieldRequest(id: field.id, value: field.value))
          .toList(),
    );
  }
}
