part of 'tabs.dart';

class OverviewTabView extends StatefulWidget {
  const OverviewTabView({super.key});

  @override
  State<OverviewTabView> createState() => _OverviewTabViewState();
}

class _OverviewTabViewState extends State<OverviewTabView>
    with AutomaticKeepAliveClientMixin {
  @override
  Widget build(BuildContext context) {
    super.build(context);
    return const _Body();
  }

  @override
  bool get wantKeepAlive => true;
}

class _Body extends StatelessWidget {
  const _Body();

  @override
  Widget build(BuildContext context) {
    return ViewModelBuilder<WorkOrderOverviewViewModel, WorkOrderOverviewState>(
      builder: (context, state) {
        final workOrder = state.workOrderFetchState.data!;

        return Scaffold(
          body: CustomScrollView(
            key: const PageStorageKey<String>("overview-tab"),
            cacheExtent: 600,
            slivers: [
              SliverOverlapInjector(
                handle:
                    NestedScrollView.sliverOverlapAbsorberHandleFor(context),
              ),
              SliverList.list(
                children: [
                  SizedBox(height: context.makulaPadding.s),
                  if (workOrder.isAssetDeleted)
                    DataListItem(
                      leadingIcon: MakulaIcons.machines,
                      title: context.localization.asset,
                      subtitle: context.localization.deletedAsset,
                    )
                  else
                    DataListItem(
                      leadingIcon: MakulaIcons.machines,
                      title: context.localization.asset,
                      subtitle: workOrder.asset!.name,
                      trailingIcon: MakulaIcons.chevronRight,
                      onTap: () {
                        if (workOrder.isAssetDeleted) return;

                        AnalyticsService.instance.sendEvent(
                          "View Machine Details",
                          {
                            "machine_id": workOrder.asset!.id,
                            "machine_name": workOrder.asset!.name,
                            "work_order_id": workOrder.id,
                          },
                        );

                        context.push(AssetDetailsPage.pageRoute(
                          id: workOrder.asset!.id,
                        ));
                      },
                    ),
                  Divider(
                    color: context.makulaBorderColors.primary,
                    height: 1,
                  ),
                  if (workOrder.isFacilityDeleted)
                    DataListItem(
                      leading: MakulaIcon(
                        MakulaIcons.facility,
                        size: 24,
                        color: context.makulaContentColors.brand,
                      ),
                      title: context.localization.connection,
                      subtitle: context.localization.deletedConnection,
                    )
                  else
                    DataListItem(
                      leading: MakulaIcon(
                        MakulaIcons.facility,
                        size: 24,
                        color: context.makulaContentColors.brand,
                      ),
                      title: context.localization.connection,
                      subtitle: workOrder.facility?.name!,
                      trailingIcon: MakulaIcons.chevronRight,
                      onTap: () {
                        if (workOrder.isFacilityDeleted) return;

                        AnalyticsService.instance.sendEvent(
                          "View Connection Details",
                          {
                            "connection_id": workOrder.facility!.id,
                            "connection_name": workOrder.facility!.name,
                            "work_order_id": workOrder.id,
                          },
                        );

                        context.push(ConnectionDetailsPage.pageRoute(
                          id: workOrder.facility!.id,
                        ));
                      },
                    ),
                  Divider(
                    color: context.makulaBorderColors.primary,
                    height: 1,
                  ),
                  if (workOrder.hasFacilityContact) ...[
                    DataListItem(
                      leading: MakulaIcon(
                        MakulaIcons.user,
                        size: 24,
                        color: context.makulaContentColors.brand,
                      ),
                      title: context.localization.contact,
                      subtitle: workOrder.user?.name ?? "-",
                    ),
                    Divider(
                      color: context.makulaBorderColors.primary,
                      height: 1,
                    ),
                  ],
                  Container(
                    width: double.infinity,
                    color: context.makulaBackgroundColors.primary,
                    padding: EdgeInsets.symmetric(
                      horizontal: context.makulaPadding.s,
                      vertical: context.makulaPadding.xxs,
                    ),
                    child: TextLinkButton.small(
                      title: context.localization.viewMoreDetails,
                      onTap: () {
                        final indexOfDetails =
                            WorOrderOverviewTab.details.index;
                        DefaultTabController.of(context)
                            .animateTo(indexOfDetails);
                      },
                    ),
                  ),
                  SizedBox(height: context.makulaPadding.s),
                  _StatusListItem(workOrder: workOrder),
                  SizedBox(height: context.makulaPadding.s),
                  DetailListItem(
                    title: context.localization.description,
                    icon: MakulaIcons.leftAlign,
                    child: (workOrder.isDescriptionEmpty)
                        ? Container(
                            alignment: AlignmentDirectional.topStart,
                            padding: EdgeInsetsDirectional.only(
                              start: context.makulaPadding.l,
                              end: context.makulaPadding.l,
                              bottom: context.makulaPadding.m,
                            ),
                            child: Text(
                              context.localization.noDescriptionMessage,
                              style: context.makulaTypography.body.large
                                  .secondary(context),
                            ),
                          )
                        : Padding(
                            padding: EdgeInsetsDirectional.symmetric(
                              horizontal: context.makulaPadding.l,
                            ),
                            child: ExpandableFormattedTextView(
                              key: const PageStorageKey<String>(
                                  "wo-description"),
                              workOrder.description,
                              fontSize: 16,
                              onShowLess: () {},
                            ),
                          ),
                  ),
                  SizedBox(height: context.makulaPadding.s),
                  const _ProcedureSection(),
                  SizedBox(height: context.makulaPadding.s),
                  const _PartsSection(),
                  SizedBox(height: context.makulaPadding.s),
                  const _LinkedWorkOrderSection(),
                  SizedBox(height: context.makulaPadding.s),
                  const WorkOrderCustomFields(),
                  SizedBox(height: context.makulaPadding.s),
                  if (!workOrder.isFacilityDeleted)
                    const FacilityCustomFields(),
                  SizedBox(height: context.makulaPadding.s),
                  if (!workOrder.isAssetDeleted) const AssetsCustomFields(),
                  SizedBox(height: context.makulaPadding.xxl),
                ],
              ),
            ],
          ),
          bottomNavigationBar: BottomActionBar(
            child: Row(
              children: [
                Expanded(
                  child: OutlineButton.medium(
                    leadingIcon: MakulaIcons.location,
                    title: context.localization.connectionDirections,
                    onTap: () {
                      if (!workOrder.hasCoordinates) {
                        Alert.showError(
                          errorMessage: context
                              .localization.connectionLocationNotAvailable,
                        );
                        return;
                      }
                      AnalyticsService.instance.sendEvent(
                        "Opened Faciltiy Directions",
                        {
                          "facility_id": workOrder.facility!.id,
                          "facility_name": workOrder.facility!.name,
                          "work_order_id": workOrder.id,
                        },
                      );
                      _onTapFacility(workOrder, context);
                    },
                  ),
                ),
                SizedBox(width: context.makulaPadding.s),
                OutlineButton.mediumIcon(
                  icon: MakulaIcons.timeTracking,
                  onTap: () {
                    AnalyticsService.instance.sendEvent(
                      "View WO Time Tracking",
                      {
                        "work_order_id": workOrder.id,
                      },
                    );
                    Navigator.of(context).push(
                      TimeActivityLogsPage.route(
                        workOrderId: workOrder.id,
                        isWorkOrderClosed: workOrder.isClosed,
                      ),
                    );
                  },
                ),
                SizedBox(width: context.makulaPadding.s),
                const _MessagingButton(),
              ],
            ),
          ),
        );
      },
    );
  }

  void _onTapFacility(WorkOrderModel? workOrder, BuildContext context) async {
    final unableToOpenMapsString = context.localization.unableToOpenMaps;
    if (workOrder?.facility?.isCoordinatesEmpty ?? true) {
      return;
    }
    final facility = workOrder!.facility!;

    final options = await MapsLauncher.getMapOptions();

    if (options.isEmpty) {
      Alert.showError(errorMessage: unableToOpenMapsString);
      return;
    }

    if (options.length == 1) {
      final option = options.first;
      launchUrl(
        option.generateUri(
          facility.latitude!.toDouble(),
          facility.longitude!.toDouble(),
        ),
      );

      return;
    }

    if (context.mounted) {
      MakulaBottomSheet(
        title: context.localization.chooseMap,
        builder: (context) => Padding(
          padding: const EdgeInsets.only(bottom: 16.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: options
                .map(
                  (e) => ListTile(
                    title: Text(e.label(context.localization)),
                    onTap: () {
                      launchUrl(
                        e.generateUri(
                          facility.latitude!.toDouble(),
                          facility.longitude!.toDouble(),
                        ),
                        mode: LaunchMode.externalApplication,
                      );
                    },
                  ),
                )
                .toList(),
          ),
        ),
      ).show(context);
    }
  }
}

class _MessagingButton extends StatelessWidget {
  const _MessagingButton();

  @override
  Widget build(BuildContext context) {
    return ViewModelBuilder<WorkOrderOverviewViewModel, WorkOrderOverviewState>(
      builder: (context, state) {
        if (state.workOrderFetchState.isLoading) return const SizedBox();
        return PrimaryButton.mediumIcon(
          icon: MakulaIcons.messaging,
          onTap: () {
            AnalyticsService.instance.sendEvent(
              "Opened Messaging Screen",
              {
                "work_order_id": state.workOrderFetchState.data!.id,
                "origin": "work order overview",
              },
            );

            Navigator.push(
              context,
              MaterialPageRoute(
                settings: const RouteSettings(name: "MessagingScreen"),
                builder: (_) => MessagingPage(
                  workOrderModel: state.workOrderFetchState.data!,
                ),
              ),
            );
          },
        );
      },
    );
  }
}

class _StatusListItem extends StatelessWidget {
  const _StatusListItem({required this.workOrder});

  final WorkOrderModel workOrder;

  @override
  Widget build(BuildContext context) {
    return ViewModelListener<WorkOrderOverviewViewModel,
        WorkOrderOverviewState>(
      listenWhen: (previous, current) =>
          previous.changeWorkOrderStatusState !=
          current.changeWorkOrderStatusState,
      listener: (context, state) {
        if (state.changeWorkOrderStatusState.isFailure) {
          Alert.showError(errorMessage: state.changeWorkOrderStatusState.error);
        } else if (state.changeWorkOrderStatusState.isLoaded) {
          Alert.showSuccess(
              message: context.localization.workOrderStatusUpdated);
        }
      },
      child:
          ViewModelBuilder<WorkOrderOverviewViewModel, WorkOrderOverviewState>(
              builder: (context, state) {
        final status = state.workOrderFetchState.data!.status;

        return DataListItem(
          titleWidget: Text(
            context.localization.status,
            style: context.makulaTypography.body.large.primary(context),
          ),
          trailing: state.changeWorkOrderStatusState.isLoading
              ? const MakulaLoader()
              : Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CustomFieldTag.fromTagColor(
                      tagColor:
                          context.makulaTagColors.getTagColorFor(status?.color),
                      text: status?.name ?? "-",
                    ),
                    SizedBox(width: context.makulaPadding.m),
                    MakulaIcon(
                      MakulaIcons.chevronRight,
                      color: context.makulaContentColors.primary,
                      size: 20,
                    ),
                  ],
                ),
          onTap: state.changeWorkOrderStatusState.isLoading
              ? null
              : () {
                  AnalyticsService.instance.sendEvent(
                    "Initiate Word Order Status Change",
                    {
                      "work_order_id": state.id,
                      "status_id": status?.id,
                    },
                  );

                  InhertedViewModel.of<WorkOrderOverviewViewModel>(context)
                      .viewModel
                      .getWorkOrderStatuses();
                  Navigator.of(context).push(
                    SelectStatusPage.route(
                      viewModel:
                          InhertedViewModel.of<WorkOrderOverviewViewModel>(
                        context,
                      ).viewModel,
                    ),
                  );
                },
        );
      }),
    );
  }
}

class _LinkedWorkOrderSection extends StatelessWidget {
  const _LinkedWorkOrderSection();

  @override
  Widget build(BuildContext context) {
    return ViewModelBuilder<WorkOrderOverviewViewModel, WorkOrderOverviewState>(
      builder: (context, state) {
        final linkedWorkOrders = state.linkedWorkOrders.take(2).toList();

        return DetailListItem(
          title: context.localization.linkedWorkOrders,
          subtitle: context.localization
              .linkedWorkOrdersCount(state.linkedWorkOrders.length),
          child: Material(
            child: ListView.separated(
              padding: EdgeInsets.zero,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: linkedWorkOrders.length +
                  (state.linkedWorkOrders.length > 2 ? 1 : 0),
              itemBuilder: (context, index) {
                if (linkedWorkOrders.length == index) {
                  return Container(
                    width: double.infinity,
                    color: context.makulaBackgroundColors.primary,
                    padding: EdgeInsets.symmetric(
                      horizontal: context.makulaPadding.s,
                      vertical: context.makulaPadding.xxs,
                    ),
                    child: TextLinkButton.small(
                      title: context.localization.viewMoreLinkedWorkOrders,
                      onTap: () {
                        final index =
                            WorOrderOverviewTab.linkedWorkOrders.index;
                        DefaultTabController.of(context).animateTo(index);
                      },
                    ),
                  );
                }
                final workOrder = linkedWorkOrders[index];
                return LinkedWorkOrderCard(
                  item: workOrder,
                  showTrailingIcon: true,
                  onTap: () {
                    AnalyticsService.instance.sendEvent(
                      "View Work Order Overview",
                      {
                        "work_order_id": workOrder.id,
                        "index": index,
                        'origin': 'linked work order',
                      },
                    );

                    Navigator.of(context)
                        .push(WorkOrderOverviewPage.route(workOrder.id));
                  },
                );
              },
              separatorBuilder: (context, index) => Divider(
                height: 1,
                color: context.makulaBorderColors.primary,
              ),
            ),
          ),
        );
      },
    );
  }
}

class _ProcedureSection extends StatelessWidget {
  const _ProcedureSection();

  @override
  Widget build(BuildContext context) {
    return ViewModelBuilder<WorkOrderOverviewViewModel, WorkOrderOverviewState>(
      builder: (context, state) {
        final procedures = state.procedures.take(2).toList();
        final workOrder = state.workOrderFetchState.data!;

        return DetailListItem(
          title: context.localization.procedures,
          subtitle:
              context.localization.procedureCount(state.procedures.length),
          child: Material(
            child: ListView.separated(
              padding: EdgeInsets.zero,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount:
                  procedures.length + (state.procedures.length > 2 ? 1 : 0),
              itemBuilder: (context, index) {
                if (procedures.length == index) {
                  return Container(
                    width: double.infinity,
                    color: context.makulaBackgroundColors.primary,
                    padding: EdgeInsets.symmetric(
                      horizontal: context.makulaPadding.s,
                      vertical: context.makulaPadding.xxs,
                    ),
                    child: TextLinkButton.small(
                      title: context.localization.viewMoreProcedures,
                      onTap: () {
                        final index = WorOrderOverviewTab.procedures.index;
                        DefaultTabController.of(context).animateTo(index);
                      },
                    ),
                  );
                }
                final procedure = procedures[index];
                return ProcedureCard(
                  item: procedure,
                  showTrailingIcon: true,
                  onTap: () {
                    AnalyticsService.instance.sendEvent(
                      "Open Attached Procedure Instance",
                      {
                        "work_order_id": workOrder.id,
                        "procedure_id": procedure.id,
                        'origin': 'wo overview tab',
                      },
                    );

                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        settings:
                            const RouteSettings(name: "ProcedureOverview"),
                        builder: (context) => ProcedureOverview(
                          procedureID: procedure.id,
                          ticketID: workOrder.id,
                          source: workOrder.title,
                          parentAssetId: workOrder.asset?.id,
                        ),
                      ),
                    );
                  },
                );
              },
              separatorBuilder: (context, index) => Divider(
                height: 1,
                color: context.makulaBorderColors.primary,
              ),
            ),
          ),
        );
      },
    );
  }
}

class _PartsSection extends StatelessWidget {
  const _PartsSection();

  @override
  Widget build(BuildContext context) {
    return ViewModelBuilder<WorkOrderOverviewViewModel, WorkOrderOverviewState>(
      builder: (context, state) {
        final parts = state.assignedParts.take(2).toList();

        return DetailListItem(
          title: context.localization.parts,
          subtitle: context.localization.partCount(state.assignedParts.length),
          child: Material(
            child: ListView.separated(
              padding: EdgeInsets.zero,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount:
                  parts.length + (state.assignedParts.length > 2 ? 1 : 0),
              itemBuilder: (context, index) {
                if (parts.length == index) {
                  return Container(
                    width: double.infinity,
                    color: context.makulaBackgroundColors.primary,
                    padding: EdgeInsets.symmetric(
                      horizontal: context.makulaPadding.s,
                      vertical: context.makulaPadding.xxs,
                    ),
                    child: TextLinkButton.small(
                      title: context.localization.viewMoreParts,
                      onTap: () {
                        final index = WorOrderOverviewTab.parts.index;
                        DefaultTabController.of(context).animateTo(index);
                      },
                    ),
                  );
                }
                final part = parts[index];
                return PartCard(
                  part: part,
                  showTrailingIcon: true,
                  onTap: () {
                    final workOrderId =
                        InhertedViewModel.of<WorkOrderOverviewViewModel>(
                                context)
                            .viewModel
                            .state
                            .id;
                    final isClosed =
                        InhertedViewModel.of<WorkOrderOverviewViewModel>(
                                context)
                            .viewModel
                            .state
                            .workOrderFetchState
                            .data!
                            .isClosed;

                    Navigator.of(context).push(
                      WorkOrderPartDetailsPage.route(
                        part: part,
                        workOrderId: workOrderId,
                        canRemove: !isClosed,
                      ),
                    );
                  },
                );
              },
              separatorBuilder: (context, index) => Divider(
                height: 1,
                color: context.makulaBorderColors.primary,
              ),
            ),
          ),
        );
      },
    );
  }
}

class WorkOrderCustomFields extends StatelessWidget {
  const WorkOrderCustomFields({super.key});

  @override
  Widget build(BuildContext context) {
    final hasPermission = InhertedViewModel.of<AppViewModel>(context)
        .viewModel
        .state
        .user
        .permissions
        .hasEditWorkOrderCustomFieldsPermission;
    return ViewModelBuilder<WorkOrderOverviewViewModel, WorkOrderOverviewState>(
      builder: (context, state) {
        final fetchState = state.customFieldsFetchState;
        final customFields = state.customFields;
        final isClosed = state.workOrderFetchState.data?.isClosed == true;

        final cannotEdit = !hasPermission ||
            state.customFieldsFetchState.isFailure ||
            isClosed ||
            state.customFields.isEmpty;

        return DetailListItem(
          title: context.localization.workOrderCustomFields,
          icon: MakulaIcons.customField,
          trailingIcon: cannotEdit ? null : MakulaIcons.edit,
          onTap: cannotEdit
              ? null
              : () {
                  AnalyticsService.instance.sendEvent(
                    "Initiate Edit WO Custom Fields",
                    {
                      "work_order_id": state.id,
                      "from": "work_order_overview",
                    },
                  );

                  Navigator.of(context).push(
                    EditCustomFieldsPage.route(
                        title:
                            context.localization.editWorkOrderCustomFieldTitle,
                        customFields: customFields,
                        onUpdate: (editedFields) async {
                          await InhertedViewModel.of<
                                  WorkOrderOverviewViewModel>(context)
                              .viewModel
                              .updateCustomField(editedFields);

                          AnalyticsService.instance.sendEvent(
                            "Edited WO Custom Fields",
                            {
                              "work_order_id": state.id,
                              "no_of_custom_fields": editedFields.length,
                            },
                          );
                        }),
                  );
                },
          child: fetchState.isFailure
              ? Padding(
                  padding: EdgeInsets.all(context.makulaPadding.l),
                  child: FullScreenErrorWidget(
                    errorMessage: context.localization.loadCustomFieldsFailed,
                    onRetry: InhertedViewModel.of<WorkOrderOverviewViewModel>(
                            context)
                        .viewModel
                        .getCustomFields,
                  ),
                )
              : fetchState.isLoaded
                  ? MakulaCustomFields(customFields: state.customFields)
                  : const Center(child: MakulaLoader()),
        );
      },
    );
  }
}

class FacilityCustomFields extends StatelessWidget {
  const FacilityCustomFields({super.key});

  @override
  Widget build(BuildContext context) {
    final hasPermission = InhertedViewModel.of<AppViewModel>(context)
        .viewModel
        .state
        .user
        .permissions
        .hasEditConnectionsCustomFieldsPermission;

    return ViewModelBuilder<WorkOrderOverviewViewModel, WorkOrderOverviewState>(
      builder: (context, state) {
        final fetchState = state.facilityCustomFieldsFetchState;
        final customFields = state.facilityCustomFields;
        final isClosed = state.workOrderFetchState.data?.isClosed == true;

        final cannotEdit = !hasPermission ||
            isClosed ||
            state.facilityCustomFieldsFetchState.isFailure ||
            state.facilityCustomFields.isEmpty;

        return DetailListItem(
          title: context.localization.connectionCustomFields,
          icon: MakulaIcons.customField,
          trailingIcon: cannotEdit ? null : MakulaIcons.edit,
          onTap: cannotEdit
              ? null
              : () {
                  AnalyticsService.instance.sendEvent(
                    "Initiate Edit Connections Custom Fields",
                    {
                      "work_order_id": state.id,
                      "from": "work_order_overview",
                    },
                  );

                  Navigator.of(context).push(
                    EditCustomFieldsPage.route(
                      title:
                          context.localization.editConnectionCustomFieldTitle,
                      customFields: customFields,
                      onUpdate: (editedFields) async {
                        await InhertedViewModel.of<WorkOrderOverviewViewModel>(
                                context)
                            .viewModel
                            .updateConnectionCustomField(editedFields);

                        AnalyticsService.instance.sendEvent(
                          "Edited Connections Custom Fields",
                          {
                            "work_order_id": state.id,
                            "no_of_custom_fields": editedFields.length,
                          },
                        );
                      },
                    ),
                  );
                },
          child: fetchState.isFailure
              ? Padding(
                  padding: EdgeInsets.all(context.makulaPadding.l),
                  child: FullScreenErrorWidget(
                    errorMessage: context.localization.loadCustomFieldsFailed,
                    onRetry: () =>
                        InhertedViewModel.of<WorkOrderOverviewViewModel>(
                                context)
                            .viewModel
                            .fetchConnectionCustomFields(
                              state.workOrderFetchState.data!.facility!.id,
                            ),
                  ),
                )
              : fetchState.isLoaded
                  ? MakulaCustomFields(customFields: state.facilityCustomFields)
                  : const Center(child: MakulaLoader()),
        );
      },
    );
  }
}

class AssetsCustomFields extends StatelessWidget {
  const AssetsCustomFields({super.key});

  @override
  Widget build(BuildContext context) {
    final hasPermission = InhertedViewModel.of<AppViewModel>(context)
        .viewModel
        .state
        .user
        .permissions
        .hasEditAssetCustomFieldsPermission;

    return ViewModelBuilder<WorkOrderOverviewViewModel, WorkOrderOverviewState>(
      builder: (context, state) {
        final fetchState = state.assetsCustomFieldsFetchState;
        final customFields = state.assetsCustomFields;
        final isClosed = state.workOrderFetchState.data?.isClosed == true;

        final cannotEdit = !hasPermission ||
            state.assetsCustomFieldsFetchState.isFailure ||
            isClosed ||
            state.assetsCustomFields.isEmpty;

        return DetailListItem(
          title: context.localization.assetCustomFields,
          icon: MakulaIcons.customField,
          trailingIcon: cannotEdit ? null : MakulaIcons.edit,
          onTap: cannotEdit
              ? null
              : () {
                  AnalyticsService.instance.sendEvent(
                    "Initiate Edit Assets Custom Fields",
                    {
                      "work_order_id": state.id,
                      "from": "work_order_overview",
                    },
                  );

                  Navigator.of(context).push(
                    EditCustomFieldsPage.route(
                      title: context.localization.editAssetCustomFieldTitle,
                      customFields: customFields,
                      onUpdate: (editedFields) async {
                        await InhertedViewModel.of<WorkOrderOverviewViewModel>(
                                context)
                            .viewModel
                            .updateAssetCustomField(editedFields);

                        AnalyticsService.instance.sendEvent(
                          "Edited Assets Custom Fields",
                          {
                            "work_order_id": state.id,
                            "no_of_custom_fields": editedFields.length,
                          },
                        );
                      },
                    ),
                  );
                },
          child: fetchState.isFailure
              ? Padding(
                  padding: EdgeInsets.all(context.makulaPadding.l),
                  child: FullScreenErrorWidget(
                    errorMessage: context.localization.loadCustomFieldsFailed,
                    onRetry: () =>
                        InhertedViewModel.of<WorkOrderOverviewViewModel>(
                                context)
                            .viewModel
                            .fetchAssetsCustomFields(
                              state.workOrderFetchState.data!.asset!.id,
                            ),
                  ),
                )
              : fetchState.isLoaded
                  ? MakulaCustomFields(customFields: state.assetsCustomFields)
                  : const Center(child: MakulaLoader()),
        );
      },
    );
  }
}
