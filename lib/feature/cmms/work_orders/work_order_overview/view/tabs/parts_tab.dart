part of 'tabs.dart';

class PartsTabView extends StatefulWidget {
  const PartsTabView({super.key});

  @override
  State<PartsTabView> createState() => _PartsTabViewState();
}

class _PartsTabViewState extends State<PartsTabView>
    with AutomaticKeepAliveClientMixin {
  @override
  Widget build(BuildContext context) {
    super.build(context);
    return const _PartListBody();
  }

  @override
  bool get wantKeepAlive => true;
}

class _PartListBody extends StatelessWidget {
  const _PartListBody();

  @override
  Widget build(BuildContext context) {
    return ViewModelBuilder<WorkOrderOverviewViewModel, WorkOrderOverviewState>(
      buildWhen: (previous, current) =>
          previous.assignedPartsFetchState != current.assignedPartsFetchState ||
          previous.workOrderFetchState != current.workOrderFetchState,
      builder: (context, state) {
        final workOrder = state.workOrderFetchState.data!;
        var fetchState = state.assignedPartsFetchState;
        if (fetchState.isFailure) {
          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: FullScreenErrorWidget(
              errorMessage: context.localization.unableToLoadAssignedParts,
              onRetry: InhertedViewModel.of<WorkOrderOverviewViewModel>(context)
                  .viewModel
                  .getAttachedParts,
            ),
          );
        }
        if (fetchState.isLoaded) {
          if (state.assignedParts.isEmpty) {
            return workOrder.isAssetDeleted
                ? EmptyStateView(
                    title: context.localization.assetDeteledWorkOrderTitle,
                    subtitle: context.localization.assetDeteledWorkOrderMessage,
                    child: _AttachPartButton(workOrder: workOrder),
                  )
                : EmptyStateView(
                    title: context.localization.noPartsAssignedTitle,
                    subtitle: context.localization.noPartsAssignedMessage,
                    child: _AttachPartButton(workOrder: workOrder),
                  );
          }
          return Scaffold(
            body: CustomScrollView(
              key: const PageStorageKey<String>("parts-tab"),
              slivers: [
                SliverOverlapInjector(
                  handle:
                      NestedScrollView.sliverOverlapAbsorberHandleFor(context),
                ),
                SliverPadding(
                  padding:
                      EdgeInsets.symmetric(vertical: context.makulaPadding.xs),
                  sliver: _PartsList(parts: state.assignedParts),
                ),
              ],
            ),
            bottomNavigationBar: BottomActionBar(
              child: _AttachPartButton(workOrder: workOrder),
            ),
          );
        }
        return const Center(child: MakulaLoader());
      },
    );
  }
}

class _PartsList extends StatelessWidget {
  const _PartsList({
    required this.parts,
  });

  final List<PartModel> parts;

  @override
  Widget build(BuildContext context) {
    return SliverList.separated(
      itemCount: parts.length,
      separatorBuilder: (context, __) => SizedBox(
        height: context.makulaPadding.s,
      ),
      itemBuilder: (context, index) {
        final part = parts[index];
        return PartCard(
          part: part,
          onTap: () {
            final workOrderId =
                InhertedViewModel.of<WorkOrderOverviewViewModel>(context)
                    .viewModel
                    .state
                    .id;
            final isClosed =
                InhertedViewModel.of<WorkOrderOverviewViewModel>(context)
                    .viewModel
                    .state
                    .workOrderFetchState
                    .data!
                    .isClosed;

            Navigator.of(context).push(
              WorkOrderPartDetailsPage.route(
                part: part,
                workOrderId: workOrderId,
                canRemove: !isClosed,
              ),
            );
          },
        );
      },
    );
  }
}

class PartCard extends StatelessWidget {
  const PartCard({
    super.key,
    required this.part,
    required this.onTap,
    this.showTrailingIcon = false,
  });

  final PartModel part;
  final VoidCallback? onTap;
  final bool showTrailingIcon;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      highlightColor: context.makulaBackgroundColors.highlight,
      splashColor: context.makulaBackgroundColors.splash,
      child: Ink(
        color: context.makulaBackgroundColors.primary,
        padding: EdgeInsetsDirectional.all(context.makulaPadding.l),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ClipRRect(
              clipBehavior: Clip.hardEdge,
              borderRadius: BorderRadius.circular(context.makulaRadius.xxs),
              child: CachedNetworkImage(
                height: 48,
                width: 56,
                imageUrl: part.imageUrl ?? '',
                fit: BoxFit.cover,
                placeholder: (context, url) => const MakulaLoader(),
                errorWidget: (context, url, error) =>
                    const AssetImagePlaceHolder(),
              ),
            ),
            SizedBox(width: context.makulaPadding.m),
            Expanded(
              child: Row(
                children: [
                  Flexible(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Flexible(
                          child: Text(
                            part.name,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                            style: context.makulaTypography.label.large.primary(
                              context,
                            ),
                          ),
                        ),
                        SizedBox(height: context.makulaPadding.xxs),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Flexible(
                              child: Text(
                                part.articleNumber,
                                overflow: TextOverflow.ellipsis,
                                style: context.makulaTypography.body.small
                                    .secondary(
                                  context,
                                ),
                              ),
                            ),
                            Text(
                              " • Qty: ${part.quantity}",
                              overflow: TextOverflow.ellipsis,
                              style: context.makulaTypography.label.small
                                  .secondary(
                                context,
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: context.makulaPadding.xxs),
                        if (part.status != null) ...[
                          CustomFieldTag.fromTagColor(
                            tagColor: context.makulaTagColors.primary,
                            text: part.status!,
                          ),
                        ],
                      ],
                    ),
                  ),
                  if (showTrailingIcon) ...[
                    SizedBox(width: context.makulaPadding.m),
                    MakulaIcon(
                      MakulaIcons.chevronRight,
                      size: 20,
                      color: context.makulaContentColors.primary,
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _AttachPartButton extends StatelessWidget {
  const _AttachPartButton({required this.workOrder});

  final WorkOrderModel workOrder;

  @override
  Widget build(BuildContext context) {
    return ViewModelListener<WorkOrderOverviewViewModel,
        WorkOrderOverviewState>(
      listenWhen: (previous, current) =>
          previous.partsFetchState != current.partsFetchState,
      listener: (context, state) {
        if (state.partsFetchState.isFailure) {
          Alert.showError(
            errorMessage:
                state.partsFetchState.error ?? ExceptionMessage.general,
          );
          return;
        }
        if (state.partsFetchState.isLoaded) {
          final filteredParts =
              InhertedViewModel.of<WorkOrderOverviewViewModel>(context)
                  .viewModel
                  .state
                  .filteredParts;

          Navigator.of(context).push(
            WorkOrderPartsListingPage.route(
              workOrderId: workOrder.id,
              invertoryParts: filteredParts,
              assetId: workOrder.asset!.id,
            ),
          );
        }
      },
      child:
          ViewModelBuilder<WorkOrderOverviewViewModel, WorkOrderOverviewState>(
        buildWhen: (previous, current) =>
            previous.partsFetchState != current.partsFetchState,
        builder: (context, state) {
          return PrimaryButton.medium(
            leadingIcon: MakulaIcons.add,
            title: context.localization.assignParts,
            isLoading: state.partsFetchState.isLoading,
            onTap: workOrder.isClosed || workOrder.isAssetDeleted
                ? null
                : () =>
                    InhertedViewModel.of<WorkOrderOverviewViewModel>(context)
                        .viewModel
                        .getParts(),
          );
        },
      ),
    );
  }
}
