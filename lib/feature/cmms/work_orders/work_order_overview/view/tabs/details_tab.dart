part of 'tabs.dart';

class DetailTabView extends StatelessWidget {
  const DetailTabView({super.key});

  @override
  Widget build(BuildContext context) {
    return ViewModelBuilder<WorkOrderOverviewViewModel, WorkOrderOverviewState>(
      builder: (context, state) {
        final workOrder = state.workOrderFetchState.data!;
        final teams = workOrder.teams;
        return CustomScrollView(
          key: const PageStorageKey<String>("detail-tab"),
          slivers: [
            SliverOverlapInjector(
              handle: NestedScrollView.sliverOverlapAbsorberHandleFor(context),
            ),
            SliverList.list(
              children: [
                SizedBox(height: context.makulaPadding.s),
                DataListItem(
                  title: context.localization.workOrderId,
                  trailing: Text(workOrder.ticketID ?? "-"),
                ),
                Divider(color: context.makulaBorderColors.primary, height: 1),
                DataListItem(
                  title: context.localization.creationDate,
                  trailing: Text(workOrder.date ?? "-"),
                ),
                Divider(color: context.makulaBorderColors.primary, height: 1),
                DataListItem(
                  title: context.localization.scheduledDateTime,
                  trailing: workOrder.schedule == null
                      ? Text(
                          "-",
                          style: context.makulaTypography.body.large
                              .primary(context),
                        )
                      : null,
                  subtitle: workOrder.schedule == null
                      ? null
                      : "${workOrder.schedule!.startDate} • ${workOrder.schedule!.startTime} - ${workOrder.schedule!.endDate} • ${workOrder.schedule!.endTime}",
                ),
                Divider(color: context.makulaBorderColors.primary, height: 1),
                DataListItem(
                  title: context.localization.totalTimeSpent,
                  trailing: Text(workOrder.formattedTotalTimeLogged),
                ),
                Divider(color: context.makulaBorderColors.primary, height: 1),
                DataListItem(
                  title: context.localization.teams,
                  trailing: getTeamsTag(context, workOrder),
                  onTap: teams.isEmpty
                      ? null
                      : () {
                          AnalyticsService.instance.sendEvent(
                            "View Work Order Teams",
                            {"work_order_id": workOrder.id},
                          );
                          Navigator.of(context).push(
                            MaterialPageRoute(
                              builder: (_) => TeamsPage(teams: teams),
                            ),
                          );
                        },
                ),
                Divider(color: context.makulaBorderColors.primary, height: 1),
                DataListItem(
                  title: context.localization.assignedTo,
                  trailing: getAssigneeTag(context, workOrder),
                  onTap: () {
                    AnalyticsService.instance.sendEvent(
                      "View Work Order Assignees",
                      {"work_order_id": workOrder.id},
                    );
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (_) =>
                            InhertedViewModel<WorkOrderOverviewViewModel>(
                          viewModel:
                              InhertedViewModel.of<WorkOrderOverviewViewModel>(
                                      context)
                                  .viewModel,
                          child: const AssigneePage(),
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ],
        );
      },
    );
  }

  Widget getAssigneeTag(BuildContext context, WorkOrderModel item) {
    return SizedBox(
      width: 160,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          if (item.assigneeNames.isNotEmpty)
            Flexible(child: UserTag(label: item.assigneeNames[0])),
          if (item.assigneeNames.length > 1) ...[
            SizedBox(width: context.makulaPadding.xs),
            UserTag(label: "+${item.assigneeNames.length - 1}"),
          ],
          SizedBox(width: context.makulaPadding.xs),
          const MakulaIcon(MakulaIcons.chevronRight, size: 20),
        ],
      ),
    );
  }

  Widget getTeamsTag(BuildContext context, WorkOrderModel item) {
    if (item.teams.isEmpty) {
      return Text(
        context.localization.none,
        style: context.makulaTypography.body.large.primary(context),
      );
    }
    return SizedBox(
      width: 160,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          if (item.teams.isNotEmpty)
            Flexible(
              child: CustomFieldTag.fromTagColor(
                tagColor: context.makulaTagColors.getTagColorFor(
                  item.teams[0].color,
                ),
                text: item.teams[0].name,
              ),
            ),
          if (item.teams.length > 1) ...[
            SizedBox(width: context.makulaPadding.xs),
            CustomFieldTag.fromTagColor(
              text: "+${item.teams.length - 1}",
              tagColor: context.makulaTagColors.getTagColorFor(
                item.teams[1].color,
              ),
            ),
          ],
          SizedBox(width: context.makulaPadding.xs),
          const MakulaIcon(MakulaIcons.chevronRight, size: 20),
        ],
      ),
    );
  }
}

class AssigneePage extends StatelessWidget {
  const AssigneePage({super.key});

  @override
  Widget build(BuildContext context) {
    final hasPermission = InhertedViewModel.of<AppViewModel>(context)
        .viewModel
        .state
        .user
        .permissions
        .hasAssignWorkOrderPermission;
    return ViewModelBuilder<WorkOrderOverviewViewModel, WorkOrderOverviewState>(
      builder: (context, state) {
        final currentAssignees =
            state.workOrderFetchState.data?.assignees ?? [];
        return Scaffold(
          appBar: MakulaBasicAppBar(title: context.localization.assignedTo),
          floatingActionButton:
              !hasPermission || state.workOrderFetchState.data!.isClosed
                  ? null
                  : PrimaryButton.largeIcon(
                      icon: MakulaIcons.edit,
                      onTap: () {
                        AnalyticsService.instance.sendEvent(
                          "Initiate Edit Work Order Assignees",
                          {"work_order_id": state.id},
                        );
                        Navigator.of(context).push(
                          AssignWorkOrderPage.route(
                            workOrderId: state.id,
                            workOrderTeams:
                                state.workOrderFetchState.data?.teams ?? [],
                            currentAssignees: currentAssignees,
                          ),
                        );
                      },
                    ),
          body: currentAssignees.isEmpty
              ? EmptyStateView(
                  title: context.localization.noUserAssignedTitle,
                  subtitle: context.localization.noUserAssignedSubtitle,
                )
              : ListView.separated(
                  padding:
                      EdgeInsets.symmetric(vertical: context.makulaPadding.xs),
                  separatorBuilder: (context, index) => Divider(
                    color: context.makulaBorderColors.primary,
                    height: 1,
                  ),
                  itemCount: currentAssignees.length,
                  itemBuilder: (context, index) {
                    final name = currentAssignees[index].name;
                    return DataListItem(
                      leading: MakulaAvatar(text: name),
                      titleWidget: Text(
                        name,
                        style: context.makulaTypography.body.large
                            .primary(context),
                      ),
                    );
                  },
                ),
        );
      },
    );
  }
}

class MakulaAvatar extends StatelessWidget {
  const MakulaAvatar({
    super.key,
    required this.text,
  });

  final String text;

  @override
  Widget build(BuildContext context) {
    return CircleAvatar(
      radius: 16,
      backgroundColor: context.makulaBackgroundColors.brandLightest,
      foregroundColor: context.makulaContentColors.brand,
      child: Text(
        text.getInitials(),
        style: context.makulaTypography.label.small,
      ),
    );
  }
}

class TeamsPage extends StatelessWidget {
  const TeamsPage({super.key, required this.teams});

  final List<WorkOrderTeamModel> teams;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MakulaBasicAppBar(title: context.localization.teams),
      body: ListView.separated(
        padding: EdgeInsets.symmetric(vertical: context.makulaPadding.xs),
        separatorBuilder: (context, index) => Divider(
          color: context.makulaBorderColors.primary,
          height: 1,
        ),
        itemCount: teams.length,
        itemBuilder: (context, index) {
          final team = teams[index];
          return DataListItem(
            leading: CustomFieldTag.fromTagColor(
              tagColor: context.makulaTagColors.getTagColorFor(team.color),
              text: team.name,
            ),
          );
        },
      ),
    );
  }
}
