import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:makula_flutter/core/core.dart';
import 'package:makula_flutter/core/ui/components/alert.dart';
import 'package:makula_flutter/core/utils/utils.dart';
import 'package:makula_flutter/feature/app/view_model/app_view_model.dart';
import 'package:makula_flutter/feature/assets/assets.dart';
import 'package:makula_flutter/feature/cmms/work_orders/edit_custom_fields/edit_custom_fields.dart';
import 'package:makula_flutter/feature/cmms/work_orders/model/model.dart';
import 'package:makula_flutter/feature/cmms/work_orders/work_order_overview/assign_work_order/assign_work_order.dart';
import 'package:makula_flutter/feature/cmms/work_orders/work_order_overview/messaging/messaging.dart';
import 'package:makula_flutter/feature/cmms/work_orders/work_order_overview/procedure/model/procedure_model.dart';
import 'package:makula_flutter/feature/cmms/work_orders/work_order_overview/procedure/procedure_overview/procedure_overview.dart';
import 'package:makula_flutter/feature/cmms/work_orders/work_order_overview/procedure/procedure_overview/widgets/select_procedure_template_view.dart';
import 'package:makula_flutter/feature/cmms/work_orders/work_order_overview/time_activity_logs/time_activity_logs.dart';
import 'package:makula_flutter/feature/cmms/work_orders/work_order_overview/view_model/work_order_overview_view_model.dart';
import 'package:makula_flutter/feature/cmms/work_orders/work_order_overview/work_order_overview.dart';
import 'package:makula_flutter/feature/cmms/work_orders/work_order_overview/work_order_part_details/work_order_part_details.dart';
import 'package:makula_flutter/feature/cmms/work_orders/work_order_overview/work_order_parts/work_order_parts.dart';
import 'package:makula_flutter/feature/connections/connection_details/view/view.dart';
import 'package:makula_flutter/service/analytics/analytics_service.dart';
import 'package:makula_theme/makula_theme.dart';
import 'package:url_launcher/url_launcher.dart';

part 'details_tab.dart';
part 'overview_tab.dart';
part 'parts_tab.dart';
part 'procedure_tab.dart';
part 'linked_work_order_tab.dart';
