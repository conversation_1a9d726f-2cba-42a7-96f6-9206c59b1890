part of 'tabs.dart';

class ProcedureTabView extends StatelessWidget {
  const ProcedureTabView({super.key});

  @override
  Widget build(BuildContext context) {
    return ViewModelListener<WorkOrderOverviewViewModel,
        WorkOrderOverviewState>(
      listenWhen: (previous, current) =>
          previous.procedureTemplateFetchState !=
          current.procedureTemplateFetchState,
      listener: (context, state) {
        if (state.procedureTemplateFetchState.isFailure) {
          Alert.showError(
            errorMessage: state.procedureTemplateFetchState.error ??
                ExceptionMessage.general,
          );
          return;
        }
        if (state.procedureTemplateFetchState.isLoaded) {
          Navigator.of(context).push(
            SelectProcedureTemplatePage.route(
              viewModel:
                  InhertedViewModel.of<WorkOrderOverviewViewModel>(context)
                      .viewModel,
            ),
          );
        }
      },
      child:
          ViewModelBuilder<WorkOrderOverviewViewModel, WorkOrderOverviewState>(
        builder: (context, state) {
          final workOrder = state.workOrderFetchState.data!;
          if (workOrder.procedures.isEmpty) {
            return EmptyStateView(
              title: context.localization.noProceduresAssignedTitle,
              subtitle: context.localization.noProceduresAssignedMessage,
              child: _AttachProcedureButton(workOrder: workOrder),
            );
          }
          return Scaffold(
            body: CustomScrollView(
              key: const PageStorageKey<String>("procedure-tab"),
              slivers: [
                SliverOverlapInjector(
                  handle:
                      NestedScrollView.sliverOverlapAbsorberHandleFor(context),
                ),
                SliverPadding(
                  padding:
                      EdgeInsets.symmetric(vertical: context.makulaPadding.s),
                  sliver: _ProcedureList(workOrder: workOrder),
                ),
              ],
            ),
            bottomNavigationBar: BottomActionBar(
              child: _AttachProcedureButton(workOrder: workOrder),
            ),
          );
        },
      ),
    );
  }
}

class _ProcedureList extends StatelessWidget {
  const _ProcedureList({required this.workOrder});

  final WorkOrderModel workOrder;

  @override
  Widget build(BuildContext context) {
    return SliverList.separated(
      itemCount: workOrder.procedures.length,
      itemBuilder: (context, index) {
        var item = workOrder.procedures[index];
        return ProcedureCard(
            item: item,
            onTap: () async {
              AnalyticsService.instance.sendEvent(
                "Open Attached Procedure Instance",
                {
                  "work_order_id": workOrder.id,
                  "procedure_id": item.id,
                  'origin': 'wo procedure tab',
                },
              );

              Navigator.push(
                context,
                MaterialPageRoute(
                  settings: const RouteSettings(name: "ProcedureOverview"),
                  builder: (context) => ProcedureOverview(
                    procedureID: item.id,
                    ticketID: workOrder.id,
                    source: workOrder.title,
                    parentAssetId: workOrder.asset?.id,
                  ),
                ),
              );
            });
      },
      separatorBuilder: (context, __) => SizedBox(
        height: context.makulaPadding.xs,
      ),
    );
  }
}

class ProcedureCard extends StatelessWidget {
  const ProcedureCard({
    super.key,
    required this.item,
    this.onTap,
    this.showTrailingIcon = false,
  });

  final ProcedureModel item;
  final VoidCallback? onTap;
  final bool showTrailingIcon;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      highlightColor: context.makulaBackgroundColors.highlight,
      splashColor: context.makulaBackgroundColors.splash,
      child: Ink(
        color: context.makulaBackgroundColors.primary,
        padding: EdgeInsets.all(context.makulaPadding.l),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Stack(
              children: [
                MakulaCardIcon.large(
                  icon: MakulaIcons.procedure,
                  tagColor: context.makulaTagColors.primary,
                  secondaryIcon: item.isDownloaded
                      ? MakulaIcons.offlineDownloadFilled
                      : null,
                ),
              ],
            ),
            SizedBox(width: context.makulaPadding.l),
            Expanded(
              child: Row(
                children: [
                  Flexible(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            Text(
                              "#${item.procedureId ?? ""}",
                              style: context.makulaTypography.body.small
                                  .secondary(context),
                            ),
                          ],
                        ),
                        SizedBox(height: context.makulaPadding.xxs),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            Flexible(
                              child: Text(
                                item.name ?? "",
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                                style: context.makulaTypography.label.large
                                    .primary(context),
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: context.makulaPadding.s),
                        CustomFieldTag.fromTagColor(
                          text: item.stateName ?? "",
                          tagColor: item.isFinalized
                              ? context.makulaTagColors.green
                              : context.makulaTagColors.grey,
                        ),
                      ],
                    ),
                  ),
                  if (showTrailingIcon) ...[
                    SizedBox(width: context.makulaPadding.l),
                    MakulaIcon(
                      MakulaIcons.chevronRight,
                      size: 20,
                      color: context.makulaContentColors.primary,
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _AttachProcedureButton extends StatelessWidget {
  const _AttachProcedureButton({required this.workOrder});

  final WorkOrderModel workOrder;

  @override
  Widget build(BuildContext context) {
    return ViewModelBuilder<WorkOrderOverviewViewModel, WorkOrderOverviewState>(
        buildWhen: (previous, current) =>
            previous.procedureTemplateFetchState !=
            current.procedureTemplateFetchState,
        builder: (context, state) {
          return PrimaryButton.medium(
            leadingIcon: MakulaIcons.add,
            title: context.localization.attachProcedure,
            isLoading: state.procedureTemplateFetchState.isLoading,
            onTap: workOrder.isClosed
                ? null
                : () {
                    AnalyticsService.instance.sendEvent(
                      "Open Attached Procedure Instance",
                      {"work_order_id": workOrder.id},
                    );

                    InhertedViewModel.of<WorkOrderOverviewViewModel>(
                      context,
                    ).viewModel.getProcedureTemplates();
                  },
          );
        });
  }
}
