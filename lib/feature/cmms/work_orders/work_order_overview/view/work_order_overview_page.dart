part of 'view.dart';

class WorkOrderOverviewPage extends StatelessWidget {
  const WorkOrderOverviewPage({
    super.key,
    required this.id,
  });

  static Route route(String workOrderId) => MaterialPageRoute(
        settings: const RouteSettings(name: "TicketOverviewScreen"),
        builder: (context) => WorkOrderOverviewPage(id: workOrderId),
      );

  final String id;

  static String pageName() => ':id';

  static String pageRoute({
    required WorkOrderListTypeModel type,
    required String workOrderId,
  }) =>
      '${type.getRoute()}/${pageName().replaceFirst(':id', workOrderId)}';

  @override
  Widget build(BuildContext context) {
    return ViewModelProvider<WorkOrderOverviewViewModel>(
      create: () => WorkOrderOverviewViewModel(
        id: id,
        workOrderService: get<FeatureModules>()!.workOrderService,
      )
        ..getWorkOrderDetails()
        ..getPaidFeatures()
        ..getCustomFields()
        ..getAttachedParts(),
      child: const WorkOrderOverviewView(),
    );
  }
}
