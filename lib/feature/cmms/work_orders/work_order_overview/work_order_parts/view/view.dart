import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:makula_flutter/core/core.dart';
import 'package:makula_flutter/core/ui/components/alert.dart';
import 'package:makula_flutter/feature/cmms/work_orders/work_order_overview/work_order_overview.dart';
import 'package:makula_flutter/feature/cmms/work_orders/work_order_overview/work_order_part_details/work_order_part_details.dart';
import 'package:makula_flutter/feature/feature_modules.dart';
import 'package:makula_theme/makula_theme.dart';

import '../view_model/work_order_parts_listing_view_model.dart';

part 'work_order_parts_listing_page.dart';
part 'work_order_parts_listing_view.dart';
