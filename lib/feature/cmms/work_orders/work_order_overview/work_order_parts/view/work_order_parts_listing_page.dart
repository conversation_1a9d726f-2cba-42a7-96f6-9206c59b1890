part of 'view.dart';

class WorkOrderPartsListingPage extends StatelessWidget {
  const WorkOrderPartsListingPage({
    super.key,
    required this.assetId,
    required this.workOrderId,
    required this.invertoryParts,
  });

  static Route route({
    required String assetId,
    required String workOrderId,
    required List<PartModel> invertoryParts,
  }) =>
      MaterialPageRoute(
        builder: (context) => WorkOrderPartsListingPage(
          assetId: assetId,
          workOrderId: workOrderId,
          invertoryParts: invertoryParts,
        ),
      );

  final String assetId;
  final String workOrderId;
  final List<PartModel> invertoryParts;

  @override
  Widget build(BuildContext context) {
    return ViewModelProvider(
      create: () => WorkOrderPartsListingViewModel(
        workOrderService: get<FeatureModules>()!.workOrderService,
        invertoryParts: invertoryParts,
        assetId: assetId,
        workOrderId: workOrderId,
      ),
      child: const WorkOrderPartsListingView(),
    );
  }
}
