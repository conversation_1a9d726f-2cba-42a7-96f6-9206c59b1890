part of 'view.dart';

class WorkOrderPartsListingView extends StatelessWidget {
  const WorkOrderPartsListingView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.makulaBackgroundColors.accent,
      appBar: MakulaBasicAppBar(title: context.localization.assignParts),
      body: const _PartList(),
    );
  }
}

class _PartList extends StatelessWidget {
  const _PartList();

  @override
  Widget build(BuildContext context) {
    return ViewModelBuilder<WorkOrderPartsListingViewModel,
        WorkOrderPartsListingState>(
      builder: (context, state) {
        if (state.invertoryParts.isEmpty) {
          return EmptyStateView(
            title: context.localization.noPartsFound,
            subtitle: context.localization.assetHasNoParts,
          );
        }
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: EdgeInsets.symmetric(
                horizontal: context.makulaPadding.l,
                vertical: context.makulaPadding.m,
              ),
              decoration: BoxDecoration(
                color: context.makulaBackgroundColors.primary,
                border: Border(
                  bottom: BorderSide(
                    color: context.makulaBorderColors.primary,
                  ),
                ),
              ),
              child: MakulaSearchField(
                onChanged: InhertedViewModel.of<WorkOrderPartsListingViewModel>(
                        context)
                    .viewModel
                    .onSearch,
              ),
            ),
            Expanded(
              child: (state.parts.isEmpty)
                  ? const NoSearchResultFoundWidget()
                  : ListView.separated(
                      padding: EdgeInsets.symmetric(
                        vertical: context.makulaPadding.xs,
                      ),
                      separatorBuilder: (context, index) =>
                          SizedBox(height: context.makulaPadding.xs),
                      itemCount: state.parts.length,
                      itemBuilder: (context, index) {
                        final part = state.parts[index];
                        return PartCard(part: part);
                      },
                    ),
            ),
            const BottomActionBar(child: _AddSelectedPartsButton()),
          ],
        );
      },
    );
  }
}

class _AddSelectedPartsButton extends StatelessWidget {
  const _AddSelectedPartsButton();

  @override
  Widget build(BuildContext context) {
    return ViewModelListener<WorkOrderPartsListingViewModel,
        WorkOrderPartsListingState>(
      listener: (context, state) {
        if (state.assignPartState.isLoaded) {
          Navigator.of(context).pop();

          Alert.showSuccess(
              message: context.localization.partsAssignedSuccessfully);
        }

        if (state.assignPartState.isFailure) {
          Alert.showError(errorMessage: state.assignPartState.error);
        }
      },
      child: ViewModelBuilder<WorkOrderPartsListingViewModel,
          WorkOrderPartsListingState>(
        builder: (context, state) {
          return PrimaryButton.medium(
            title: context.localization.addSelectedParts,
            isLoading: state.assignPartState.isLoading,
            onTap: state.currentSelectedParts.isNotEmpty
                ? () {
                    InhertedViewModel.of<WorkOrderPartsListingViewModel>(
                            context)
                        .viewModel
                        .saveParts();
                  }
                : null,
          );
        },
      ),
    );
  }
}

class PartCard extends StatelessWidget {
  const PartCard({super.key, required this.part});

  final PartModel part;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        final workOrderId =
            InhertedViewModel.of<WorkOrderPartsListingViewModel>(context)
                .viewModel
                .state
                .workOrderId;
        Navigator.of(context).push(
          WorkOrderPartDetailsPage.route(
            part: part,
            workOrderId: workOrderId,
            canRemove: false,
          ),
        );
      },
      child: Ink(
        padding: EdgeInsetsDirectional.all(context.makulaPadding.l),
        color: context.makulaBackgroundColors.primary,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (part.imageUrl?.isNotEmpty == true)
              ClipRRect(
                clipBehavior: Clip.hardEdge,
                borderRadius: BorderRadius.circular(context.makulaRadius.xxs),
                child: CachedNetworkImage(
                  width: 56,
                  height: 48,
                  imageUrl: part.imageUrl ?? '',
                  fit: BoxFit.cover,
                  placeholder: (context, url) => const MakulaLoader(),
                  errorWidget: (context, url, error) =>
                      const AssetImagePlaceHolder(),
                ),
              )
            else
              const SizedBox(
                height: 48,
                width: 56,
                child: AssetImagePlaceHolder(),
              ),
            SizedBox(width: context.makulaPadding.m),
            Flexible(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Flexible(
                    child: Text(
                      part.name,
                      style:
                          context.makulaTypography.label.large.primary(context),
                    ),
                  ),
                  SizedBox(height: context.makulaPadding.xxs),
                  Flexible(
                    child: Text(
                      part.articleNumber,
                      style: context.makulaTypography.body.small
                          .secondary(context),
                    ),
                  ),
                  SizedBox(height: context.makulaPadding.m),
                  ViewModelBuilder<WorkOrderPartsListingViewModel,
                      WorkOrderPartsListingState>(
                    builder: (context, state) {
                      return Row(
                        children: [
                          const Spacer(),
                          _Counter(
                            quantity: state.getPartQuantity(part.id),
                            onAdd: () => InhertedViewModel.of<
                                    WorkOrderPartsListingViewModel>(context)
                                .viewModel
                                .onAddPart(part),
                            onRemove: () => InhertedViewModel.of<
                                    WorkOrderPartsListingViewModel>(context)
                                .viewModel
                                .onRemovePart(part),
                          ),
                        ],
                      );
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _Counter extends StatelessWidget {
  const _Counter({
    required this.onAdd,
    required this.onRemove,
    required this.quantity,
  });

  final VoidCallback onAdd;
  final VoidCallback onRemove;
  final int quantity;

  @override
  Widget build(BuildContext context) {
    return quantity == 0
        ? OutlineButton.smallIcon(
            icon: MakulaIcons.add,
            onTap: onAdd,
          )
        : Row(
            children: [
              OutlineButton.smallIcon(
                icon: quantity == 1 ? MakulaIcons.delete : MakulaIcons.minus,
                onTap: onRemove,
              ),
              SizedBox(
                width: 40,
                child: Center(
                  child: Text(
                    quantity.toString(),
                    style: context.makulaTypography.body.medium.copyWith(
                      color: context.makulaContentColors.brand,
                    ),
                  ),
                ),
              ),
              OutlineButton.smallIcon(
                icon: MakulaIcons.add,
                onTap: onAdd,
              ),
            ],
          );
  }
}
