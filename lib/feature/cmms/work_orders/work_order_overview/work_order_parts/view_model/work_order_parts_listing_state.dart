part of 'work_order_parts_listing_view_model.dart';

typedef AssignPartToWorkOrderApiState = GeneralResponseState<void>;

class WorkOrderPartsListingState extends Equatable {
  final String assetId;
  final String workOrderId;
  final AssignPartToWorkOrderApiState assignPartState;
  final String search;
  final List<PartModel> currentSelectedParts;
  final List<PartModel> invertoryParts;

  const WorkOrderPartsListingState({
    required this.assetId,
    required this.workOrderId,
    required this.assignPartState,
    required this.search,
    required this.currentSelectedParts,
    required this.invertoryParts,
  });

  const WorkOrderPartsListingState.initial({
    required this.assetId,
    required this.workOrderId,
    this.search = '',
    this.assignPartState = const AssignPartToWorkOrderApiState(),
    this.currentSelectedParts = const [],
    this.invertoryParts = const [],
  });

  List<PartModel> get parts => invertoryParts
      .where(
        (e) =>
            e.name.toLowerCase().contains(search.toLowerCase()) ||
            e.articleNumber.toLowerCase().contains(search.toLowerCase()),
      )
      .toList();

  bool isPartSelected(PartModel part) =>
      currentSelectedParts.any((element) => element.id == part.id);

  int getPartQuantity(String partId) {
    final part = currentSelectedParts.firstWhereOrNull(
      (element) => element.id == partId,
    );
    return part?.quantity ?? 0;
  }

  WorkOrderPartsListingState copyWith({
    AssignPartToWorkOrderApiState? assignPartState,
    List<PartModel>? currentSelectedParts,
    String? search,
  }) {
    return WorkOrderPartsListingState(
      assetId: assetId,
      invertoryParts: invertoryParts,
      workOrderId: workOrderId,
      search: search ?? this.search,
      assignPartState: assignPartState ?? this.assignPartState,
      currentSelectedParts: currentSelectedParts ?? this.currentSelectedParts,
    );
  }

  @override
  List<Object?> get props => [
        search,
        assignPartState,
        currentSelectedParts,
        invertoryParts,
      ];
}
