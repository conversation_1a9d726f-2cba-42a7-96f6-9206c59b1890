import 'package:equatable/equatable.dart';
import 'package:makula_flutter/core/core.dart';
import 'package:makula_flutter/core/utils/utils.dart';
import 'package:makula_flutter/feature/cmms/work_orders/work_order_overview/work_order_overview.dart';
import 'package:makula_flutter/service/service.dart';
import 'package:makula_flutter/service/work_order/work_order_service.dart';

part 'work_order_parts_listing_state.dart';

class WorkOrderPartsListingViewModel
    extends ObservableViewModel<WorkOrderPartsListingState> {
  WorkOrderPartsListingViewModel({
    required WorkOrderService workOrderService,
    required String assetId,
    required String workOrderId,
    required List<PartModel> invertoryParts,
  })  : _workOrderService = workOrderService,
        super(WorkOrderPartsListingState.initial(
          assetId: assetId,
          workOrderId: workOrderId,
          invertoryParts: invertoryParts,
        ));

  final WorkOrderService _workOrderService;

  Future<void> saveParts() async {
    updateState(
      state.copyWith(
        assignPartState: state.assignPartState.toLoading(),
      ),
    );

    try {
      await _workOrderService.savePartsFor(
        state.workOrderId,
        state.assetId,
        state.currentSelectedParts.map((e) => e.toServiceModel()).toList(),
      );

      updateState(
        state.copyWith(
          assignPartState: state.assignPartState.toLoaded(),
        ),
      );
    } on NoInternetException catch (_) {
      updateState(
        state.copyWith(
          assignPartState: state.assignPartState.toFailure(
            error: ExceptionMessage.noInternet,
          ),
        ),
      );
    } catch (error) {
      updateState(
        state.copyWith(
          assignPartState: state.assignPartState.toFailure(
            error: ExceptionMessage.general,
          ),
        ),
      );
    }
  }

  void onAddPart(PartModel part) {
    if (state.isPartSelected(part)) {
      final quantity = state.getPartQuantity(part.id);
      updatePartQuantity(part, quantity + 1);
      return;
    }
    updateState(
      state.copyWith(
        currentSelectedParts: [
          ...state.currentSelectedParts,
          part.copyWith(quantity: 1)
        ],
      ),
    );
  }

  void onRemovePart(PartModel part) {
    if (state.isPartSelected(part) && state.getPartQuantity(part.id) > 1) {
      final quantity = state.getPartQuantity(part.id);
      updatePartQuantity(part, quantity - 1);
      return;
    }

    final mList = List<PartModel>.from(state.currentSelectedParts);

    mList.removeWhere((element) => element.id == part.id);

    updateState(
      state.copyWith(currentSelectedParts: mList),
    );
  }

  void updatePartQuantity(PartModel part, int count) {
    final mList = List<PartModel>.from(state.currentSelectedParts);

    mList.removeWhere((element) => element.id == part.id);

    mList.add(part.copyWith(quantity: count));

    updateState(
      state.copyWith(currentSelectedParts: mList),
    );
  }

  void onSearch(String search) {
    updateState(state.copyWith(search: search));
  }
}
