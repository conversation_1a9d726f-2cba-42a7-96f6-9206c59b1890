part of 'view.dart';

class WorkOrderPartDetailsPage extends StatelessWidget {
  const WorkOrderPartDetailsPage({
    super.key,
    required this.part,
    required this.canRemove,
    this.onRemove,
    this.workOrderId,
    this.isRequest = false,
  });

  static Route route({
    required PartModel part,
    required bool canRemove,
    bool isRequest = false,
    String? workOrderId,
    VoidCallback? onRemove,
  }) {
    AnalyticsService.instance.sendEvent(
      "View Work Order Part Details",
      {
        "work_order_id": workOrderId,
        "part_id": part.id,
      },
    );
    return MaterialPageRoute(
      builder: (context) => WorkOrderPartDetailsPage(
        part: part,
        canRemove: canRemove,
        isRequest: isRequest,
        workOrderId: workOrderId,
        onRemove: onRemove,
      ),
    );
  }

  final PartModel part;
  final String? workOrderId;
  final bool canRemove;
  final bool isRequest;
  final VoidCallback? onRemove;

  @override
  Widget build(BuildContext context) {
    return ViewModelProvider(
      create: () => WorkOrderPartDetailsViewModel(
        workOrderService: get<FeatureModules>()!.workOrderService,
        part: part,
        isRequest: isRequest,
        workOrderId: workOrderId ?? "",
        canRemove: canRemove,
        onRemove: onRemove,
      )..getPartCustomFields(),
      child: const WorkOrderPartDetailsView(),
    );
  }
}
