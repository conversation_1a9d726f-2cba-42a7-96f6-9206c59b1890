part of 'view.dart';

class WorkOrderPartDetailsView extends StatelessWidget {
  const WorkOrderPartDetailsView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.makulaBackgroundColors.accent,
      appBar: AppBar(
        title: Text(
          context.localization.partDetails,
          style: context.makulaTypography.headline.large.primary(context),
        ),
        backgroundColor: context.makulaBackgroundColors.primary,
        surfaceTintColor: context.makulaBackgroundColors.primary,
        leading: IconButton(
          icon: const MakulaIcon(MakulaIcons.back, size: 24),
          onPressed: Navigator.of(context).pop,
        ),
      ),
      body: ViewModelBuilder<WorkOrderPartDetailsViewModel,
          WorkOrderPartDetailsState>(
        builder: (context, state) {
          if (state.fetchPartCustomFieldsState.isLoading) {
            return const Center(child: <PERSON><PERSON><PERSON><PERSON>oa<PERSON>());
          }
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: _PartDetails(
                  part: state.part,
                  customFields: state.customFields,
                ),
              ),
              if (state.canRemove) const _BottomBar(),
            ],
          );
        },
      ),
    );
  }
}

class _BottomBar extends StatelessWidget {
  const _BottomBar();

  @override
  Widget build(BuildContext context) {
    return ViewModelListener<WorkOrderPartDetailsViewModel,
        WorkOrderPartDetailsState>(
      listenWhen: (previous, current) =>
          previous.removePartState != current.removePartState,
      listener: (context, state) {
        if (state.removePartState.isFailure) {
          Alert.showError(errorMessage: state.removePartState.error);
        } else if (state.removePartState.isLoaded) {
          Navigator.pop(context);
        }
      },
      child: ViewModelBuilder<WorkOrderPartDetailsViewModel,
          WorkOrderPartDetailsState>(
        builder: (context, state) {
          return BottomActionBar(
            child: PrimaryButton.medium(
              title: context.localization.removePart,
              backgroundColor: context.makulaBackgroundColors.danger,
              isLoading: state.removePartState.isLoading,
              onTap: state.removePartState.isLoaded
                  ? null
                  : InhertedViewModel.of<WorkOrderPartDetailsViewModel>(context)
                      .viewModel
                      .removePart,
            ),
          );
        },
      ),
    );
  }
}

class _PartDetails extends StatelessWidget {
  const _PartDetails({required this.part, required this.customFields});

  final PartModel part;
  final List<CustomFieldModel> customFields;

  @override
  Widget build(BuildContext context) {
    return ListView(
      padding: EdgeInsets.symmetric(vertical: context.makulaPadding.xs),
      children: [
        DataListItem(
          title: context.localization.partName,
          subtitle: part.name,
        ),
        Divider(height: 1, color: context.makulaBorderColors.primary),
        DataListItem(
          title: context.localization.articleNumber,
          subtitle: part.articleNumber,
        ),
        Divider(height: 1, color: context.makulaBorderColors.primary),
        if (part.quantity > 0) ...[
          DataListItem(
            title: context.localization.assignedQuantity,
            subtitle: part.quantity.toString(),
          ),
          Divider(height: 1, color: context.makulaBorderColors.primary),
        ],
        SizedBox(height: context.makulaPadding.xs),
        DetailListItem(
          title: context.localization.description,
          icon: MakulaIcons.leftAlign,
          child: Container(
            padding: EdgeInsetsDirectional.only(
              bottom: context.makulaPadding.m,
              start: context.makulaPadding.l,
              end: context.makulaPadding.l,
            ),
            alignment: AlignmentDirectional.topStart,
            child: Text(
              part.description?.isNotEmpty == true
                  ? part.description!
                  : context.localization.noDescriptionAvailable,
              style: context.makulaTypography.body.large.primary(context),
            ),
          ),
        ),
        SizedBox(height: context.makulaPadding.xs),
        if (customFields.isNotEmpty)
          DetailListItem(
            title: context.localization.customFields,
            icon: MakulaIcons.customField,
            child: MakulaCustomFields(customFields: customFields),
          ),
      ],
    );
  }
}
