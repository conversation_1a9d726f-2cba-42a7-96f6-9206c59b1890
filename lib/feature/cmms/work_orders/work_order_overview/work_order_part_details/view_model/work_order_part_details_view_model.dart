import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:makula_flutter/core/core.dart';
import 'package:makula_flutter/core/utils/utils.dart';
import 'package:makula_flutter/feature/cmms/work_orders/work_order_overview/work_order_overview.dart';
import 'package:makula_flutter/service/service.dart';
import 'package:makula_flutter/service/work_order/work_order_service.dart';

part 'work_order_part_details_state.dart';

class WorkOrderPartDetailsViewModel
    extends ObservableViewModel<WorkOrderPartDetailsState> {
  WorkOrderPartDetailsViewModel({
    required WorkOrderService workOrderService,
    required PartModel part,
    required String workOrderId,
    required bool canRemove,
    required bool isRequest,
    this.onRemove,
  })  : _workOrderService = workOrderService,
        super(
          WorkOrderPartDetailsState.initial(
            workOrderId: workOrderId,
            part: part,
            canRemove: canRemove,
            isRequest: isRequest,
          ),
        );

  final WorkOrderService _workOrderService;
  final VoidCallback? onRemove;

  Future<void> removePart() async {
    if (onRemove != null) {
      onRemove!();
      return;
    }

    updateState(
      state.copyWith(
        removePartState: state.removePartState.toLoading(),
      ),
    );

    try {
      await _workOrderService.removePart(state.part.id, state.workOrderId);

      updateState(
        state.copyWith(
          removePartState: state.removePartState.toLoaded(),
        ),
      );
    } on NoInternetException catch (_) {
      updateState(
        state.copyWith(
          removePartState: state.removePartState.toFailure(
            error: ExceptionMessage.noInternet,
          ),
        ),
      );
    } catch (error) {
      updateState(
        state.copyWith(
          removePartState: state.removePartState.toFailure(
            error: ExceptionMessage.general,
          ),
        ),
      );
    }
  }

  Future<void> getPartCustomFields() async {
    updateState(
      state.copyWith(
        fetchPartCustomFieldsState:
            state.fetchPartCustomFieldsState.toLoading(),
      ),
    );

    try {
      final fields = state.isRequest
          ? await _workOrderService.fetchSharedPartCustomFields(state.part.id)
          : await _workOrderService.fetchPartCustomFields(state.part.id);

      updateState(
        state.copyWith(
          fetchPartCustomFieldsState: state.fetchPartCustomFieldsState.toLoaded(
            data: fields
                .map((e) => CustomFieldModel.fromServiceModel(e))
                .toList(),
          ),
        ),
      );
    } on NoInternetException catch (_) {
      updateState(
        state.copyWith(
          fetchPartCustomFieldsState:
              state.fetchPartCustomFieldsState.toFailure(
            error: ExceptionMessage.noInternet,
          ),
        ),
      );
    } catch (error) {
      updateState(
        state.copyWith(
          fetchPartCustomFieldsState:
              state.fetchPartCustomFieldsState.toFailure(
            error: ExceptionMessage.general,
          ),
        ),
      );
    }
  }
}
