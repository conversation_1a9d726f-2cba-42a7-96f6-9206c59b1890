part of 'work_order_part_details_view_model.dart';

typedef RemoveWorkOrderPartState = GeneralResponseState<void>;
typedef FetchPartCustomFieldsState
    = GeneralResponseState<List<CustomFieldModel>>;

class WorkOrderPartDetailsState extends Equatable {
  final bool canRemove;
  final bool isRequest;
  final PartModel part;
  final String workOrderId;
  final RemoveWorkOrderPartState removePartState;
  final FetchPartCustomFieldsState fetchPartCustomFieldsState;

  const WorkOrderPartDetailsState({
    required this.isRequest,
    required this.canRemove,
    required this.part,
    required this.workOrderId,
    required this.removePartState,
    required this.fetchPartCustomFieldsState,
  });

  const WorkOrderPartDetailsState.initial({
    required this.isRequest,
    required this.canRemove,
    required this.part,
    required this.workOrderId,
    this.removePartState = const RemoveWorkOrderPartState(),
    this.fetchPartCustomFieldsState = const FetchPartCustomFieldsState(),
  });

  WorkOrderPartDetailsState copyWith({
    RemoveWorkOrderPartState? removePartState,
    FetchPartCustomFieldsState? fetchPartCustomFieldsState,
  }) {
    return WorkOrderPartDetailsState(
      canRemove: canRemove,
      isRequest: isRequest,
      part: part,
      workOrderId: workOrderId,
      removePartState: removePartState ?? this.removePartState,
      fetchPartCustomFieldsState:
          fetchPartCustomFieldsState ?? this.fetchPartCustomFieldsState,
    );
  }

  List<CustomFieldModel> get customFields =>
      fetchPartCustomFieldsState.data ?? [];

  @override
  List<Object?> get props => [
        part,
        canRemove,
        workOrderId,
        removePartState,
        isRequest,
        fetchPartCustomFieldsState,
      ];
}
