import 'dart:async';

import 'package:equatable/equatable.dart';
import 'package:makula_flutter/core/core.dart';
import 'package:makula_flutter/core/utils/utils.dart';
import 'package:makula_flutter/feature/connections/connection_details/model/model.dart';
import 'package:makula_flutter/feature/feature.dart';
import 'package:makula_flutter/service/connection/connection_service.dart';
import 'package:makula_flutter/service/exceptions.dart';
import 'package:makula_flutter/service/oem/oem_service.dart';

part 'connection_details_state.dart';

class ConnectionDetailsViewModel
    extends ObservableViewModel<ConnectionDetailsState> {
  ConnectionDetailsViewModel({
    required String connectionId,
    required ConnectionService connectionService,
    required OemService oemService,
  })  : _connectionService = connectionService,
        _oemService = oemService,
        super(ConnectionDetailsState.initial(connectionId: connectionId));

  final ConnectionService _connectionService;
  final OemService _oemService;

  StreamSubscription<List<ConnectionAssetModel>>? _assetsSubscription;
  StreamSubscription<List<CustomFieldModel>>? _customFieldSubscription;

  @override
  void dispose() {
    _customFieldSubscription?.cancel();
    _assetsSubscription?.cancel();
    super.dispose();
  }

  void initialize() {
    _assetsSubscription?.cancel();
    _assetsSubscription = _connectionService
        .getAssets(state.connectionId)
        .map((assets) =>
            assets.map(ConnectionAssetModel.fromServiceModel).toList())
        .listen((assets) {
      updateState(
        state.copyWith(
          fetchPaginatedAssetsState: state.fetchPaginatedAssetsState.copyWith(
            apiState: state.fetchPaginatedAssetsState.apiState.toLoaded(
              data: assets,
            ),
          ),
        ),
      );
    });

    _customFieldSubscription?.cancel();
    _customFieldSubscription = _connectionService
        .getCustomFields(state.connectionId)
        .map(
          (fields) =>
              fields.map((e) => CustomFieldModel.fromServiceModel(e)).toList(),
        )
        .listen((fields) {
      updateState(
        state.copyWith(
          customFieldsFetchState: state.customFieldsFetchState.toLoaded(
            data: fields,
          ),
        ),
      );
    });
  }

  Future<void> getConnectionDetails() async {
    updateState(
      state.copyWith(
        connectionDetailFetchState:
            state.connectionDetailFetchState.toLoading(),
      ),
    );

    try {
      final connection = await _connectionService.getConnectionDetail(
        state.connectionId,
      );
      final model = ConnectionDetailModel.fromServiceModel(connection);

      updateState(
        state.copyWith(
          connectionDetailFetchState: state.connectionDetailFetchState.toLoaded(
            data: model,
          ),
          lastSyncDuration: model.lastSyncDuration,
        ),
      );
    } on NoInternetException {
      updateState(
        state.copyWith(
          connectionDetailFetchState:
              state.connectionDetailFetchState.toFailure(
            error: ExceptionMessage.noInternet,
          ),
        ),
      );
    } catch (e) {
      updateState(
        state.copyWith(
          connectionDetailFetchState:
              state.connectionDetailFetchState.toFailure(
            error: ExceptionMessage.general,
          ),
        ),
      );
    }
  }

  Future<void> getCustomFields() async {
    updateState(
      state.copyWith(
        customFieldsFetchState: state.customFieldsFetchState.toLoading(),
      ),
    );

    try {
      final fields =
          await _connectionService.fetchCustomFields(state.connectionId);

      updateState(
        state.copyWith(
          customFieldsFetchState: state.customFieldsFetchState.toLoaded(
            data: fields.map(CustomFieldModel.fromServiceModel).toList(),
          ),
        ),
      );
    } catch (e) {
      updateState(
        state.copyWith(
          customFieldsFetchState: state.customFieldsFetchState.toFailure(
            error: e.toString(),
          ),
        ),
      );
    }
  }

  Future<void> fetchAssets({bool forceRefresh = false}) async {
    updateState(
      state.copyWith(
        fetchPaginatedAssetsState: state.fetchPaginatedAssetsState.copyWith(
          apiState: state.fetchPaginatedAssetsState.apiState.toLoading(),
        ),
      ),
    );

    try {
      final hasMoreData = await _connectionService.fetchConnectionAssets(
        state.connectionId,
        forceRefresh: forceRefresh,
      );

      updateState(
        state.copyWith(
          fetchPaginatedAssetsState: state.fetchPaginatedAssetsState.copyWith(
            apiState: state.fetchPaginatedAssetsState.apiState.toLoaded(),
            hasMoreData: hasMoreData,
          ),
        ),
      );
    } on NoInternetException {
      updateState(
        state.copyWith(
          fetchPaginatedAssetsState: state.fetchPaginatedAssetsState.copyWith(
            apiState: state.fetchPaginatedAssetsState.apiState.toFailure(
              error: ExceptionMessage.noInternet,
            ),
          ),
        ),
      );
    } catch (e) {
      updateState(
        state.copyWith(
          fetchPaginatedAssetsState: state.fetchPaginatedAssetsState.copyWith(
            apiState: state.fetchPaginatedAssetsState.apiState.toFailure(
              error: ExceptionMessage.general,
            ),
          ),
        ),
      );
    }
  }

  void toggleHideEmptyCustomFields() {
    updateState(
      state.copyWith(hideEmptyCustomFields: !state.hideEmptyCustomFields),
    );
  }

  Future<void> updateCustomField(
    List<CustomFieldModel> editedFields,
  ) async {
    await _connectionService.updateCustomField(
      connectionId: state.connectionId,
      fields: editedFields
          .map((field) => CustomFieldRequest(id: field.id, value: field.value))
          .toList(),
    );
  }

  Future<void> downloadAllAssetsOffline() async {
    final strategy = ServiceRequestHandlingStrategy(
      request: () async => _connectionService.downloadAllAssetsOffline(
        state.connectionId,
      ),
      onChange: (responseState) {
        updateState(
          state.copyWith(
            downloadAllAssetsOfflineState: responseState,
            lastSyncDuration: const Duration(seconds: 2),
          ),
        );
      },
    );

    await strategy.execute();
  }

  Future<void> isDeepOfflineFunctionalityEnabled() async {
    final strategy = ServiceRequestHandlingStrategy<bool>(
      request: () async {
        final oem = await _oemService.getOem();
        return oem.deepOfflineFunctionalityEnabled;
      },
      onChange: (responseState) {
        updateState(
          state.copyWith(deepUploadAccessState: responseState),
        );
      },
    );

    await strategy.execute();
  }
}
