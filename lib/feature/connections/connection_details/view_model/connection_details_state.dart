part of 'connection_details_view_model.dart';

typedef ConnectionDetailFetchState
    = GeneralResponseState<ConnectionDetailModel>;
typedef CustomFieldsFetchState = GeneralResponseState<List<CustomFieldModel>>;
typedef FetchPaginatedAssetsState
    = PaginatedResponseState<ConnectionAssetModel>;
typedef DownloadAllAssetsOfflineState = GeneralResponseState<void>;
typedef DeepUploadAccessState = GeneralResponseState<bool>;

class ConnectionDetailsState extends Equatable {
  final String connectionId;
  final ConnectionDetailFetchState connectionDetailFetchState;
  final CustomFieldsFetchState customFieldsFetchState;
  final FetchPaginatedAssetsState fetchPaginatedAssetsState;
  final DownloadAllAssetsOfflineState downloadAllAssetsOfflineState;
  final DeepUploadAccessState deepUploadAccessState;
  final bool hideEmptyCustomFields;
  final Duration? lastSyncDuration;

  const ConnectionDetailsState({
    required this.connectionId,
    required this.connectionDetailFetchState,
    required this.customFieldsFetchState,
    required this.fetchPaginatedAssetsState,
    required this.downloadAllAssetsOfflineState,
    required this.deepUploadAccessState,
    required this.hideEmptyCustomFields,
    this.lastSyncDuration,
  });

  factory ConnectionDetailsState.initial({required String connectionId}) =>
      ConnectionDetailsState(
        connectionId: connectionId,
        connectionDetailFetchState: const ConnectionDetailFetchState(),
        customFieldsFetchState: const CustomFieldsFetchState(),
        fetchPaginatedAssetsState: FetchPaginatedAssetsState.initial(),
        downloadAllAssetsOfflineState: const DownloadAllAssetsOfflineState(),
        deepUploadAccessState: const DeepUploadAccessState(),
        hideEmptyCustomFields: true,
        lastSyncDuration: null,
      );

  ConnectionDetailsState copyWith({
    ConnectionDetailFetchState? connectionDetailFetchState,
    CustomFieldsFetchState? customFieldsFetchState,
    FetchPaginatedAssetsState? fetchPaginatedAssetsState,
    DownloadAllAssetsOfflineState? downloadAllAssetsOfflineState,
    DeepUploadAccessState? deepUploadAccessState,
    bool? hideEmptyCustomFields,
    Duration? lastSyncDuration,
  }) {
    return ConnectionDetailsState(
      connectionId: connectionId,
      connectionDetailFetchState:
          connectionDetailFetchState ?? this.connectionDetailFetchState,
      downloadAllAssetsOfflineState:
          downloadAllAssetsOfflineState ?? this.downloadAllAssetsOfflineState,
      customFieldsFetchState:
          customFieldsFetchState ?? this.customFieldsFetchState,
      fetchPaginatedAssetsState:
          fetchPaginatedAssetsState ?? this.fetchPaginatedAssetsState,
      hideEmptyCustomFields:
          hideEmptyCustomFields ?? this.hideEmptyCustomFields,
      deepUploadAccessState:
          deepUploadAccessState ?? this.deepUploadAccessState,
      lastSyncDuration: lastSyncDuration ?? this.lastSyncDuration,
    );
  }

  bool get isOffline => connectionDetailFetchState.data?.isOffline ?? false;

  bool get canDeepUpload => (deepUploadAccessState.data ?? false) && !isOffline;

  String get lastSyncDurationText {
    if (lastSyncDuration == null) return "";

    final difference = lastSyncDuration!;
    return difference.inTimePassed;
  }

  List<CustomFieldModel> get customFields =>
      (customFieldsFetchState.data ?? []);

  bool get showEmptyCustomFields => !hideEmptyCustomFields;

  List<ConnectionAssetModel> get assets => fetchPaginatedAssetsState.data;

  List<ConnectionAssetModel> get assetsPreview => assets.take(3).toList();

  @override
  List<Object?> get props => [
        connectionDetailFetchState,
        customFieldsFetchState,
        downloadAllAssetsOfflineState,
        fetchPaginatedAssetsState,
        deepUploadAccessState,
        hideEmptyCustomFields,
      ];
}
