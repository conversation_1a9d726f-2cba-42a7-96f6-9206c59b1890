import 'dart:math';

import 'package:flutter/material.dart';
import 'package:makula_flutter/core/core.dart';
import 'package:makula_flutter/feature/cmms/work_orders/work_order_overview/procedure/procedure_overview/widgets/generic_two_button_bottom_sheet.dart';
import 'package:makula_flutter/feature/connections/connection_details/model/model.dart';
import 'package:makula_flutter/feature/connections/connection_details/view_model/connection_details_view_model.dart';
import 'package:makula_flutter/feature/connections/connections_list/connection_list.dart';
import 'package:makula_flutter/feature/feature_modules.dart';
import 'package:makula_flutter/service/analytics/analytics_service.dart';
import 'package:makula_theme/makula_theme.dart';

import 'tabs/tabs.dart';

part 'connection_details_page.dart';
part 'connection_details_view.dart';
