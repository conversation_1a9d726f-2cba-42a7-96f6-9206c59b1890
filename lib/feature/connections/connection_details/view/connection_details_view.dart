part of 'view.dart';

class ConnectionDetailsView extends StatelessWidget {
  const ConnectionDetailsView({super.key});

  @override
  Widget build(BuildContext context) {
    return ViewModelBuilder<ConnectionDetailsViewModel, ConnectionDetailsState>(
      builder: (context, state) {
        if (state.connectionDetailFetchState.isLoading) {
          return const Scaffold(
            body: Center(child: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>()),
          );
        }

        if (state.connectionDetailFetchState.isFailure) {
          return Scaffold(
            body: GeneralErrorWidget(
              message: state.connectionDetailFetchState.error,
              onRetry: () =>
                  InhertedViewModel.of<ConnectionDetailsViewModel>(context)
                      .viewModel
                      .getConnectionDetails(),
            ),
          );
        }

        final connection = state.connectionDetailFetchState.data!;

        return PopScope(
          canPop: !state.downloadAllAssetsOfflineState.isLoading,
          onPopInvokedWithResult: (didPop, result) {
            if (state.downloadAllAssetsOfflineState.isLoading && !didPop) {
              showModalBottomSheet(
                context: context,
                builder: (_) {
                  return const DownloadInProgressSheet();
                },
              );
              return;
            }
          },
          child: Scaffold(
            backgroundColor: context.makulaBackgroundColors.accent,
            resizeToAvoidBottomInset: false,
            body: _Body(
              connection: connection,
              isDownloading: state.downloadAllAssetsOfflineState.isLoading,
            ),
          ),
        );
      },
    );
  }
}

class _Body extends StatelessWidget {
  const _Body({
    required this.connection,
    required this.isDownloading,
  });

  final ConnectionDetailModel connection;
  final bool isDownloading;

  static const _imageWidth = 48.0;
  static const _imageHeight = 48.0;

  double calculateFlexibleSpaceHeight(
    BuildContext context,
    String title,
    String subtitle,
  ) {
    final spacerHeight = context.makulaPadding.xxs;

    final titlePainter = TextPainter(
      text: TextSpan(
        text: title,
        style: context.makulaTypography.headline.large.primary(context),
      ),
      textDirection: TextDirection.ltr,
    )..layout(maxWidth: MediaQuery.of(context).size.width - 32 - _imageWidth);

    final subtitlePainter = TextPainter(
      text: TextSpan(
        text: subtitle,
        style: context.makulaTypography.body.small,
      ),
      textDirection: TextDirection.ltr,
    )..layout(maxWidth: MediaQuery.of(context).size.width - 32 - _imageWidth);

    return max(
      _imageHeight,
      titlePainter.size.height +
          subtitlePainter.size.height +
          spacerHeight +
          12,
    );
  }

  @override
  Widget build(BuildContext context) {
    const tabs = ConnectionDetailsTab.values;

    return DefaultTabController(
      length: tabs.length,
      child: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
            SliverOverlapAbsorber(
              handle: NestedScrollView.sliverOverlapAbsorberHandleFor(context),
              sliver: SliverAppBar.medium(
                automaticallyImplyLeading: false,
                backgroundColor: context.makulaBackgroundColors.primary,
                surfaceTintColor: context.makulaBackgroundColors.primary,
                leading: IconButton(
                  icon: const MakulaIcon(
                    MakulaIcons.back,
                    size: 24,
                  ),
                  onPressed: () {
                    if (isDownloading) {
                      showModalBottomSheet(
                        context: context,
                        builder: (_) {
                          return const DownloadInProgressSheet();
                        },
                      );
                      return;
                    }
                    Navigator.of(context).pop();
                  },
                ),
                expandedHeight: kToolbarHeight +
                    context.makulaPadding.l +
                    kTextTabBarHeight +
                    calculateFlexibleSpaceHeight(
                      context,
                      connection.name,
                      connection.id,
                    ),
                actions: [
                  MenuButtonWidget(connection: connection),
                ],
                floating: false,
                pinned: true,
                forceElevated: innerBoxIsScrolled,
                title: Text(
                  connection.name,
                  style:
                      context.makulaTypography.headline.large.primary(context),
                ),
                flexibleSpace: FlexibleSpaceBar(
                  background: Align(
                    alignment: AlignmentDirectional.bottomStart,
                    child: Padding(
                      padding: EdgeInsets.symmetric(
                        horizontal: context.makulaPadding.l,
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              MakulaCardIcon.large(
                                icon: MakulaIcons.facility,
                                tagColor: context.makulaTagColors.blue,
                              ),
                              SizedBox(width: context.makulaPadding.m),
                              Flexible(
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Flexible(
                                      child: Text(
                                        connection.name,
                                        style: context
                                            .makulaTypography.label.large
                                            .primary(context),
                                      ),
                                    ),
                                    SizedBox(height: context.makulaPadding.xxs),
                                    Flexible(
                                      child: Text(
                                        connection.displayId,
                                        style: context
                                            .makulaTypography.body.small
                                            .secondary(context),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: context.makulaPadding.l),
                          const SizedBox(height: kTextTabBarHeight),
                        ],
                      ),
                    ),
                  ),
                ),
                bottom: const _TabBar(),
              ),
            ),
          ];
        },
        body: TabBarView(
          children: tabs.map((tab) => tab.getTabView(connection)).toList(),
        ),
      ),
    );
  }
}

class MenuButtonWidget extends StatelessWidget {
  const MenuButtonWidget({
    super.key,
    required this.connection,
  });

  final ConnectionDetailModel connection;

  @override
  Widget build(BuildContext context) {
    return ViewModelBuilder<ConnectionDetailsViewModel, ConnectionDetailsState>(
      builder: (context, state) {
        if (state.canDeepUpload) {
          return PopupMenuButton<String>(
            color: context.makulaBackgroundColors.primary,
            iconColor: context.makulaContentColors.primary,
            icon: MakulaIcon(
              MakulaIcons.more,
              size: 24,
              color: context.makulaContentColors.primary,
            ),
            onOpened: () {
              AnalyticsService.instance.sendEvent(
                "Connection Details More Button Clicked",
                {
                  "connectionId": connection.id,
                  "connectionName": connection.name,
                },
              );
            },
            onSelected: (value) {
              if (value == 'downloadOffline') {
                InhertedViewModel.of<ConnectionDetailsViewModel>(context)
                    .viewModel
                    .downloadAllAssetsOffline();
              }
            },
            itemBuilder: (context) => [
              PopupMenuItem<String>(
                value: 'downloadOffline',
                enabled: !state.downloadAllAssetsOfflineState.isLoading,
                child: Text(
                  context.localization.downloadOffline,
                  style: context.makulaTypography.body.medium.primary(context),
                ),
              ),
            ],
          );
        }
        return const SizedBox.shrink();
      },
    );
  }
}

class _TabBar extends StatelessWidget implements PreferredSizeWidget {
  const _TabBar();

  @override
  Widget build(BuildContext context) {
    return Material(
      color: context.makulaBackgroundColors.primary,
      child: TabBar(
        dividerHeight: 1.0,
        dividerColor: context.makulaBorderColors.primary,
        overlayColor: WidgetStateProperty.all(
          context.makulaBackgroundColors.accent.withTransparency25,
        ),
        indicatorSize: TabBarIndicatorSize.tab,
        labelStyle: context.makulaTypography.label.medium.primary(context),
        unselectedLabelStyle:
            context.makulaTypography.label.medium.secondary(context),
        indicatorColor: context.makulaBorderColors.brand,
        labelPadding:
            EdgeInsets.symmetric(horizontal: context.makulaPadding.xl),
        tabs: ConnectionDetailsTab.values
            .map((tab) => Tab(text: tab.title(context.localization)))
            .toList(),
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kTextTabBarHeight);
}

class ConnectionSyncBannerWidget extends StatelessWidget {
  const ConnectionSyncBannerWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return ViewModelBuilder<ConnectionDetailsViewModel, ConnectionDetailsState>(
      builder: (context, state) {
        if (state.downloadAllAssetsOfflineState.isLoading) {
          return MakulaBanner(
            text: context.localization.downloading,
            onTap: () {},
            action: const SizedBox.square(
              dimension: 12,
              child: CircularProgressIndicator(strokeWidth: 2),
            ),
          );
        }

        if (state.downloadAllAssetsOfflineState.isFailure) {
          return MakulaBanner(
            text: context.localization.syncFailed,
            onTap: () {
              InhertedViewModel.of<ConnectionDetailsViewModel>(context)
                  .viewModel
                  .downloadAllAssetsOffline();
            },
            backgroundColor: context.makulaBackgroundColors.dangerLighest,
            foregroundColor: context.makulaContentColors.danger,
            action: GestureDetector(
              onTap: InhertedViewModel.of<ConnectionDetailsViewModel>(context)
                  .viewModel
                  .downloadAllAssetsOffline,
              child: Text(
                context.localization.retryLabel,
                style: context.makulaTypography.label.small.copyWith(
                  color: context.makulaContentColors.danger,
                ),
              ),
            ),
          );
        }

        if (state.isOffline) {
          return MakulaBanner(
            text: context.localization.connectionOfflineBanner,
            onTap: () {},
            backgroundColor: context.makulaBackgroundColors.warningLightest,
            foregroundColor: context.makulaContentColors.warning,
          );
        }

        if (state.lastSyncDuration != null && state.canDeepUpload) {
          return MakulaBanner(
            text: context.localization
                .lastConnectionDownloadedMessage(state.lastSyncDurationText),
            onTap: () {
              InhertedViewModel.of<ConnectionDetailsViewModel>(context)
                  .viewModel
                  .downloadAllAssetsOffline();
            },
            backgroundColor: context.makulaBackgroundColors.warningLightest,
            foregroundColor: context.makulaContentColors.warning,
          );
        }
        return const SizedBox.shrink();
      },
    );
  }
}

class DownloadInProgressSheet extends StatelessWidget {
  const DownloadInProgressSheet({super.key});

  @override
  Widget build(BuildContext context) {
    return GenericTwoButtonBottomSheet(
      titleText: context.localization.downlaodInProgressTitle,
      bodyText: context.localization.downloadInProgressMessage,
      positiveButtonText: context.localization.yesLeave,
      negativeButtonText: context.localization.cancel,
      positiveCallback: () => {
        Navigator.pop(context),
        Navigator.pop(context),
      },
      negativeCallback: () => {
        Navigator.pop(context),
      },
    );
  }
}
