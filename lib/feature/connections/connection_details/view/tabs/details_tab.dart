part of 'tabs.dart';

class ConnectionOverviewTab extends StatefulWidget {
  const ConnectionOverviewTab({
    super.key,
    required this.connection,
  });

  final ConnectionDetailModel connection;

  @override
  State<ConnectionOverviewTab> createState() => _ConnectionOverviewTabState();
}

class _ConnectionOverviewTabState extends State<ConnectionOverviewTab>
    with AutomaticKeepAliveClientMixin {
  @override
  Widget build(BuildContext context) {
    super.build(context);

    return Scaffold(
      body: CustomScrollView(
        key: const PageStorageKey<String>("connection-overview"),
        slivers: [
          SliverOverlapInjector(
            handle: NestedScrollView.sliverOverlapAbsorberHandleFor(context),
          ),
          const SliverToBoxAdapter(
            child: ConnectionSyncBannerWidget(),
          ),
          SliverList.list(
            children: [
              SizedBox(height: context.makulaPadding.s),
              DataListItem(
                title: context.localization.type,
                subtitle: widget.connection.type ?? "-",
              ),
              Divider(color: context.makulaBorderColors.primary, height: 1),
              DataListItem(
                title: context.localization.address,
                subtitle: widget.connection.address?.address ?? "-",
                trailingIcon: widget.connection.hasCoordinates
                    ? MakulaIcons.chevronRight
                    : null,
                onTap: widget.connection.hasCoordinates
                    ? () => _onTapDirection(widget.connection, context)
                    : null,
              ),
              Divider(color: context.makulaBorderColors.primary, height: 1),
              DataListItem(
                title: context.localization.teams,
                trailing: getTeamsTag(context, widget.connection),
                onTap: widget.connection.teams.isEmpty
                    ? null
                    : () {
                        AnalyticsService.instance.sendEvent(
                          "View Connection Teams",
                          {"connection_id": widget.connection.id},
                        );
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (_) => TeamsPage(
                              teams: widget.connection.teams,
                            ),
                          ),
                        );
                      },
              ),
              Divider(color: context.makulaBorderColors.primary, height: 1),
              SizedBox(height: context.makulaPadding.s),
              const CustomFields(),
              SizedBox(height: context.makulaPadding.s),
              DetailListItem(
                title: context.localization.description,
                icon: MakulaIcons.leftAlign,
                child: (widget.connection.isDescriptionEmpty)
                    ? Container(
                        alignment: AlignmentDirectional.topStart,
                        padding: EdgeInsetsDirectional.only(
                          start: context.makulaPadding.l,
                          end: context.makulaPadding.l,
                          bottom: context.makulaPadding.m,
                        ),
                        child: Text(
                          context.localization.noDescriptionMessage,
                          style: context.makulaTypography.body.large
                              .secondary(context),
                        ),
                      )
                    : Padding(
                        padding: EdgeInsetsDirectional.symmetric(
                          horizontal: context.makulaPadding.l,
                        ),
                        child: ExpandableFormattedTextView(
                          key: const PageStorageKey<String>(
                              "connection-description"),
                          widget.connection.description,
                          fontSize: 16,
                          onShowLess: () {},
                        ),
                      ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget getTeamsTag(BuildContext context, ConnectionDetailModel item) {
    if (item.teams.isEmpty) {
      return Text(
        context.localization.none,
        style: context.makulaTypography.body.large.primary(context),
      );
    }
    return SizedBox(
      width: 160,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          if (item.teams.isNotEmpty)
            Flexible(
              child: CustomFieldTag.fromTagColor(
                tagColor: context.makulaTagColors.getTagColorFor(
                  item.teams[0].color,
                ),
                text: item.teams[0].name,
              ),
            ),
          if (item.teams.length > 1) ...[
            SizedBox(width: context.makulaPadding.xs),
            CustomFieldTag.fromTagColor(
              text: "+${item.teams.length - 1}",
              tagColor: context.makulaTagColors.getTagColorFor(
                item.teams[1].color,
              ),
            ),
          ],
          SizedBox(width: context.makulaPadding.xs),
          const MakulaIcon(MakulaIcons.chevronRight, size: 20),
        ],
      ),
    );
  }

  void _onTapDirection(
    ConnectionDetailModel connection,
    BuildContext context,
  ) async {
    final unableToOpenMapsString = context.localization.unableToOpenMaps;
    if (!connection.hasCoordinates) {
      return;
    }
    final address = connection.address!;

    final options = await MapsLauncher.getMapOptions();

    if (options.isEmpty) {
      Alert.showError(errorMessage: unableToOpenMapsString);
      return;
    }

    if (options.length == 1) {
      final option = options.first;
      launchUrl(
        option.generateUri(
          address.latitude.toDouble(),
          address.longitude.toDouble(),
        ),
      );

      return;
    }

    if (context.mounted) {
      MakulaBottomSheet(
        title: context.localization.chooseMap,
        builder: (context) => Padding(
          padding: const EdgeInsets.only(bottom: 16.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: options
                .map(
                  (e) => ListTile(
                    title: Text(e.label(context.localization)),
                    onTap: () {
                      launchUrl(
                        e.generateUri(
                          address.latitude.toDouble(),
                          address.longitude.toDouble(),
                        ),
                        mode: LaunchMode.externalApplication,
                      );
                    },
                  ),
                )
                .toList(),
          ),
        ),
      ).show(context);
    }
  }

  @override
  bool get wantKeepAlive => true;
}

class CustomFields extends StatelessWidget {
  const CustomFields({super.key});

  @override
  Widget build(BuildContext context) {
    final hasPermission = InhertedViewModel.of<AppViewModel>(context)
        .viewModel
        .state
        .user
        .permissions
        .hasEditConnectionsCustomFieldsPermission;

    return ViewModelBuilder<ConnectionDetailsViewModel, ConnectionDetailsState>(
      builder: (context, state) {
        var fetchState = state.customFieldsFetchState;
        var customFields = state.customFields;

        final cannotEdit = !hasPermission ||
            state.customFieldsFetchState.isFailure ||
            state.customFields.isEmpty;

        return DetailListItem(
          title: context.localization.customFields,
          icon: MakulaIcons.customField,
          trailingIcon: cannotEdit ? null : MakulaIcons.edit,
          onTap: cannotEdit
              ? null
              : () {
                  AnalyticsService.instance.sendEvent(
                    "Initiate Edit Connections Custom Fields",
                    {
                      "connection_id": state.connectionId,
                      "from": "connection_overview",
                    },
                  );

                  Navigator.of(context).push(
                    EditCustomFieldsPage.route(
                      title:
                          context.localization.editConnectionCustomFieldTitle,
                      customFields: customFields,
                      onUpdate: (editedFields) async {
                        await InhertedViewModel.of<ConnectionDetailsViewModel>(
                                context)
                            .viewModel
                            .updateCustomField(editedFields);

                        AnalyticsService.instance.sendEvent(
                          "Edited Connections Custom Fields",
                          {
                            "connection_id": state.connectionId,
                            "no_of_custom_fields": editedFields.length,
                            "from": "connection_overview",
                          },
                        );
                      },
                    ),
                  );
                },
          child: fetchState.isFailure
              ? Padding(
                  padding: EdgeInsets.all(context.makulaPadding.l),
                  child: FullScreenErrorWidget(
                    errorMessage: context.localization.loadCustomFieldsFailed,
                    onRetry: InhertedViewModel.of<ConnectionDetailsViewModel>(
                            context)
                        .viewModel
                        .getCustomFields,
                  ),
                )
              : fetchState.isLoaded
                  ? MakulaCustomFields(customFields: state.customFields)
                  : const Center(child: MakulaLoader()),
        );
      },
    );
  }
}

class TeamsPage extends StatelessWidget {
  const TeamsPage({super.key, required this.teams});

  final List<ConnectionTeamModel> teams;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MakulaBasicAppBar(title: context.localization.teams),
      body: ListView.separated(
        padding: EdgeInsets.symmetric(vertical: context.makulaPadding.xs),
        separatorBuilder: (context, index) => Divider(
          color: context.makulaBorderColors.primary,
          height: 1,
        ),
        itemCount: teams.length,
        itemBuilder: (context, index) {
          final team = teams[index];
          return DataListItem(
            leading: CustomFieldTag.fromTagColor(
              tagColor: context.makulaTagColors.getTagColorFor(team.color),
              text: team.name,
            ),
          );
        },
      ),
    );
  }
}
