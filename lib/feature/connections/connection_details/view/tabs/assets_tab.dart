part of 'tabs.dart';

class ConnectionAssetsTab extends StatefulWidget {
  const ConnectionAssetsTab({super.key});

  @override
  State<ConnectionAssetsTab> createState() => _ConnectionAssetsTabState();
}

class _ConnectionAssetsTabState extends State<ConnectionAssetsTab>
    with AutomaticKeepAliveClientMixin {
  @override
  Widget build(BuildContext context) {
    super.build(context);
    return ViewModelBuilder<ConnectionDetailsViewModel, ConnectionDetailsState>(
      builder: (context, state) {
        if (state.fetchPaginatedAssetsState.isLoading) {
          return const Center(child: MakulaLoader());
        }
        if (state.fetchPaginatedAssetsState.isFailure) {
          return FullScreenErrorWidget(
            onRetry: () =>
                InhertedViewModel.of<ConnectionDetailsViewModel>(context)
                    .viewModel
                    .fetchAssets(forceRefresh: true),
          );
        }
        if (state.fetchPaginatedAssetsState.data.isEmpty) {
          return EmptyStateView(
            title: context.localization.emptyAssetsMessage,
            illustration: MakulaIllustrations.noUser,
          );
        }

        return PaginatedSliverListWidget(
          key: const PageStorageKey<String>("connection-assets-tab"),
          onPullRefresh: () async {
            if (state.fetchPaginatedAssetsState.isPaginatedLoading) return;
            return InhertedViewModel.of<ConnectionDetailsViewModel>(context)
                .viewModel
                .fetchAssets(forceRefresh: true);
          },
          itemCount: state.fetchPaginatedAssetsState.data.length,
          hasMoreItems: state.fetchPaginatedAssetsState.hasMoreData,
          topSliverWidget: const SliverToBoxAdapter(
            child: ConnectionSyncBannerWidget(),
          ),
          itemBuilder: (context, index) {
            final asset = state.fetchPaginatedAssetsState.data[index];
            return AssetCard(
              title: asset.name,
              imageUrl: asset.image,
              facilityName: asset.facility.name,
              serialNumber: asset.serialNumber,
              subAssetCount: asset.subAssetCount,
              onTap: () {
                context.push(AssetDetailsPage.pageRoute(id: asset.id));
              },
            );
          },
          onScrollEndReached: () {
            if (state.fetchPaginatedAssetsState.isLoading) return;
            if (!state.fetchPaginatedAssetsState.isPaginatedLoading) {
              InhertedViewModel.of<ConnectionDetailsViewModel>(context)
                  .viewModel
                  .fetchAssets();
            }
          },
          bottomWidget: const Center(child: MakulaLoader()),
          separatorBuilder: (_, __) => SizedBox(
            height: context.makulaPadding.xs,
          ),
        );
      },
    );
  }

  @override
  bool get wantKeepAlive => true;
}
