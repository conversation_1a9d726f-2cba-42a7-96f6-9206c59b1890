import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:makula_flutter/core/core.dart';
import 'package:makula_flutter/core/ui/components/alert.dart';
import 'package:makula_flutter/core/utils/utils.dart';
import 'package:makula_flutter/feature/app/view_model/app_view_model.dart';
import 'package:makula_flutter/feature/assets/assets.dart';
import 'package:makula_flutter/feature/cmms/work_orders/edit_custom_fields/view/view.dart';
import 'package:makula_flutter/feature/connections/connection.dart';
import 'package:makula_flutter/service/analytics/analytics_service.dart';
import 'package:makula_theme/makula_theme.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../model/model.dart';
import '../../view_model/connection_details_view_model.dart';

part 'assets_tab.dart';
part 'details_tab.dart';

enum ConnectionDetailsTab {
  overview,
  assets;

  String title(AppLocalizations localization) {
    switch (this) {
      case ConnectionDetailsTab.overview:
        return localization.overview;
      case ConnectionDetailsTab.assets:
        return localization.assets;
    }
  }

  Widget getTabView(ConnectionDetailModel connection) {
    switch (this) {
      case ConnectionDetailsTab.overview:
        return ConnectionOverviewTab(connection: connection);
      case ConnectionDetailsTab.assets:
        return const ConnectionAssetsTab();
    }
  }
}
