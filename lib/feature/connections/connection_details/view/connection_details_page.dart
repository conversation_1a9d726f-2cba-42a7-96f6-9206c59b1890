part of 'view.dart';

class ConnectionDetailsPage extends StatelessWidget {
  const ConnectionDetailsPage({super.key, required this.id});

  static String pageName() => ':id';

  static String pageRoute({required String id}) =>
      '${ConnectionListPage.pageRoute()}/${pageName().replaceFirst(':id', id)}';

  final String id;

  @override
  Widget build(BuildContext context) {
    return ViewModelProvider<ConnectionDetailsViewModel>(
      create: () => ConnectionDetailsViewModel(
        connectionId: id,
        connectionService: get<FeatureModules>()!.connectionsService,
        oemService: get<FeatureModules>()!.oemService,
      )
        ..initialize()
        ..getConnectionDetails()
        ..getCustomFields()
        ..fetchAssets(forceRefresh: true)
        ..isDeepOfflineFunctionalityEnabled(),
      child: const ConnectionDetailsView(),
    );
  }
}
