part of 'model.dart';

class ConnectionAssetModel extends Equatable {
  final String id;
  final String? typeId;
  final String name;
  final String serialNumber;
  final String? image;
  final int subAssetCount;
  final ConnectionAssetModel? parent;
  final ConnectionAssetFacilityModel facility;
  final ConnectionAssetOemModel? oem;

  const ConnectionAssetModel({
    required this.id,
    required this.name,
    required this.serialNumber,
    required this.facility,
    required this.image,
    required this.subAssetCount,
    required this.parent,
    required this.typeId,
    required this.oem,
  });

  factory ConnectionAssetModel.fromServiceModel(ConnectionAsset asset) {
    return ConnectionAssetModel(
      id: asset.id,
      name: asset.name,
      serialNumber: asset.serialNumber,
      image: asset.image,
      subAssetCount: asset.subAssetCount,
      parent: asset.parent != null
          ? ConnectionAssetModel.fromServiceModel(asset.parent!)
          : null,
      facility: ConnectionAssetFacilityModel.fromServiceModel(asset.facility),
      typeId: asset.typeId,
      oem: asset.oem != null
          ? ConnectionAssetOemModel.fromServiceModel(asset.oem!)
          : null,
    );
  }

  @override
  List<Object?> get props => [id, name, serialNumber, oem];
}

class ConnectionAssetOemModel extends Equatable {
  final String id;
  final String name;
  final String? logo;
  final String? brandLogo;

  const ConnectionAssetOemModel({
    required this.id,
    required this.name,
    this.logo,
    this.brandLogo,
  });

  factory ConnectionAssetOemModel.fromServiceModel(
      ConnectionAssetOem assetOem) {
    return ConnectionAssetOemModel(
      id: assetOem.id,
      name: assetOem.name,
      logo: assetOem.logo,
      brandLogo: assetOem.brandLogo,
    );
  }

  @override
  List<Object?> get props => [id, name, logo, brandLogo];
}

class ConnectionAssetFacilityModel extends Equatable {
  final String id;
  final String name;

  const ConnectionAssetFacilityModel({
    required this.id,
    required this.name,
  });

  factory ConnectionAssetFacilityModel.fromServiceModel(
      ConnectionAssetFacility assetFacility) {
    return ConnectionAssetFacilityModel(
      id: assetFacility.id,
      name: assetFacility.name,
    );
  }

  static const empty = ConnectionAssetFacilityModel(
    id: "",
    name: "",
  );

  bool get isEmpty => this == empty;

  @override
  List<Object?> get props => [id, name];
}

class ConnectionAssetPageInfoModel extends Equatable {
  final int limit;
  final int skip;
  final String? searchQuery;

  const ConnectionAssetPageInfoModel({
    this.limit = 100,
    required this.skip,
    this.searchQuery,
  });

  ConnectionAssetPageInfo toServiceModel() {
    return ConnectionAssetPageInfo(
      limit: limit,
      skip: skip,
      searchQuery: searchQuery,
    );
  }

  @override
  List<Object?> get props => [limit, skip, searchQuery];
}

class PaginatedConnectionAssetListModel extends Equatable {
  final List<ConnectionAssetModel> assets;
  final int totalCount;

  const PaginatedConnectionAssetListModel({
    required this.assets,
    required this.totalCount,
  });

  factory PaginatedConnectionAssetListModel.fromServiceModel(
    PaginatedConnectionAssetList assetList,
  ) =>
      PaginatedConnectionAssetListModel(
        assets: assetList.assets
            .map((e) => ConnectionAssetModel.fromServiceModel(e))
            .toList(),
        totalCount: assetList.totalCount,
      );

  @override
  List<Object?> get props => [assets, totalCount];
}
