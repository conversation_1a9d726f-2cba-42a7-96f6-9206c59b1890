import 'dart:convert';

import 'package:equatable/equatable.dart';
import 'package:flutter_quill/flutter_quill.dart';
import 'package:makula_flutter/core/lib/slatejs/slatejs_to_delta.dart';
import 'package:makula_flutter/core/lib/slatejs/model/slatejs.dart';
import 'package:makula_flutter/service/connection/connection_service.dart';

part 'connection_detail_model.dart';
part 'connection_asset_model.dart';
