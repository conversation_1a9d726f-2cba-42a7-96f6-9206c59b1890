part of 'model.dart';

class ConnectionDetailModel extends Equatable {
  final String id;
  final String displayId;
  final String name;
  final String? type;
  final ConnectionAddressModel? address;
  final List<ConnectionTeamModel> teams;
  final String description;
  final bool isOffline;
  final Duration? lastSyncDuration;

  const ConnectionDetailModel({
    required this.id,
    required this.displayId,
    required this.name,
    required this.type,
    this.address,
    this.teams = const [],
    this.description = "",
    this.isOffline = false,
    this.lastSyncDuration,
  });

  factory ConnectionDetailModel.fromServiceModel(
    ConnectionDetail serviceModel,
  ) {
    return ConnectionDetailModel(
      id: serviceModel.id,
      name: serviceModel.name,
      displayId: serviceModel.displayId,
      type: serviceModel.type.isEmpty ? null : serviceModel.type,
      address: serviceModel.address.isEmpty
          ? null
          : ConnectionAddressModel.fromServiceModel(serviceModel.address),
      teams:
          serviceModel.teams.map(ConnectionTeamModel.fromServiceModel).toList(),
      description: serviceModel.description ?? "",
      isOffline: serviceModel.isOffline,
      lastSyncDuration: serviceModel.syncDate == null
          ? null
          : DateTime.now().difference(serviceModel.syncDate!),
    );
  }

  static const empty = ConnectionDetailModel(
    id: "",
    name: "",
    type: "",
    displayId: "",
  );

  bool get isEmpty => this == empty;

  bool get isNotEmpty => !isEmpty;

  bool get isDescriptionEmpty {
    if (description.isEmpty) return true;

    try {
      final value = jsonDecode(description);

      if (value is List) {
        if (value.isEmpty) return true;
      }
      final deltaList = SlateJs.fromJson(value).toDeltaOperations();

      final document = Document.fromJson(deltaList);
      return document.isEmpty();
    } catch (e) {
      return false;
    }
  }

  bool get hasCoordinates => address?.hasCoordinates ?? false;

  @override
  List<Object?> get props => [
        id,
        name,
        type,
        address,
        teams,
        description,
        isOffline,
        lastSyncDuration,
      ];
}

class ConnectionTeamModel extends Equatable {
  final String id;
  final String name;
  final String? color;

  const ConnectionTeamModel({
    required this.id,
    required this.name,
    required this.color,
  });

  factory ConnectionTeamModel.fromServiceModel(ConnectionTeam serviceModel) {
    return ConnectionTeamModel(
      id: serviceModel.id,
      name: serviceModel.name,
      color: serviceModel.color,
    );
  }

  @override
  List<Object?> get props => [id, name, color];
}

class ConnectionAddressModel extends Equatable {
  final String address;
  final num longitude;
  final num latitude;
  final String postalCode;

  const ConnectionAddressModel({
    required this.address,
    required this.longitude,
    required this.latitude,
    required this.postalCode,
  });

  factory ConnectionAddressModel.fromServiceModel(
      ConnectionAddress serviceModel) {
    return ConnectionAddressModel(
      address: serviceModel.address ?? "",
      longitude: serviceModel.longitude ?? 0.0,
      latitude: serviceModel.latitude ?? 0.0,
      postalCode: serviceModel.pinCode ?? "",
    );
  }

  bool get hasCoordinates => longitude != 0.0 && latitude != 0.0;

  @override
  List<Object?> get props => [address, longitude, latitude, postalCode];
}
