part of 'view_model.dart';

typedef FetchConnectionsState = PaginatedResponseState<ConnectionListItemModel>;

class ConnectionsListState extends Equatable {
  final FetchConnectionsState fetchConnectionsState;

  const ConnectionsListState({
    required this.fetchConnectionsState,
  });

  factory ConnectionsListState.initial() {
    return ConnectionsListState(
      fetchConnectionsState: FetchConnectionsState.initial(),
    );
  }

  ConnectionsListState copyWith({
    FetchConnectionsState? fetchConnectionsState,
  }) {
    return ConnectionsListState(
      fetchConnectionsState:
          fetchConnectionsState ?? this.fetchConnectionsState,
    );
  }

  List<ConnectionListItemModel> get connections => fetchConnectionsState.data;

  @override
  List<Object?> get props => [fetchConnectionsState];
}
