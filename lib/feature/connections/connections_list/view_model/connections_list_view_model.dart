part of 'view_model.dart';

class ConnectionsListViewModel
    extends ObservableViewModel<ConnectionsListState> {
  ConnectionsListViewModel({
    required ConnectionService connectionService,
  })  : _connectionService = connectionService,
        super(ConnectionsListState.initial());

  final ConnectionService _connectionService;
  Timer? _connectionSearchDebounceTimer;

  Future<void> getConnections({
    bool forceRefresh = false,
    bool skipDebounce = false,
  }) async {
    if (skipDebounce) {
      _getConnections(forceRefresh: forceRefresh);
      return;
    }

    if (_connectionSearchDebounceTimer?.isActive ?? false) {
      _connectionSearchDebounceTimer?.cancel();
    }

    _connectionSearchDebounceTimer = Timer(
      const Duration(milliseconds: 500),
      () => _getConnections(forceRefresh: forceRefresh),
    );
  }

  Future<void> _getConnections({bool forceRefresh = false}) async {
    updateState(
      state.copyWith(
        fetchConnectionsState: state.fetchConnectionsState.copyWith(
          apiState: state.fetchConnectionsState.apiState.toLoading(),
        ),
      ),
    );

    try {
      final paginatedConnectionList = await _connectionService.fetchConnections(
        ConnectionPageInfoModel(
          skip: forceRefresh ? 0 : state.connections.length,
          searchQuery: state.fetchConnectionsState.searchQuery,
        ).toServiceModel(),
      );

      final modelList = paginatedConnectionList.connections
          .map((e) => ConnectionListItemModel.fromServiceModel(e))
          .toList();

      final completeList = [
        if (!forceRefresh) ...state.connections,
        ...modelList,
      ];

      updateState(
        state.copyWith(
          fetchConnectionsState: state.fetchConnectionsState.copyWith(
            apiState: state.fetchConnectionsState.apiState.toLoaded(
              data: completeList,
            ),
            hasMoreData:
                completeList.length < paginatedConnectionList.totalCount,
          ),
        ),
      );
    } on NoInternetException {
      updateState(
        state.copyWith(
          fetchConnectionsState: state.fetchConnectionsState.copyWith(
            apiState: state.fetchConnectionsState.apiState.toFailure(
              error: ExceptionMessage.noInternet,
            ),
          ),
        ),
      );
    } catch (e) {
      updateState(
        state.copyWith(
          fetchConnectionsState: state.fetchConnectionsState.copyWith(
            apiState: state.fetchConnectionsState.apiState.toFailure(
              error: ExceptionMessage.general,
            ),
          ),
        ),
      );
    }
  }

  void searchConnections(String value) {
    updateState(
      state.copyWith(
        fetchConnectionsState: state.fetchConnectionsState.copyWith(
          searchQuery: value,
        ),
      ),
    );

    getConnections(forceRefresh: true);
  }
}
