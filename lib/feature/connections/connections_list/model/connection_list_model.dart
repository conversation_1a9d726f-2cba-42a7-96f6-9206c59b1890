part of 'model.dart';

class ConnectionListItemModel extends Equatable {
  final String id;
  final String name;
  final String? facilityId;
  final String? address;

  const ConnectionListItemModel({
    required this.id,
    required this.name,
    this.facilityId,
    this.address,
  });

  factory ConnectionListItemModel.fromServiceModel(ConnectionListModel model) {
    return ConnectionListItemModel(
      id: model.id,
      name: model.name,
      facilityId: model.facilityId,
      address: model.address,
    );
  }

  @override
  List<Object?> get props => [id, name, facilityId, address];
}

class PaginatedConnectionListModel extends Equatable {
  final List<ConnectionListItemModel> connections;
  final int totalCount;

  const PaginatedConnectionListModel({
    required this.connections,
    required this.totalCount,
  });

  factory PaginatedConnectionListModel.fromServiceModel(
    PaginatedConnectionList model,
  ) {
    return PaginatedConnectionListModel(
      connections: model.connections
          .map((e) => ConnectionListItemModel.fromServiceModel(e))
          .toList(),
      totalCount: model.totalCount,
    );
  }

  @override
  List<Object?> get props => [connections, totalCount];
}

class ConnectionPageInfoModel extends Equatable {
  final int limit;
  final int skip;
  final String? searchQuery;

  const ConnectionPageInfoModel({
    this.limit = 100,
    required this.skip,
    this.searchQuery,
  });

  ConnectionPageInfo toServiceModel() {
    return ConnectionPageInfo(
      limit: limit,
      skip: skip,
      searchQuery: searchQuery,
    );
  }

  @override
  List<Object?> get props => [limit, skip, searchQuery];
}
