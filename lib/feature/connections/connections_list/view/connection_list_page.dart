part of 'view.dart';

class ConnectionListPage extends StatelessWidget {
  const ConnectionListPage({super.key});

  static Route route() =>
      MaterialPageRoute<void>(builder: (_) => const ConnectionListPage());

  static String pageName() => 'connections';

  static String pageRoute() => '/${pageName()}';

  @override
  Widget build(BuildContext context) {
    return ViewModelProvider<ConnectionsListViewModel>(
      create: () => ConnectionsListViewModel(
        connectionService: get<FeatureModules>()!.connectionsService,
      )..getConnections(forceRefresh: true),
      child: const ConnectionListView(),
    );
  }
}
