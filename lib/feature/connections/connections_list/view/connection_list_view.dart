part of 'view.dart';

class ConnectionListView extends StatelessWidget {
  const ConnectionListView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.makulaBackgroundColors.accent,
      appBar: ProductAppBar(
        title: context.localization.connections,
        product: ProductModel.assetHub,
      ) as PreferredSizeWidget,
      body: const Column(
        children: [
          _SearchBar(),
          Expanded(child: _ConnectionListBody()),
        ],
      ),
    );
  }
}

class _SearchBar extends StatelessWidget {
  const _SearchBar();

  @override
  Widget build(BuildContext context) {
    return ViewModelBuilder<ConnectionsListViewModel, ConnectionsListState>(
      buildWhen: (previous, current) =>
          previous.fetchConnectionsState.searchQuery !=
          current.fetchConnectionsState.searchQuery,
      builder: (context, state) {
        return Container(
          color: context.makulaBackgroundColors.primary,
          padding: EdgeInsets.all(context.makulaPadding.l),
          child: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(
            onChanged: InhertedViewModel.of<ConnectionsListViewModel>(context)
                .viewModel
                .searchConnections,
          ),
        );
      },
    );
  }
}

class _ConnectionListBody extends StatelessWidget {
  const _ConnectionListBody();

  @override
  Widget build(BuildContext context) {
    return ViewModelListener<ConnectionsListViewModel, ConnectionsListState>(
      listenWhen: (previous, current) =>
          previous.fetchConnectionsState != current.fetchConnectionsState,
      listener: (context, state) {
        if (state.fetchConnectionsState.apiState.isFailure) {
          Alert.showError(
            errorMessage: state.fetchConnectionsState.apiState.error ??
                ExceptionMessage.general,
          );
        }
      },
      child: ViewModelBuilder<ConnectionsListViewModel, ConnectionsListState>(
        buildWhen: (previous, current) =>
            previous.fetchConnectionsState != current.fetchConnectionsState,
        builder: (context, state) {
          return state.fetchConnectionsState.isLoading
              ? const Center(child: MakulaLoader())
              : state.fetchConnectionsState.apiState.isFailure
                  ? FullScreenErrorWidget(
                      onRetry: () =>
                          InhertedViewModel.of<ConnectionsListViewModel>(
                                  context)
                              .viewModel
                              .getConnections(forceRefresh: true),
                    )
                  : state.fetchConnectionsState.apiState.isEmpty
                      ? const _EmptyConnectionsWidget()
                      : const _ConnectionListWidget();
        },
      ),
    );
  }
}

class _ConnectionListWidget extends StatelessWidget {
  const _ConnectionListWidget();

  @override
  Widget build(BuildContext context) {
    return ViewModelBuilder<ConnectionsListViewModel, ConnectionsListState>(
      buildWhen: (previous, current) =>
          previous.fetchConnectionsState != current.fetchConnectionsState,
      builder: (context, state) {
        final connections = state.connections;
        return PaginatedListWidget(
          padding: EdgeInsets.symmetric(vertical: context.makulaPadding.xs),
          hasMoreItems: state.fetchConnectionsState.hasMoreData,
          onPullRefresh: () async {
            if (state.fetchConnectionsState.isPaginatedLoading) {
              return;
            }
            return InhertedViewModel.of<ConnectionsListViewModel>(context)
                .viewModel
                .getConnections(forceRefresh: true);
          },
          onScrollEndReached: () {
            if (state.fetchConnectionsState.apiState.isLoading) {
              return;
            }
            if (!state.fetchConnectionsState.isPaginatedLoading) {
              InhertedViewModel.of<ConnectionsListViewModel>(context)
                  .viewModel
                  .getConnections();
            }
          },
          bottomWidget: const Center(child: MakulaLoader()),
          itemCount: connections.length,
          itemBuilder: (_, index) {
            final connection = connections[index];
            return _ConnectionListItem(
              connection: connection,
              onTap: () {
                AnalyticsService.instance.sendEvent(
                  "View Connection Details",
                  {
                    "connection_id": connection.id,
                    "connection_name": connection.name,
                  },
                );
                context.push(
                  ConnectionDetailsPage.pageRoute(id: connection.id),
                );
              },
            );
          },
          separatorBuilder: (context, __) => SizedBox(
            height: context.makulaPadding.xs,
          ),
        );
      },
    );
  }
}

class _ConnectionListItem extends StatelessWidget {
  const _ConnectionListItem({
    required this.connection,
    required this.onTap,
  });

  final ConnectionListItemModel connection;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      highlightColor: context.makulaBackgroundColors.accent.withTransparency10,
      splashColor: context.makulaBackgroundColors.accent.withTransparency25,
      child: Ink(
        color: context.makulaBackgroundColors.primary,
        padding: EdgeInsets.all(context.makulaPadding.l),
        child: Row(
          children: [
            MakulaCardIcon.large(
              icon: MakulaIcons.facility,
              tagColor: context.makulaTagColors.primary,
            ),
            SizedBox(width: context.makulaPadding.m),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    connection.name,
                    style:
                        context.makulaTypography.label.large.primary(context),
                  ),
                  SizedBox(width: context.makulaPadding.s),
                  _iconLabel(
                    context: context,
                    icon: MakulaIcons.location,
                    label: connection.address ?? "-",
                  ),
                  SizedBox(width: context.makulaPadding.xs),
                  _iconLabel(
                    context: context,
                    icon: MakulaIcons.id,
                    label: connection.facilityId ?? "-",
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _iconLabel({
    required BuildContext context,
    required String icon,
    required String label,
  }) {
    return Row(
      children: [
        Padding(
          padding: const EdgeInsets.only(top: 1.0),
          child: MakulaIcon(
            icon,
            size: 16,
            color: context.makulaContentColors.tertiary,
          ),
        ),
        SizedBox(width: context.makulaPadding.xs),
        Flexible(
          child: Text(
            label,
            style: context.makulaTypography.body.small.secondary(context),
          ),
        ),
      ],
    );
  }
}

class _EmptyConnectionsWidget extends StatelessWidget {
  const _EmptyConnectionsWidget();

  @override
  Widget build(BuildContext context) {
    return EmptyStateView(
      title: context.localization.noConnectionsFound,
      illustration: MakulaIllustrations.noUser,
    );
  }
}
