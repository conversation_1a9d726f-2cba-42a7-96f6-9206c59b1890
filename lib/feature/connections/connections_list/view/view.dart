import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:makula_flutter/core/core.dart';
import 'package:makula_flutter/core/ui/components/alert.dart';
import 'package:makula_flutter/core/utils/utils.dart';
import 'package:makula_flutter/feature/connections/connection_details/connection_details.dart';
import 'package:makula_flutter/feature/connections/connections_list/model/model.dart';
import 'package:makula_flutter/feature/connections/connections_list/view_model/view_model.dart';
import 'package:makula_flutter/feature/feature_modules.dart';
import 'package:makula_flutter/feature/product_dashboard/model/model.dart';
import 'package:makula_flutter/service/analytics/analytics_service.dart';
import 'package:makula_theme/makula_theme.dart';

part 'connection_list_page.dart';
part 'connection_list_view.dart';
