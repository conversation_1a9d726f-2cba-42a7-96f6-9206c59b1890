import 'dart:async';

import 'package:equatable/equatable.dart';
import 'package:flutter/widgets.dart';
import 'package:makula_flutter/core/core.dart';
import 'package:makula_flutter/core/utils/utils.dart';
import 'package:makula_flutter/feature/app/model/model.dart';
import 'package:makula_flutter/feature/feature.dart';
import 'package:makula_flutter/service/auth/auth_service.dart';
import 'package:makula_flutter/service/background_sync/background_asset_sync_service.dart';
import 'package:makula_flutter/service/config/config_service.dart';
import 'package:makula_flutter/service/service.dart';
import 'package:makula_flutter/service/user/user_service.dart';

part 'app_state.dart';

class AppViewModel extends ObservableViewModel<AppState> {
  AppViewModel({
    required ConfigService configService,
    required AuthService authService,
    required UserService userService,
    required BackgroundAssetSyncService backgroundAssetSyncService,
  })  : _authService = authService,
        _configService = configService,
        _userService = userService,
        _backgroundAssetSyncService = backgroundAssetSyncService,
        super(const AppState.initial());

  final AuthService _authService;
  final ConfigService _configService;
  final UserService _userService;
  final BackgroundAssetSyncService _backgroundAssetSyncService;
  late StreamSubscription<bool> _authSubscription;
  late StreamSubscription<bool> _serverState;
  late StreamSubscription<UserModel> _userSubscription;

  init() {
    _authSubscription =
        _authService.isLoggedInStream.stream.listen((isloggedIn) {
      if (isloggedIn) {
        updateState(
          state.copyWith(authState: AuthenticationState.authenticated),
        );
        // Initialize background sync when user is authenticated
        // _initializeBackgroundSync();
      } else {
        updateState(
          state.copyWith(authState: AuthenticationState.unauthenticated),
        );
      }
    });

    _serverState = _authService.serverStatus.listen(
      (isUnderMaintenance) {
        if (isUnderMaintenance) {
          updateState(
            state.copyWith(serverState: ServerState.underMaintenance),
          );
        } else {
          updateState(state.copyWith(serverState: ServerState.active));
        }
      },
    );

    _userSubscription = _userService.currentUser
        .map(UserModel.fromUser)
        .listen((user) => updateState(state.copyWith(user: user)));
  }

  /// Initialize background asset sync service
  Future<void> _initializeBackgroundSync() async {
    try {
      await _backgroundAssetSyncService.initialize();
      console("AppViewModel: Background asset sync service initialized");

      // Initialize notification service for user feedback
      // Note: BackgroundSyncNotificationService can be added here when needed
      // BackgroundSyncNotificationService.instance.initialize(_backgroundAssetSyncService);

      // Start background sync (non-blocking with optimized performance)
      _backgroundAssetSyncService
          .startBackgroundSync(showNotifications: false)
          .catchError((error) {
        console("AppViewModel: Background sync failed - $error");
      });
    } catch (error) {
      console("AppViewModel: Failed to initialize background sync - $error");
    }
  }

  Future<void> fetchUser() async {
    await ServiceRequestHandlingStrategy<void>(
      initialState: state.fetchUserState,
      request: _userService.getCurrentUser,
      onChange: (responseState) => updateState(
        state.copyWith(fetchUserState: responseState),
      ),
    ).execute();
  }

  Future<void> isLoggedIn() async {
    await Future.delayed(const Duration(seconds: 1));

    await _authService.isLoggedIn();
  }

  Future<void> saveLocale(String locale) async {
    await _configService.saveAppLocale(locale);
    updateState(state.copyWith(locale: Locale(locale)));
  }

  Future<void> getLocale() async {
    var locale = await _configService.getAppLocale();
    if (locale != null) {
      updateState(state.copyWith(locale: Locale(locale)));
    }
  }

  Future<bool> isUpdateRequired() async {
    try {
      return await _configService.isUpdateRequired();
    } on NoInternetException {
      return false;
    }
  }

  @override
  void dispose() {
    _authService.dispose();
    _authSubscription.cancel();
    _serverState.cancel();
    _userSubscription.cancel();
    super.dispose();
  }
}
