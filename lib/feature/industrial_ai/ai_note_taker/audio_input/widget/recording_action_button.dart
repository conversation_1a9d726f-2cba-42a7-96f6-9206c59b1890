part of 'widget.dart';

enum RecordingActionButtonType {
  primary,
  secondary,
}

class RecordingActionButton extends StatelessWidget {
  const RecordingActionButton._({
    required this.icon,
    required this.onTap,
    required this.type,
  });

  factory RecordingActionButton.primary({
    required String icon,
    required VoidCallback? onTap,
  }) {
    return RecordingActionButton._(
      icon: icon,
      onTap: onTap,
      type: RecordingActionButtonType.primary,
    );
  }

  factory RecordingActionButton.secondary({
    required String icon,
    required VoidCallback? onTap,
  }) {
    return RecordingActionButton._(
      icon: icon,
      onTap: onTap,
      type: RecordingActionButtonType.secondary,
    );
  }

  final String icon;
  final VoidCallback? onTap;
  final RecordingActionButtonType type;

  BorderRadius get borderRadius {
    switch (type) {
      case RecordingActionButtonType.primary:
        return BorderRadius.circular(24);
      case RecordingActionButtonType.secondary:
        return BorderRadius.circular(14);
    }
  }

  double get size {
    switch (type) {
      case RecordingActionButtonType.primary:
        return 40;
      case RecordingActionButtonType.secondary:
        return 32;
    }
  }

  Color get highlightColor {
    switch (type) {
      case RecordingActionButtonType.primary:
        return kGrey100;
      case RecordingActionButtonType.secondary:
        return kBlue20;
    }
  }

  bool get isDisabled => onTap == null;

  Color get backgroundColor {
    if (isDisabled) {
      return kDisabledColor;
    }

    switch (type) {
      case RecordingActionButtonType.primary:
        return kPrimaryColor;
      case RecordingActionButtonType.secondary:
        return kBlue10;
    }
  }

  Color get foregroundColor {
    if (isDisabled) {
      return kBackgroundColor;
    }

    switch (type) {
      case RecordingActionButtonType.primary:
        return kBackgroundColor;
      case RecordingActionButtonType.secondary:
        return kPrimaryColor;
    }
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox.square(
      dimension: type == RecordingActionButtonType.primary ? 100 : 60,
      child: Material(
        borderRadius: BorderRadius.circular(24),
        child: Ink(
          decoration: BoxDecoration(
            color: backgroundColor,
            borderRadius: borderRadius,
          ),
          child: InkWell(
            highlightColor: highlightColor,
            borderRadius: borderRadius,
            onTap: onTap,
            child: Center(
              child: MakulaIcon(
                icon,
                size: size,
                color: foregroundColor,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
