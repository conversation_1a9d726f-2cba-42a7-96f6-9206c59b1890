part of 'model.dart';

class RecordedAudioFileModel extends Equatable {
  final File? file;
  final Duration duration;

  const RecordedAudioFileModel({
    required this.file,
    required this.duration,
  });

  RecordedAudioFile toRecordedAudioFile() =>
      RecordedAudioFile(file: file, duration: duration);

  static const empty = RecordedAudioFileModel(
    file: null,
    duration: Duration.zero,
  );

  @override
  List<Object?> get props => [file, duration];
}
