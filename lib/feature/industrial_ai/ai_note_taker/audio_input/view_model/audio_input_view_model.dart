import 'dart:async';
import 'dart:io';

import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:makula_flutter/core/core.dart';
import 'package:makula_flutter/core/utils/utils.dart' hide RecordingState;
import 'package:makula_flutter/core/utils/utils.dart' as utils;
import 'package:makula_flutter/feature/industrial_ai/ai_note_taker/model/model.dart';
import 'package:makula_flutter/service/ai_note_taker/ai_note_taker_service.dart';
import 'package:makula_flutter/service/analytics/analytics_service.dart';
import 'package:makula_flutter/service/service.dart';

import '../model/model.dart';

part 'audio_input_state.dart';

class AiNoteTakerViewModelException implements Exception {}

class AudioTooShort extends AiNoteTakerViewModelException {}

class AudioTooLong extends AiNoteTakerViewModelException {}

class InvalidAudio extends AiNoteTakerViewModelException {}

class AudioLimitExceeded extends AiNoteTakerViewModelException {}

class LowLanguageConfidence extends AiNoteTakerViewModelException {}

class AudioInputViewModel extends ObservableViewModel<AudioInputState> {
  AudioInputViewModel({
    required AiNoteTakerService aiNoteTakerService,
    AudioInputHelper? audioInputHelper,
    Stopwatch? stopwatch,
  })  : _audioInputHelper = audioInputHelper ?? AudioInputHelper(),
        _aiNoteTakerService = aiNoteTakerService,
        _stopwatch = stopwatch ?? Stopwatch(),
        super(AudioInputState.initial());

  final AudioInputHelper _audioInputHelper;
  final AiNoteTakerService _aiNoteTakerService;
  final Stopwatch _stopwatch;
  StreamSubscription<void>? _timerSubscription;

  @visibleForTesting
  AudioInputHelper get audioInputHelper => _audioInputHelper;

  @visibleForTesting
  AiNoteTakerService get aiNoteTakerService => _aiNoteTakerService;

  void onLanguageSelected(NoteTakerLanguage language) {
    updateState(state.copyWith(selectedLangauge: language));
  }

  void initialize() {
    _timerSubscription =
        Stream.periodic(const Duration(milliseconds: 500), (_) {}).listen((_) {
      if (state.recordingState.isRecording) {
        updateState(
          state.copyWith(duration: _stopwatch.elapsed),
        );
      }
    });

    audioInputHelper.state.listen((helperState) {
      final recorderState = RecordingState.fromRecordState(helperState);

      if (recorderState.isRecording) {
        _stopwatch.start();
      } else if (recorderState.isPaused) {
        _stopwatch.stop();
      } else if (recorderState.isStopped) {
        _stopwatch.stop();
      }

      updateState(
        state.copyWith(
          recordingState: recorderState,
          uploadState:
              recorderState.isRecording ? state.uploadState.toInitial() : null,
          createNoteState: recorderState.isRecording
              ? state.createNoteState.toInitial()
              : null,
        ),
      );
    });
  }

  Future<void> uploadAudioFile() async {
    await Future.delayed(Duration.zero);

    updateState(
      state.copyWith(uploadState: state.uploadState.toLoading()),
    );

    try {
      if (state.duration.inSeconds < 10) throw AudioTooShort();

      if (state.duration.inSeconds > 3605) throw AudioTooLong();

      final upload = await _aiNoteTakerService
          .upload(state.recordedAudioFile.toRecordedAudioFile());

      updateState(
        state.copyWith(
          uploadState: state.uploadState.toLoaded(
            data: AudioUploadModel.fromServiceModel(upload),
          ),
        ),
      );
    } on AudioTooLong {
      updateState(
        state.copyWith(
          uploadState: state.uploadState.toFailure(
            error: 'Audio is too long.',
            exception: AudioTooLong(),
          ),
        ),
      );
    } on AudioTooShort {
      updateState(
        state.copyWith(
          uploadState: state.uploadState.toFailure(
            error: 'Audio is too short.',
            exception: AudioTooShort(),
          ),
        ),
      );
    } on NoInternetException {
      updateState(
        state.copyWith(
          uploadState: state.uploadState.toFailure(
            error: ExceptionMessage.noInternet,
            exception: Exception(),
          ),
        ),
      );
    } catch (e) {
      updateState(
        state.copyWith(
          uploadState: state.uploadState.toFailure(
            error: ExceptionMessage.general,
            exception: Exception(),
          ),
        ),
      );
    }
  }

  Future<void> createNote() async {
    if (!state.uploadState.isLoaded) return;

    updateState(
      state.copyWith(createNoteState: state.createNoteState.toLoading()),
    );

    try {
      final url = state.uploadState.data!.url;

      if (state.selectedLangauge == null) {
        final languageCode = await _aiNoteTakerService.detectLanguage(url);
        updateState(
          state.copyWith(
            selectedLangauge: NoteTakerLanguage.getlanguageFor(languageCode),
          ),
        );
      }

      final note = await _aiNoteTakerService.generateSummary(
        audioUrl: url,
        languageCode: state.selectedLangauge!.code,
      );

      AnalyticsService.instance.sendEvent(
        "AI Note Created",
        {
          "id": note.id,
          "title": note.content.title,
          "language": note.languageCode,
        },
      );

      updateState(
        state.copyWith(
          createNoteState: state.createNoteState.toLoaded(
            data: AiNoteModel.fromServiceModel(note),
          ),
        ),
      );
    } on InvalidAudioException {
      updateState(
        state.copyWith(
          createNoteState: state.createNoteState.toFailure(
            error: "Failed to Create Note",
            exception: InvalidAudio(),
          ),
        ),
      );
    } on AudioLimitExceededException {
      updateState(
        state.copyWith(
          createNoteState: state.createNoteState.toFailure(
            error: "Failed to Create Note",
            exception: AudioLimitExceeded(),
          ),
        ),
      );
    } on LowLanguageConfidenceException {
      final languageCode = await _aiNoteTakerService.getDefaultLanguage();
      updateState(
        state.copyWith(
          createNoteState: state.createNoteState.toFailure(
            error: "Unable to detect language",
            exception: LowLanguageConfidence(),
          ),
          selectedLangauge: languageCode == null
              ? null
              : NoteTakerLanguage.getlanguageFor(languageCode),
        ),
      );
    } on NoInternetException {
      updateState(
        state.copyWith(
          createNoteState: state.createNoteState.toFailure(
            error: ExceptionMessage.noInternet,
            exception: Exception(),
          ),
        ),
      );
    } catch (e) {
      updateState(
        state.copyWith(
          createNoteState: state.createNoteState.toFailure(
            error: ExceptionMessage.general,
            exception: Exception(),
          ),
        ),
      );
    }
  }

  Future<void> onRecord() async {
    updateState(
      state.copyWith(
        intializeRecorderState: state.intializeRecorderState.toLoading(),
      ),
    );

    try {
      _stopwatch.reset();
      await _audioInputHelper.record();

      updateState(
        state.copyWith(
          intializeRecorderState: state.intializeRecorderState.toLoaded(),
        ),
      );
    } catch (e) {
      updateState(
        state.copyWith(
          intializeRecorderState: state.intializeRecorderState.toFailure(
            error: ExceptionMessage.general,
          ),
        ),
      );
    }
  }

  Future<void> onStop() async {
    final path = await _audioInputHelper.stopRecorder();
    final duration = await _audioInputHelper.calculateDuration();

    AnalyticsService.instance.sendEvent(
      "Initiate Generate Summary",
      {"duration": duration.inSeconds},
    );

    updateState(
      state.copyWith(
        recordedAudioFile: RecordedAudioFileModel(
          file: File(path),
          duration: duration,
        ),
      ),
    );
  }

  Future<void> onPause() async {
    await _audioInputHelper.pauseRecorder();
  }

  Future<void> onResume() async {
    await _audioInputHelper.resumeRecorder();
  }

  Future<void> onSubmit() async {
    if (!state.recordingState.isStopped) await onStop();
    if (!state.uploadState.isLoaded) await uploadAudioFile();
    if (!state.createNoteState.isLoaded) await createNote();
  }

  @override
  void dispose() {
    _stopwatch.reset();
    _audioInputHelper.dispose();
    _timerSubscription?.cancel();
    super.dispose();
  }
}
