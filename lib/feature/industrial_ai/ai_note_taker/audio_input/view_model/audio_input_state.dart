part of 'audio_input_view_model.dart';

typedef UploadState = GeneralResponseState<AudioUploadModel>;
typedef CreateNoteState = GeneralResponseState<AiNoteModel>;
typedef IntializeRecorderState = GeneralResponseState<void>;

enum RecordingState {
  initial,
  recording,
  paused,
  stopped;

  factory RecordingState.fromRecordState(utils.RecordingState state) {
    switch (state) {
      case utils.RecordingState.recording:
        return RecordingState.recording;
      case utils.RecordingState.paused:
        return RecordingState.paused;
      case utils.RecordingState.stopped:
        return RecordingState.stopped;
      default:
        return RecordingState.initial;
    }
  }

  bool get isInitial => this == RecordingState.initial;

  bool get isRecording => this == RecordingState.recording;

  bool get isStopped => this == RecordingState.stopped;

  bool get isPaused => this == RecordingState.paused;
}

class AudioInputState extends Equatable {
  final RecordingState recordingState;
  final IntializeRecorderState intializeRecorderState;
  final UploadState uploadState;
  final CreateNoteState createNoteState;
  final RecordedAudioFileModel recordedAudioFile;
  final Duration duration;
  final NoteTakerLanguage? selectedLangauge;

  const AudioInputState({
    required this.recordingState,
    required this.intializeRecorderState,
    required this.uploadState,
    required this.createNoteState,
    required this.recordedAudioFile,
    required this.duration,
    required this.selectedLangauge,
  });

  factory AudioInputState.initial() => const AudioInputState(
        recordingState: RecordingState.initial,
        intializeRecorderState: IntializeRecorderState(),
        uploadState: UploadState(),
        createNoteState: CreateNoteState(),
        recordedAudioFile: RecordedAudioFileModel.empty,
        duration: Duration.zero,
        selectedLangauge: null,
      );

  AudioInputState copyWith({
    RecordingState? recordingState,
    IntializeRecorderState? intializeRecorderState,
    UploadState? uploadState,
    CreateNoteState? createNoteState,
    RecordedAudioFileModel? recordedAudioFile,
    Duration? duration,
    NoteTakerLanguage? selectedLangauge,
  }) =>
      AudioInputState(
        recordingState: recordingState ?? this.recordingState,
        intializeRecorderState:
            intializeRecorderState ?? this.intializeRecorderState,
        uploadState: uploadState ?? this.uploadState,
        createNoteState: createNoteState ?? this.createNoteState,
        recordedAudioFile: recordedAudioFile ?? this.recordedAudioFile,
        duration: duration ?? this.duration,
        selectedLangauge: selectedLangauge ?? this.selectedLangauge,
      );

  bool get isUplaoding => uploadState.isLoading || uploadState.isFailure;

  bool get idCreatingNote =>
      createNoteState.isLoading || createNoteState.isFailure;

  String get formattedDuration => duration.toMMSS;

  @override
  List<Object?> get props => [
        recordingState,
        intializeRecorderState,
        uploadState,
        createNoteState,
        recordedAudioFile,
        duration,
        selectedLangauge,
      ];
}
