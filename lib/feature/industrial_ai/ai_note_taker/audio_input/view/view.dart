import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:makula_flutter/core/core.dart';
import 'package:makula_flutter/core/ui/components/text_view.dart';
import 'package:makula_flutter/core/utils/utils.dart' hide RecordingState;
import 'package:makula_flutter/feature/industrial_ai/ai_note_taker/audio_input/view_model/audio_input_view_model.dart';
import 'package:makula_flutter/feature/industrial_ai/ai_note_taker/note_details/note_details.dart';
import 'package:makula_flutter/feature/cmms/work_orders/work_order_overview/procedure/procedure_overview/widgets/generic_two_button_bottom_sheet.dart';
import 'package:makula_flutter/feature/feature_modules.dart';
import 'package:makula_flutter/feature/industrial_ai/ai_note_taker/note_listing/note_listing.dart';
import 'package:makula_flutter/service/analytics/analytics_service.dart';
import 'package:makula_theme/makula_theme.dart';

import '../widget/widget.dart';

part 'audio_input_page.dart';
part 'audio_input_view.dart';
