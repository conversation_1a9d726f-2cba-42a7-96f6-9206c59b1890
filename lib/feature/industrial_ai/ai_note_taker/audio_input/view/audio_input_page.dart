part of 'view.dart';

class AudioInputPage extends StatelessWidget {
  const AudioInputPage({super.key});

  static String pageName() => 'audioInput';

  static String pageRoute() => '${NoteListingPage.pageRoute()}/${pageName()}';

  @override
  Widget build(BuildContext context) {
    return ViewModelProvider<AudioInputViewModel>(
      create: () => AudioInputViewModel(
        aiNoteTakerService: get<FeatureModules>()!.aiNoteTakerService,
      )
        ..initialize()
        ..onRecord(),
      child: const AudioInputView(),
    );
  }
}
