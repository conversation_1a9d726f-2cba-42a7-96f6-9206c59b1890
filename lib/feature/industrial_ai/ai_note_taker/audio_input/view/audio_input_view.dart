part of 'view.dart';

class AudioInputView extends StatefulWidget {
  const AudioInputView({super.key});

  @override
  State<AudioInputView> createState() => _AudioInputViewState();
}

class _AudioInputViewState extends State<AudioInputView> {
  late final AppLifecycleListener _listener;

  @override
  void initState() {
    super.initState();
    _listener = AppLifecycleListener(
      onHide: () => _handleTransition('hide'),
      onInactive: () => _handleTransition('inactive'),
      onPause: () => _handleTransition('pause'),
      onDetach: () => _handleTransition('detach'),
    );
  }

  @override
  void dispose() {
    _listener.dispose();
    super.dispose();
  }

  void _handleTransition(String name) {
    InhertedViewModel.of<AudioInputViewModel>(context).viewModel.onPause();
  }

  @override
  Widget build(BuildContext context) {
    return ViewModelListener<AudioInputViewModel, AudioInputState>(
      listenWhen: (previous, current) =>
          previous.uploadState != current.uploadState ||
          previous.createNoteState != current.createNoteState,
      listener: (context, state) {
        if (state.createNoteState.isLoaded) {
          context.pushReplacement(
            NoteDetailPage.pageRoute(noteId: state.createNoteState.data!.id),
            extra: state.createNoteState.data,
          );
        }
      },
      child: PopScope(
        canPop: false,
        child: Scaffold(
          backgroundColor: kBackgroundColor,
          appBar: MakulaBasicAppBar(
            leading: const _BackButton(),
            title: context.localization.aiNotetaker,
            backgroundColor: context.makulaBackgroundColors.accent,
          ),
          body: const _Body(),
        ),
      ),
    );
  }
}

class _BackButton extends StatelessWidget {
  const _BackButton();

  @override
  Widget build(BuildContext context) {
    return ViewModelBuilder<AudioInputViewModel, AudioInputState>(
      builder: (context, state) {
        return IconButton(
          icon: const MakulaIcon(MakulaIcons.back, size: 24),
          onPressed: () {
            if (state.recordingState.isRecording) {
              InhertedViewModel.of<AudioInputViewModel>(context)
                  .viewModel
                  .onPause();
              showModalBottomSheet(
                context: context,
                enableDrag: false,
                builder: (BuildContext buildContext) {
                  return GenericTwoButtonBottomSheet(
                    titleText: context.localization.continueWithoutSavingTitle,
                    bodyText: context.localization.continueWithoutSavingMessage,
                    positiveButtonText: context.localization.confirmDelete,
                    negativeButtonText: context.localization.resume,
                    positiveCallback: () => {
                      Navigator.pop(context),
                      Navigator.pop(context),
                    },
                    negativeCallback: () => {
                      InhertedViewModel.of<AudioInputViewModel>(context)
                          .viewModel
                          .onResume(),
                      Navigator.pop(context),
                    },
                  );
                },
              );
            } else {
              showModalBottomSheet(
                context: context,
                enableDrag: false,
                builder: (BuildContext buildContext) {
                  return _ExitingAudioInputPage(
                    title: context.localization.continueWithoutSavingTitle,
                    body: context.localization.continueWithoutSavingMessage,
                    viewModel:
                        InhertedViewModel.of<AudioInputViewModel>(context)
                            .viewModel,
                  );
                },
              );
            }
          },
        );
      },
    );
  }
}

class _Body extends StatelessWidget {
  const _Body();

  @override
  Widget build(BuildContext context) {
    return ViewModelBuilder<AudioInputViewModel, AudioInputState>(
      builder: (context, state) {
        return SizedBox(
          width: double.infinity,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const SizedBox(height: 40),
                const _MainView(),
                const Spacer(),
                const _Controls(),
                SizedBox(
                  height: 40 + MediaQuery.paddingOf(context).bottom,
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

class _MainView extends StatelessWidget {
  const _MainView();

  @override
  Widget build(BuildContext context) {
    return ViewModelBuilder<AudioInputViewModel, AudioInputState>(
      builder: (context, state) {
        if (state.recordingState != RecordingState.stopped) {
          return const _AudioState();
        }

        if (state.isUplaoding) {
          return const _UplaodState();
        }

        if (state.idCreatingNote) {
          return const _CreateNoteState();
        }

        return const SizedBox.shrink();
      },
    );
  }
}

class _UplaodState extends StatelessWidget {
  const _UplaodState();

  @override
  Widget build(BuildContext context) {
    return ViewModelListener<AudioInputViewModel, AudioInputState>(
      listenWhen: (previous, current) =>
          previous.uploadState != current.uploadState,
      listener: (context, state) {
        if (state.uploadState.isFailure &&
            (state.uploadState.exception is AudioTooLong ||
                state.uploadState.exception is AudioTooShort)) {
          showModalBottomSheet(
            isDismissible: false,
            context: context,
            enableDrag: false,
            builder: (BuildContext buildContext) {
              return state.uploadState.exception is AudioTooLong
                  ? _RestartRecordingBottomSheet(
                      title: context.localization.audioTooLong,
                      body: context.localization.audioTooLongMessage,
                      viewModel:
                          InhertedViewModel.of<AudioInputViewModel>(context)
                              .viewModel,
                    )
                  : _RestartRecordingBottomSheet(
                      title: context.localization.recordingTooShort,
                      body: context.localization.recordingTooShortMessage,
                      viewModel:
                          InhertedViewModel.of<AudioInputViewModel>(context)
                              .viewModel,
                    );
            },
          );
        }
      },
      child: ViewModelBuilder<AudioInputViewModel, AudioInputState>(
        buildWhen: (previous, current) =>
            previous.uploadState != current.uploadState,
        builder: (context, state) {
          if (state.uploadState.isFailure) {
            bool willOpenBottomSheet =
                state.uploadState.exception is AudioTooLong ||
                    state.uploadState.exception is AudioTooShort;
            return _LoadingFailureWidget(
              message: state.uploadState.error ??
                  context.localization.failedToUploadAudio,
              onRetry: InhertedViewModel.of<AudioInputViewModel>(context)
                  .viewModel
                  .onSubmit,
              showRetry: !willOpenBottomSheet,
            );
          }

          if (state.uploadState.isLoading) {
            return _TextWithAnimationWidget(
              animation: MakulaAnimations.aiLoading,
              text: context.localization.uploadingLabel,
            );
          }

          return TextView(
            text: state.uploadState.state.name,
            fontSize: 20,
          );
        },
      ),
    );
  }
}

class _LoadingFailureWidget extends StatelessWidget {
  const _LoadingFailureWidget({
    required this.message,
    required this.onRetry,
    required this.showRetry,
  });

  final String message;
  final VoidCallback onRetry;
  final bool showRetry;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const Padding(
          padding: EdgeInsets.symmetric(vertical: 20.0),
          child: MakulaIcon(
            MakulaIcons.error,
            size: 100,
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 56.0),
          child: TextView(
            text: message,
            align: TextAlign.center,
            textColor: kTextColorLight,
            fontSize: 14,
          ),
        ),
        if (showRetry) ...[
          const SizedBox(height: 32),
          SizedBox(
            width: 200,
            child: SecondaryButton.medium(
              title: context.localization.retryLabel,
              onTap: onRetry,
            ),
          ),
        ]
      ],
    );
  }
}

class _RecordingLimitReachedBottomSheet extends StatelessWidget {
  const _RecordingLimitReachedBottomSheet({
    required this.title,
    required this.body,
    required this.viewModel,
  });

  final String title;
  final String body;
  final AudioInputViewModel viewModel;

  @override
  Widget build(BuildContext context) {
    return GenericTwoButtonBottomSheet(
      titleText: title,
      bodyText: body,
      positiveButtonText: context.localization.save,
      negativeButtonText: context.localization.exit,
      positiveCallback: () {
        Navigator.of(context).pop();
        viewModel.onSubmit();
      },
      negativeCallback: () {
        Navigator.of(context).pop();
        Navigator.of(context).pop();
      },
    );
  }
}

class _RestartRecordingBottomSheet extends StatelessWidget {
  const _RestartRecordingBottomSheet({
    required this.title,
    required this.body,
    required this.viewModel,
  });

  final String title;
  final String body;
  final AudioInputViewModel viewModel;

  @override
  Widget build(BuildContext context) {
    return GenericTwoButtonBottomSheet(
      titleText: title,
      bodyText: body,
      positiveButtonText: context.localization.startOver,
      negativeButtonText: context.localization.exit,
      positiveCallback: () {
        Navigator.of(context).pop();
        viewModel.onRecord();
      },
      negativeCallback: () {
        Navigator.of(context).pop();
        Navigator.of(context).pop();
      },
    );
  }
}

class _ExitingAudioInputPage extends StatelessWidget {
  const _ExitingAudioInputPage({
    required this.title,
    required this.body,
    required this.viewModel,
  });

  final String title;
  final String body;
  final AudioInputViewModel viewModel;

  @override
  Widget build(BuildContext context) {
    return GenericTwoButtonBottomSheet(
      titleText: title,
      bodyText: body,
      positiveButtonText: context.localization.exit,
      negativeButtonText: context.localization.cancel,
      positiveCallback: () {
        Navigator.of(context).pop();
        Navigator.of(context).pop();
      },
      negativeCallback: () {
        Navigator.of(context).pop();
      },
    );
  }
}

class _CreateNoteState extends StatelessWidget {
  const _CreateNoteState();

  bool showBottomSheet(CreateNoteState state) {
    if (state.isFailure) {
      return state.exception is InvalidAudio ||
          state.exception is AudioLimitExceeded;
    }

    return false;
  }

  bool showLanguageSelectionDialog(AudioInputState state) =>
      state.createNoteState.isFailure &&
      state.createNoteState.exception is LowLanguageConfidence;

  @override
  Widget build(BuildContext context) {
    return ViewModelListener<AudioInputViewModel, AudioInputState>(
      listenWhen: (previous, current) =>
          previous.createNoteState != current.createNoteState,
      listener: (context, state) {
        if (showBottomSheet(state.createNoteState)) {
          showModalBottomSheet(
            isDismissible: false,
            context: context,
            enableDrag: false,
            builder: (BuildContext buildContext) {
              if (state.createNoteState.exception is AudioLimitExceeded) {
                return _RestartRecordingBottomSheet(
                  title: context.localization.audioLimitExceeded,
                  body: context.localization.audioLimitExceededMessage,
                  viewModel: InhertedViewModel.of<AudioInputViewModel>(context)
                      .viewModel,
                );
              }
              return _RestartRecordingBottomSheet(
                title: context.localization.createNoteFailed,
                body: context.localization.createNoteFailedMessage,
                viewModel: InhertedViewModel.of<AudioInputViewModel>(context)
                    .viewModel,
              );
            },
          );
        }
        if (showLanguageSelectionDialog(state)) {
          showDialog(
            context: context,
            barrierDismissible: false,
            builder: (c) => InhertedViewModel(
              viewModel:
                  InhertedViewModel.of<AudioInputViewModel>(context).viewModel,
              child: const LanguageSelectionDialog(),
            ),
          );
        }
      },
      child: ViewModelBuilder<AudioInputViewModel, AudioInputState>(
        builder: (context, state) {
          if (state.createNoteState.isFailure) {
            return _LoadingFailureWidget(
              message: state.createNoteState.error ??
                  context.localization.createNoteFailed,
              onRetry: InhertedViewModel.of<AudioInputViewModel>(context)
                  .viewModel
                  .onSubmit,
              showRetry: !showBottomSheet(state.createNoteState),
            );
          }

          if (state.createNoteState.isLoading) {
            return _TextWithAnimationWidget(
              animation: MakulaAnimations.aiLoading,
              text: context.localization.analysingInput,
            );
          }

          return TextView(
            text: state.createNoteState.state.name,
            fontSize: 20,
          );
        },
      ),
    );
  }
}

class _TextWithAnimationWidget extends StatelessWidget {
  const _TextWithAnimationWidget({
    required this.animation,
    required this.text,
  });

  final String animation;
  final String text;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        TextView(
          text: text,
          fontSize: 16,
          textColor: kPrimaryColor,
        ),
        SizedBox.square(
          dimension: 240,
          child: Center(
            child: MakulaAnimation(
              animation,
              height: 80,
              width: 80,
            ),
          ),
        ),
      ],
    );
  }
}

class _AudioState extends StatelessWidget {
  const _AudioState();

  @override
  Widget build(BuildContext context) {
    return ViewModelListener<AudioInputViewModel, AudioInputState>(
      listenWhen: (previous, current) =>
          previous.intializeRecorderState != current.intializeRecorderState,
      listener: (context, state) {
        if (state.intializeRecorderState.isFailure) {
          showModalBottomSheet(
            context: context,
            isDismissible: false,
            isScrollControlled: false,
            enableDrag: false,
            builder: (BuildContext buildContext) {
              return GenericTwoButtonBottomSheet(
                titleText: context.localization.unableToRecord,
                bodyText: context.localization.microphoneInUseError,
                positiveButtonText: context.localization.retryLabel,
                negativeButtonText: context.localization.exit,
                positiveCallback: () {
                  Navigator.pop(context);
                  InhertedViewModel.of<AudioInputViewModel>(context)
                      .viewModel
                      .onRecord();
                },
                negativeCallback: () {
                  Navigator.pop(context);
                  Navigator.pop(context);
                },
              );
            },
          );
        }
      },
      child: ViewModelBuilder<AudioInputViewModel, AudioInputState>(
        builder: (context, state) {
          if (state.intializeRecorderState.isLoaded) {
            return Column(
              children: [
                TextView(
                  text: state.recordingState == RecordingState.recording
                      ? context.localization.listeningLabel
                      : context.localization.pausedLabel,
                  fontSize: 14,
                  textColor: kTextColorLight,
                ),
                const SizedBox(height: 40),
                AnimatedSwitcher(
                  duration: const Duration(milliseconds: 300),
                  child: state.recordingState == RecordingState.recording
                      ? const MakulaAnimation(
                          MakulaAnimations.recording,
                          height: 200,
                          width: 200,
                        )
                      : SizedBox(
                          height: 200,
                          child: Center(
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: List.generate(
                                5,
                                (_) => Container(
                                  width: 14,
                                  height: 14,
                                  margin: const EdgeInsets.symmetric(
                                    horizontal: 6,
                                  ),
                                  decoration: const BoxDecoration(
                                    color: kPrimaryColor,
                                    shape: BoxShape.circle,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                ),
                const SizedBox(height: 40),
                const _TimerWidget(),
              ],
            );
          }

          if (state.intializeRecorderState.isFailure) {
            return _LoadingFailureWidget(
              message: context.localization.failedToInitializeRecorder,
              onRetry: InhertedViewModel.of<AudioInputViewModel>(context)
                  .viewModel
                  .onRecord,
              showRetry: false,
            );
          }
          return _TextWithAnimationWidget(
            animation: MakulaAnimations.aiLoading,
            text: context.localization.initializingRecorder,
          );
        },
      ),
    );
  }
}

class _TimerWidget extends StatelessWidget {
  const _TimerWidget();

  @override
  Widget build(BuildContext context) {
    final viewModel =
        InhertedViewModel.of<AudioInputViewModel>(context).viewModel;
    return ViewModelListener<AudioInputViewModel, AudioInputState>(
      listenWhen: (previous, current) => previous.duration != current.duration,
      listener: (context, state) {
        if (state.duration.inSeconds >= 3600) {
          InhertedViewModel.of<AudioInputViewModel>(context).viewModel.onStop();

          showModalBottomSheet(
            context: context,
            isDismissible: false,
            isScrollControlled: false,
            enableDrag: false,
            builder: (BuildContext buildContext) {
              return _RecordingLimitReachedBottomSheet(
                body: context.localization.recordingLimitMessage,
                title: context.localization.recordingLimitReached,
                viewModel: viewModel,
              );
            },
          );
        }
      },
      child: ViewModelBuilder<AudioInputViewModel, AudioInputState>(
        builder: (context, state) {
          return Column(
            children: [
              TextView(
                text: state.formattedDuration,
                fontSize: 18,
                textFontWeight: FontWeight.w600,
              ),
              SizedBox(height: context.makulaPadding.xs),
              Text(
                context.localization.perRecordingLimit,
                style: context.makulaTypography.body.small.secondary(context),
              ),
            ],
          );
        },
      ),
    );
  }
}

class _Controls extends StatelessWidget {
  const _Controls();

  @override
  Widget build(BuildContext context) {
    return const Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _PlayPauseButton(),
        SizedBox(width: 24),
        _SubmitButton(),
        SizedBox(width: 24),
        _DeleteButton(),
      ],
    );
  }
}

class _DeleteButton extends StatelessWidget {
  const _DeleteButton();

  @override
  Widget build(BuildContext context) {
    return ViewModelBuilder<AudioInputViewModel, AudioInputState>(
      builder: (context, state) {
        return RecordingActionButton.secondary(
          icon: MakulaIcons.delete,
          onTap: state.recordingState.isStopped
              ? null
              : () {
                  InhertedViewModel.of<AudioInputViewModel>(context)
                      .viewModel
                      .onPause();

                  showModalBottomSheet(
                    context: context,
                    enableDrag: false,
                    builder: (BuildContext buildContext) {
                      return GenericTwoButtonBottomSheet(
                        titleText: context.localization.deleteRecordingTitle,
                        bodyText: context.localization.deleteRecordingMessage,
                        positiveButtonText: context.localization.confirmDelete,
                        negativeButtonText: context.localization.resume,
                        positiveCallback: () => {
                          Navigator.pop(context),
                          Navigator.pop(context),
                        },
                        negativeCallback: () => {
                          InhertedViewModel.of<AudioInputViewModel>(context)
                              .viewModel
                              .onResume(),
                          Navigator.pop(context),
                        },
                      );
                    },
                  );
                },
        );
      },
    );
  }
}

class _SubmitButton extends StatelessWidget {
  const _SubmitButton();

  @override
  Widget build(BuildContext context) {
    return ViewModelBuilder<AudioInputViewModel, AudioInputState>(
      builder: (context, state) {
        return RecordingActionButton.primary(
          icon: MakulaIcons.check,
          onTap: state.recordingState.isStopped
              ? null
              : InhertedViewModel.of<AudioInputViewModel>(context)
                  .viewModel
                  .onSubmit,
        );
      },
    );
  }
}

class _PlayPauseButton extends StatelessWidget {
  const _PlayPauseButton();

  @override
  Widget build(BuildContext context) {
    return ViewModelBuilder<AudioInputViewModel, AudioInputState>(
      builder: (context, state) {
        if (state.recordingState == RecordingState.paused) {
          return RecordingActionButton.secondary(
            icon: MakulaIcons.record,
            onTap: state.recordingState.isStopped
                ? null
                : InhertedViewModel.of<AudioInputViewModel>(context)
                    .viewModel
                    .onResume,
          );
        }
        return RecordingActionButton.secondary(
          icon: MakulaIcons.pause,
          onTap: state.recordingState.isStopped
              ? null
              : InhertedViewModel.of<AudioInputViewModel>(context)
                  .viewModel
                  .onPause,
        );
      },
    );
  }
}

class LanguageSelectionDialog extends StatelessWidget {
  const LanguageSelectionDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: ViewModelBuilder<AudioInputViewModel, AudioInputState>(
        buildWhen: (previous, current) =>
            previous.selectedLangauge != current.selectedLangauge,
        builder: (context, state) {
          return AlertDialog(
            title: Text(context.localization.detectLanguageDialogTitle),
            titlePadding: EdgeInsets.all(context.makulaPadding.l),
            contentPadding:
                EdgeInsets.symmetric(horizontal: context.makulaPadding.l),
            actionsPadding: EdgeInsets.all(context.makulaPadding.l),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  context.localization.detectLanguageDialogSubtitle,
                  style:
                      context.makulaTypography.body.medium.secondary(context),
                ),
                SizedBox(height: context.makulaPadding.xxl),
                LanguageSelectionField(
                  onSelect: InhertedViewModel.of<AudioInputViewModel>(context)
                      .viewModel
                      .onLanguageSelected,
                  selectedLanguage: state.selectedLangauge,
                ),
              ],
            ),
            actions: [
              SizedBox(
                width: double.infinity,
                child: PrimaryButton.medium(
                  onTap: () {
                    AnalyticsService.instance.sendEvent(
                      "Manually Selected Ai Note Language",
                      {
                        "selectedLanguage": state.selectedLangauge?.code,
                        "audioUrl": state.uploadState.data?.url,
                      },
                    );
                    InhertedViewModel.of<AudioInputViewModel>(context)
                        .viewModel
                        .createNote();
                    Navigator.pop(context);
                  },
                  title: context.localization.confirmSelection,
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}

class LanguageSelectionField extends StatefulWidget {
  const LanguageSelectionField({
    super.key,
    required this.onSelect,
    this.selectedLanguage,
  });

  final ValueChanged<NoteTakerLanguage> onSelect;
  final NoteTakerLanguage? selectedLanguage;

  @override
  State<LanguageSelectionField> createState() => _LanguageSelectionFieldState();
}

class _LanguageSelectionFieldState extends State<LanguageSelectionField> {
  String searchQuery = '';

  List<NoteTakerLanguage> get filteredLanguages => NoteTakerLanguage.all
      .where(
        (language) =>
            language.label.toLowerCase().contains(searchQuery.toLowerCase()) ||
            language.code.toLowerCase().contains(searchQuery.toLowerCase()),
      )
      .toList();

  @override
  Widget build(BuildContext context) {
    return MakulaTextField(
      key: Key('languageSelectionField-${widget.selectedLanguage?.label}'),
      hintText: context.localization.selectLanguage,
      readOnly: true,
      initialValue: widget.selectedLanguage?.label,
      onTap: () {
        setState(() => searchQuery = '');
        MakulaBottomSheet(
          title: context.localization.selectLanguage,
          isScrollControlled: true,
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.8,
          ),
          builder: (_) => StatefulBuilder(
            builder: (context, setStateA) {
              return Expanded(
                child: Column(
                  children: [
                    Padding(
                      padding: EdgeInsets.symmetric(
                        horizontal: context.makulaPadding.l,
                        vertical: context.makulaPadding.m,
                      ),
                      child: MakulaSearchField(
                        onChanged: (String value) =>
                            setStateA(() => searchQuery = value),
                      ),
                    ),
                    Expanded(
                      child: filteredLanguages.isEmpty
                          ? Center(
                              child: Text(context.localization.noResultFound),
                            )
                          : ListView.separated(
                              itemCount: filteredLanguages.length,
                              separatorBuilder: (context, index) => Divider(
                                height: 1,
                                color: context.makulaBorderColors.primary,
                              ),
                              itemBuilder: (context, index) {
                                final language = filteredLanguages[index];
                                return DataListItem(
                                  titleWidget: Text(
                                    language.label,
                                    style: context.makulaTypography.body.medium
                                        .primary(context),
                                  ),
                                  subtitleWidget: Text(
                                    language.code,
                                    style: context.makulaTypography.body.small
                                        .secondary(context),
                                  ),
                                  onTap: () {
                                    widget.onSelect(language);
                                    Navigator.of(context).pop();
                                  },
                                  trailingIcon:
                                      widget.selectedLanguage == language
                                          ? MakulaIcons.check
                                          : null,
                                );
                              },
                            ),
                    ),
                    SizedBox(height: MediaQuery.viewInsetsOf(context).bottom),
                  ],
                ),
              );
            },
          ),
        ).show(context);
      },
      suffixIcon: MakulaIcon(
        MakulaIcons.chevronDown,
        size: 16,
        color: context.makulaContentColors.secondary,
      ),
    );
  }
}
