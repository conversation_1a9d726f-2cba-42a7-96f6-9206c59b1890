part of 'view.dart';

class NoteOverview extends StatelessWidget {
  const NoteOverview({super.key});

  @override
  Widget build(BuildContext context) {
    return ViewModelBuilder<NoteDetailsViewModel, NoteDetailsState>(
      builder: (context, state) {
        if (state.fetchNoteDetailState.isLoaded) {
          final note = state.note;

          return Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      SizedBox(height: context.makulaPadding.s),
                      DataListItem(
                        title: context.localization.createdOn,
                        trailing: Text(note.dateString),
                      ),
                      Divider(
                        color: context.makulaBorderColors.primary,
                        height: 1,
                      ),
                      DataListItem(
                        title: context.localization.createdBy,
                        trailing: Container(
                          width: MediaQuery.of(context).size.width * 0.5,
                          alignment: AlignmentDirectional.centerEnd,
                          child: UserTag(label: note.createdBy.name),
                        ),
                      ),
                      Divider(
                        color: context.makulaBorderColors.primary,
                        height: 1,
                      ),
                      Si<PERSON><PERSON>ox(height: context.makulaPadding.s),
                      const _SummaryListTile(),
                      SizedBox(height: context.makulaPadding.s),
                      DetailListItem(
                        title: context.localization.transcript,
                        icon: MakulaIcons.record,
                        child: Padding(
                          padding: EdgeInsetsDirectional.only(
                            start: context.makulaPadding.l,
                            end: context.makulaPadding.l,
                            bottom: context.makulaPadding.l,
                          ),
                          child: ExpandableFormattedTextView(
                            note.content.transcript,
                            key: Key("summary${note.content.summary}"),
                            onShowLess: () {},
                          ),
                        ),
                      ),
                      SizedBox(height: context.makulaPadding.s),
                      DetailListItem(
                        title: context.localization.audio,
                        icon: MakulaIcons.play,
                        child: Padding(
                          padding: EdgeInsetsDirectional.only(
                            start: context.makulaPadding.l,
                            end: context.makulaPadding.l,
                            bottom: context.makulaPadding.l,
                          ),
                          child: const _AudioDownload(),
                        ),
                      ),
                      SizedBox(height: context.makulaPadding.xxl),
                    ],
                  ),
                ),
              ),
              BottomActionBar(child: _ActionButtons(note: note)),
            ],
          );
        }

        if (state.fetchNoteDetailState.isFailure) {
          return FullScreenErrorWidget(
            errorMessage:
                state.fetchNoteDetailState.error ?? ExceptionMessage.general,
            onRetry: InhertedViewModel.of<NoteDetailsViewModel>(context)
                .viewModel
                .fetchNoteDetail,
          );
        }

        return const Center(child: MakulaLoader());
      },
    );
  }
}

class _SummaryListTile extends StatelessWidget {
  const _SummaryListTile();

  @override
  Widget build(BuildContext context) {
    return ViewModelListener<NoteDetailsViewModel, NoteDetailsState>(
      listenWhen: (previous, current) =>
          previous.translateSummary != current.translateSummary,
      listener: (context, state) {
        if (state.translateSummary.isFailure) {
          Alert.showError(
              errorMessage: context.localization.summaryTranslationError);
        }
      },
      child: ViewModelBuilder<NoteDetailsViewModel, NoteDetailsState>(
        builder: (context, state) {
          return DetailListItem(
            title: context.localization.summary,
            icon: MakulaIcons.leftAlign,
            trailing: (state.translateSummary.isLoading)
                ? const MakulaLoader()
                : Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      UserTag(
                        label: state.currentLanguage.label,
                        leadingIcon: MakulaIcons.externalIcon,
                      ),
                      SizedBox(width: context.makulaPadding.m),
                      MakulaIcon(
                        MakulaIcons.chevronRight,
                        color: context.makulaContentColors.primary,
                        size: 20,
                      ),
                    ],
                  ),
            onTap: () {
              Navigator.of(context).push(
                LanguageSelectionView.route(
                  onSelect: (language) {
                    InhertedViewModel.of<NoteDetailsViewModel>(context)
                        .viewModel
                        .translateSummary(language);
                  },
                  selectedLanguage: state.currentLanguage,
                ),
              );
            },
            child: Padding(
              padding: EdgeInsetsDirectional.only(
                start: context.makulaPadding.l,
                end: context.makulaPadding.l,
                bottom: context.makulaPadding.l,
              ),
              child: FormattedTextView(
                key: Key("summary${state.summary}"),
                state.summary,
              ),
            ),
          );
        },
      ),
    );
  }
}

class _ActionButtons extends StatelessWidget {
  const _ActionButtons({
    required this.note,
  });

  final AiNoteModel note;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: OutlineButton.medium(
            title: context.localization.editNote,
            leadingIcon: MakulaIcons.edit,
            onTap: () {
              AnalyticsService.instance.sendEvent(
                "Initiate Edit Ai Note",
                {"note_id": note.id},
              );
              Navigator.of(context).push(
                EditNotePage.route(
                  noteId: note.id,
                  content: note.content,
                ),
              );
            },
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: PrimaryButton.medium(
            title: context.localization.publishNote,
            leadingIcon: MakulaIcons.aiNoteTaker,
            onTap: () {
              Navigator.of(context).push(
                PublishAssetSelection.route(
                  viewModel: InhertedViewModel.of<NoteDetailsViewModel>(context)
                      .viewModel,
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}

class _AudioDownload extends StatelessWidget {
  const _AudioDownload();

  @override
  Widget build(BuildContext context) {
    return ViewModelListener<NoteDetailsViewModel, NoteDetailsState>(
      listener: (context, state) {
        if (state.audioFileState.isFailure) {
          Alert.showError(
            errorMessage: context.localization.audioDownloadError,
          );
        }
      },
      child: ViewModelBuilder<NoteDetailsViewModel, NoteDetailsState>(
        builder: (context, state) {
          if (state.audioFileState.isLoaded) {
            return _AudioPlayer(audioFile: state.audioFileState.data!);
          }

          return _NotReadyState(
            isLoading: state.audioFileState.isLoading,
            onTap: state.audioFileState.isLoading
                ? null
                : () {
                    InhertedViewModel.of<NoteDetailsViewModel>(context)
                        .viewModel
                        .downloadAudio(state.note.content.audioUrl);
                  },
          );
        },
      ),
    );
  }
}

class _NotReadyState extends StatelessWidget {
  const _NotReadyState({
    this.onTap,
    this.isLoading = false,
  });

  final VoidCallback? onTap;
  final bool isLoading;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
      decoration: BoxDecoration(
        border: Border.all(color: kGrey10),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SecondaryButton.smallIcon(
            icon: MakulaIcons.downloadPdf,
            onTap: onTap,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: isLoading
                ? const LinearProgressIndicator(minHeight: 3, color: kGrey40)
                : Container(
                    width: double.infinity,
                    height: 3,
                    color: kGrey20,
                  ),
          ),
          const SizedBox(width: 16),
          const TextView(
            text: '00:00',
            textFontWeight: FontWeight.w700,
            textColor: kTextColorDark,
          ),
        ],
      ),
    );
  }
}

class _AudioPlayer extends StatefulWidget {
  const _AudioPlayer({required this.audioFile});

  final File audioFile;

  @override
  State<_AudioPlayer> createState() => _AudioPlayerState();
}

class _AudioPlayerState extends State<_AudioPlayer> {
  final controller = PlayerController();
  late final StreamSubscription<PlayerState>? _subscription;
  late final StreamSubscription<int>? _durationSubscription;
  bool isInitialized = false;
  int currentDuration = 0;
  double waveWidth = 0;

  final playerWaveStyle = const PlayerWaveStyle(
    fixedWaveColor: kGrey40,
    liveWaveColor: kPrimaryColor,
    spacing: 2,
    waveThickness: 1,
  );

  @override
  void initState() {
    _subscription = controller.onPlayerStateChanged.listen((state) {
      setState(() {});
    });

    _durationSubscription =
        controller.onCurrentDurationChanged.listen((duration) {
      if (duration != 0) setState(() => currentDuration = duration);
    });

    super.initState();
  }

  @override
  void didChangeDependencies() {
    waveWidth = MediaQuery.sizeOf(context).width - 154;

    controller
        .preparePlayer(
          path: widget.audioFile.path,
          volume: 1.0,
          noOfSamples: playerWaveStyle.getSamplesForWidth(waveWidth),
        )
        .then((_) => setState(() => isInitialized = true));

    super.didChangeDependencies();
  }

  String formattedDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, "0");
    String twoDigitMinutes = twoDigits(duration.inMinutes.remainder(60).abs());
    String twoDigitSeconds = twoDigits(duration.inSeconds.remainder(60).abs());
    return "$twoDigitMinutes:$twoDigitSeconds";
  }

  @override
  void dispose() {
    controller.dispose();
    _subscription?.cancel();
    _durationSubscription?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!isInitialized) return const _NotReadyState();
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
      decoration: BoxDecoration(
        border: Border.all(color: kGrey10),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SecondaryButton.smallIcon(
            icon: controller.playerState.isPlaying
                ? MakulaIcons.pause
                : MakulaIcons.play,
            onTap: () async {
              if (controller.playerState.isPlaying) {
                await controller.pausePlayer();
              } else {
                controller.startPlayer(finishMode: FinishMode.pause);
              }
            },
          ),
          const SizedBox(width: 16),
          Expanded(
            child: AudioFileWaveforms(
              size: Size(waveWidth, 32),
              playerController: controller,
              enableSeekGesture: true,
              waveformType: WaveformType.fitWidth,
              waveformData: controller.waveformData,
              playerWaveStyle: playerWaveStyle,
            ),
          ),
          const SizedBox(width: 16),
          SizedBox(
            width: 34,
            child: FittedBox(
              child: TextView(
                text:
                    formattedDuration(Duration(milliseconds: currentDuration)),
                textFontWeight: FontWeight.w700,
                textColor: kTextColorDark,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class LanguageSelectionView extends StatefulWidget {
  const LanguageSelectionView({
    super.key,
    required this.onSelect,
    this.selectedLanguage,
  });

  final ValueChanged<NoteTakerLanguage> onSelect;
  final NoteTakerLanguage? selectedLanguage;

  static Route route({
    required ValueChanged<NoteTakerLanguage> onSelect,
    required NoteTakerLanguage? selectedLanguage,
  }) {
    return MaterialPageRoute<void>(
      builder: (_) => LanguageSelectionView(
        onSelect: onSelect,
        selectedLanguage: selectedLanguage,
      ),
    );
  }

  @override
  State<LanguageSelectionView> createState() => _LanguageSelectionViewState();
}

class _LanguageSelectionViewState extends State<LanguageSelectionView> {
  String searchQuery = '';

  List<NoteTakerLanguage> get filteredLanguages => NoteTakerLanguage.all
      .where(
        (language) =>
            language.label.toLowerCase().contains(searchQuery.toLowerCase()) ||
            language.code.toLowerCase().contains(searchQuery.toLowerCase()),
      )
      .toList();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MakulaBasicAppBar(
        title: context.localization.summaryLanguage,
      ),
      body: Column(
        children: [
          Container(
            decoration: BoxDecoration(
              color: context.makulaBackgroundColors.primary,
              border: Border(
                bottom: BorderSide(color: context.makulaBorderColors.primary),
              ),
            ),
            padding: EdgeInsets.symmetric(
              horizontal: context.makulaPadding.l,
              vertical: context.makulaPadding.m,
            ),
            child: MakulaSearchField(
              onChanged: (String value) => setState(() => searchQuery = value),
            ),
          ),
          Expanded(
            child: filteredLanguages.isEmpty
                ? Center(child: Text(context.localization.noResultFound))
                : ListView.separated(
                    padding: EdgeInsets.only(top: context.makulaPadding.s),
                    itemCount: filteredLanguages.length,
                    separatorBuilder: (context, index) => Divider(
                      height: 1,
                      color: context.makulaBorderColors.primary,
                    ),
                    itemBuilder: (context, index) {
                      final language = filteredLanguages[index];
                      return DataListItem(
                        titleWidget: Text(
                          language.label,
                          style: context.makulaTypography.body.medium
                              .primary(context),
                        ),
                        subtitleWidget: Text(
                          language.code,
                          style: context.makulaTypography.body.small
                              .secondary(context),
                        ),
                        onTap: () {
                          widget.onSelect(language);
                          Navigator.of(context).pop();
                        },
                        trailingIcon: widget.selectedLanguage == language
                            ? MakulaIcons.check
                            : null,
                      );
                    },
                  ),
          ),
          SizedBox(height: MediaQuery.viewInsetsOf(context).bottom),
        ],
      ),
    );
  }
}
