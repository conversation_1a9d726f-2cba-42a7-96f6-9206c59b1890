part of 'view.dart';

class PublishedNotesView extends StatelessWidget {
  const PublishedNotesView({super.key});

  String formattedDate(DateTime date) {
    final formatter = DateFormat('MMMM yyyy');
    return formatter.format(date);
  }

  @override
  Widget build(BuildContext context) {
    return ViewModelListener<NoteDetailsViewModel, NoteDetailsState>(
      listenWhen: (previous, current) =>
          previous.unPublishNoteState != current.unPublishNoteState,
      listener: (context, state) {
        if (state.unPublishNoteState.isLoaded) {
          Alert.showSuccess(
            message: context.localization.noteUnpublishedSuccessfully,
          );
        }

        if (state.unPublishNoteState.isFailure) {
          Alert.showError(
            errorMessage:
                state.unPublishNoteState.error ?? ExceptionMessage.general,
          );
        }
      },
      child: ViewModelBuilder<NoteDetailsViewModel, NoteDetailsState>(
        builder: (context, state) {
          if (state.publishedNotes.isEmpty) {
            return Center(
              child: Text(
                context.localization.noPublishedNotes,
                style: context.makulaTypography.body.large.secondary(context),
              ),
            );
          }

          return ListView.separated(
            itemCount: state.publishedNotes.length,
            padding: EdgeInsets.only(
              top: context.makulaPadding.xs,
              bottom: 80 + MediaQuery.viewInsetsOf(context).bottom,
            ),
            separatorBuilder: (context, index) => SizedBox(
              height: context.makulaPadding.xs,
            ),
            itemBuilder: (context, index) {
              final date = state.publishedNotes.keys.toList()[index];
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: double.infinity,
                    color: context.makulaBackgroundColors.primary,
                    padding: EdgeInsets.all(context.makulaPadding.l),
                    child: Text(
                      formattedDate(date),
                      style: context.makulaTypography.label.medium
                          .secondary(context),
                    ),
                  ),
                  for (final note in state.publishedNotes[date]!)
                    _NoteTile(note: note),
                ],
              );
            },
          );
        },
      ),
    );
  }
}

class _NoteTile extends StatelessWidget {
  const _NoteTile({required this.note});

  final PublishedNoteModel note;

  @override
  Widget build(BuildContext context) {
    final asset = note.asset;
    return ListTile(
      tileColor: context.makulaBackgroundColors.primary,
      shape: const Border(
        bottom: BorderSide(color: kGrey10),
      ),
      title: Text(
        asset.name,
        style: context.makulaTypography.label.large.primary(context),
      ),
      subtitle: Text(
        "${note.dateTimeString} • SN: ${asset.serialNumber}",
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
        style: context.makulaTypography.body.small.secondary(context),
      ),
      leading: MachineThumbnail(imageUrl: asset.image),
      trailing: InkWell(
        customBorder: const CircleBorder(),
        child: Container(
          padding: const EdgeInsets.all(2),
          decoration: BoxDecoration(
            border: Border.all(
              color: context.makulaContentColors.secondary,
              width: 1,
            ),
            shape: BoxShape.circle,
          ),
          child: MakulaIcon(
            MakulaIcons.minus,
            size: 16,
            color: context.makulaContentColors.secondary,
          ),
        ),
        onTap: () {
          InhertedViewModel.of<NoteDetailsViewModel>(context)
              .viewModel
              .removePublishedNote(note);
        },
      ),
    );
  }
}
