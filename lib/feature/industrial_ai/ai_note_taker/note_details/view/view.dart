import 'dart:async';
import 'dart:io';

import 'package:audio_waveforms/audio_waveforms.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:makula_flutter/core/core.dart';
import 'package:makula_flutter/core/ui/components/alert.dart';
import 'package:makula_flutter/core/ui/components/text_view.dart';
import 'package:makula_flutter/core/utils/utils.dart';
import 'package:makula_flutter/feature/industrial_ai/ai_note_taker/edit_note/view/view.dart';
import 'package:makula_flutter/feature/industrial_ai/ai_note_taker/model/model.dart';
import 'package:makula_flutter/feature/industrial_ai/ai_note_taker/note_details/view_model/note_details_view_model.dart';
import 'package:makula_flutter/feature/assets/asset_selection/view/view.dart';
import 'package:makula_flutter/feature/cmms/work_orders/work_order_overview/procedure/procedure_overview/procedure_overview.dart';
import 'package:makula_flutter/feature/cmms/work_orders/work_order_overview/procedure/procedure_overview/widgets/generic_two_button_bottom_sheet.dart';
import 'package:makula_flutter/feature/feature_modules.dart';
import 'package:makula_flutter/feature/industrial_ai/ai_note_taker/note_listing/note_listing.dart';
import 'package:makula_flutter/service/analytics/analytics_service.dart';
import 'package:makula_theme/makula_theme.dart';

part 'note_detail_page.dart';
part 'note_detail_view.dart';
part 'publish_asset_selection.dart';
part 'published_notes_view.dart';
part 'note_overview.dart';
