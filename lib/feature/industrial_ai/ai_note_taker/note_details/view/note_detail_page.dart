part of 'view.dart';

class NoteDetailPage extends StatelessWidget {
  const NoteDetailPage({
    super.key,
    required this.id,
    this.note,
  });

  final String id;
  final AiNoteModel? note;

  static String pageName() => ':noteId';

  static String pageRoute({required String noteId}) =>
      '${NoteListingPage.pageRoute()}/${pageName().replaceFirst(':noteId', noteId)}';

  static Route route({required String id, required AiNoteModel? note}) {
    return MaterialPageRoute<void>(
      builder: (_) => NoteDetailPage(id: id, note: note),
    );
  }

  @override
  Widget build(BuildContext context) {
    return ViewModelProvider<NoteDetailsViewModel>(
      create: () => NoteDetailsViewModel(
        aiNoteTakerService: get<FeatureModules>()!.aiNoteTakerService,
        storageService: get<FeatureModules>()!.storageSerivce,
        id: id,
        note: note,
      )..fetchNoteDetail(),
      child: const NoteDetailView(),
    );
  }
}
