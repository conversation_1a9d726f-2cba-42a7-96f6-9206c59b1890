part of 'view.dart';

class PublishAssetSelection extends StatelessWidget {
  const PublishAssetSelection({
    super.key,
    required this.viewModel,
  });

  static Route route({
    required NoteDetailsViewModel viewModel,
  }) {
    return MaterialPageRoute<void>(
      builder: (_) => PublishAssetSelection(
        viewModel: viewModel,
      ),
    );
  }

  final NoteDetailsViewModel viewModel;

  @override
  Widget build(BuildContext context) {
    return InhertedViewModel(
      viewModel: viewModel
        ..resetPublishState()
        ..clearSelectedAssets(),
      child: const PublishAssetSelectionView(),
    );
  }
}

class PublishAssetSelectionView extends StatelessWidget {
  const PublishAssetSelectionView({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MakulaBasicAppBar(
        backgroundColor: context.makulaBackgroundColors.accent,
        title: context.localization.publishSummary,
      ),
      backgroundColor: context.makulaBackgroundColors.primary,
      body: Column(
        children: [
          Padding(
            padding: EdgeInsets.all(context.makulaPadding.l),
            child: Text(
              context.localization.publishAssetSelectionDescription,
              style: context.makulaTypography.body.medium.secondary(context),
            ),
          ),
          const Expanded(child: _AssetList()),
          const BottomActionBar(child: _PublishButton()),
        ],
      ),
    );
  }
}

class _AssetList extends StatelessWidget {
  const _AssetList();

  @override
  Widget build(BuildContext context) {
    return ViewModelBuilder<NoteDetailsViewModel, NoteDetailsState>(
      buildWhen: (previous, current) =>
          previous.note.publishedNotes != current.note.publishedNotes,
      builder: (context, state) {
        return AssetSelectionPage(
          onSelectionChanged: (value) {
            InhertedViewModel.of<NoteDetailsViewModel>(context)
                .viewModel
                .selectedAssetsChanged(value);
          },
        );
      },
    );
  }
}

class _PublishButton extends StatelessWidget {
  const _PublishButton();

  @override
  Widget build(BuildContext context) {
    return ViewModelListener<NoteDetailsViewModel, NoteDetailsState>(
      listenWhen: (previous, current) =>
          previous.publishNoteState != current.publishNoteState,
      listener: (context, state) {
        if (state.publishNoteState.isLoaded) {
          Navigator.of(context).pop();
          Alert.showSuccess(
              message: context.localization.notePublishedSuccessMessage);
        }
        if (state.publishNoteState.isFailure) {
          Alert.showError(errorMessage: state.publishNoteState.error);
        }
      },
      child: ViewModelBuilder<NoteDetailsViewModel, NoteDetailsState>(
        builder: (context, state) {
          return PrimaryButton.medium(
            title: context.localization.publishNote,
            isLoading: state.publishNoteState.isLoading,
            onTap: state.selectedAssets.isEmpty
                ? null
                : InhertedViewModel.of<NoteDetailsViewModel>(context)
                    .viewModel
                    .publishNote,
          );
        },
      ),
    );
  }
}
