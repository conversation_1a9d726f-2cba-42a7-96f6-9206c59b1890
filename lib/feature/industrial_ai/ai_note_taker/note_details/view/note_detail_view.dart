part of 'view.dart';

enum NoteDetailTab {
  overview,
  publishedNotes;

  String title(AppLocalizations localization) {
    switch (this) {
      case overview:
        return localization.overview;
      case publishedNotes:
        return localization.publishedNotes;
    }
  }

  Widget getTabView() {
    switch (this) {
      case overview:
        return const NoteOverview();
      case publishedNotes:
        return const PublishedNotesView();
    }
  }
}

class NoteDetailView extends StatelessWidget {
  const NoteDetailView({super.key});

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 2,
      child: ViewModelBuilder<NoteDetailsViewModel, NoteDetailsState>(
        builder: (context, state) {
          final note = state.note;

          return LoadingOverlay(
            isLoading: state.unPublishNoteState.isLoading,
            child: Scaffold(
              backgroundColor: context.makulaBackgroundColors.accent,
              appBar: MakulaBasicAppBar(
                title: note.content.title,
                backgroundColor: context.makulaBackgroundColors.primary,
                actions: state.note.transcriptionStatus.isComplete
                    ? const [_DeleteButton()]
                    : null,
                bottom: state.note.transcriptionStatus.isComplete
                    ? TabBar(
                        dividerHeight: 1.0,
                        dividerColor: context.makulaBorderColors.primary,
                        overlayColor: WidgetStateProperty.all(
                          context
                              .makulaBackgroundColors.accent.withTransparency25,
                        ),
                        indicatorSize: TabBarIndicatorSize.tab,
                        labelStyle: context.makulaTypography.label.medium
                            .primary(context),
                        unselectedLabelStyle: context
                            .makulaTypography.label.medium
                            .secondary(context),
                        indicatorColor: context.makulaBorderColors.brand,
                        labelPadding: EdgeInsets.symmetric(
                          horizontal: context.makulaPadding.xl,
                        ),
                        tabs: NoteDetailTab.values
                            .map(
                              (tab) =>
                                  Tab(text: tab.title(context.localization)),
                            )
                            .toList(),
                      )
                    : null,
              ),
              body: state.note.transcriptionStatus.isInprogress
                  ? const _InProgressView()
                  : state.note.transcriptionStatus.isFailure
                      ? const _FailureView()
                      : TabBarView(
                          children: NoteDetailTab.values.map((tab) {
                            return tab.getTabView();
                          }).toList(),
                        ),
            ),
          );
        },
      ),
    );
  }
}

class _DeleteButton extends StatelessWidget {
  const _DeleteButton();

  @override
  Widget build(BuildContext context) {
    return ViewModelListener<NoteDetailsViewModel, NoteDetailsState>(
      listener: (context, state) {
        if (state.deleteNoteState.isLoaded) {
          Navigator.of(context).pop();
          Navigator.of(context).pop();

          Alert.showSuccess(message: context.localization.noteDeleted);
        }

        if (state.deleteNoteState.isFailure) {
          Alert.showError(
            errorMessage:
                state.deleteNoteState.error ?? ExceptionMessage.general,
          );
        }
      },
      child: IconButton(
        icon: const MakulaIcon(
          MakulaIcons.delete,
          size: 24,
        ),
        onPressed: () {
          showModalBottomSheet(
            context: context,
            builder: (BuildContext buildContext) {
              return InhertedViewModel(
                viewModel: InhertedViewModel.of<NoteDetailsViewModel>(context)
                    .viewModel,
                child: const _DeleteBottomSheet(),
              );
            },
          );
        },
      ),
    );
  }
}

class _DeleteBottomSheet extends StatelessWidget {
  const _DeleteBottomSheet();

  @override
  Widget build(BuildContext context) {
    return ViewModelBuilder<NoteDetailsViewModel, NoteDetailsState>(
      builder: (context, state) {
        return GenericTwoButtonBottomSheet(
          titleText: context.localization.deleteNoteTitle,
          bodyText: context.localization.deleteNoteMessage,
          positiveButtonText: context.localization.deleteNoteConfirm,
          negativeButtonText: context.localization.cancel,
          isLoading: state.deleteNoteState.isLoading,
          positiveCallback: () =>
              InhertedViewModel.of<NoteDetailsViewModel>(context)
                  .viewModel
                  .deleteNote(),
          negativeCallback: () => {
            Navigator.pop(context),
          },
        );
      },
    );
  }
}

class _InProgressView extends StatelessWidget {
  const _InProgressView();

  @override
  Widget build(BuildContext context) {
    return EmptyStateView(
      title: context.localization.generatingSummary,
      subtitle: context.localization.generatingSummaryMessage,
      badgeWidget: const SizedBox(
        width: 240,
        height: 80,
        child: Center(
          child: MakulaAnimation(
            MakulaAnimations.aiLoading,
            height: 80,
            width: 80,
          ),
        ),
      ),
    );
  }
}

class _FailureView extends StatelessWidget {
  const _FailureView();

  @override
  Widget build(BuildContext context) {
    return ViewModelListener<NoteDetailsViewModel, NoteDetailsState>(
      listenWhen: (previous, current) =>
          previous.retryTranscriptionState != current.retryTranscriptionState ||
          previous.deleteNoteState != current.deleteNoteState,
      listener: (context, state) {
        if (state.retryTranscriptionState.isFailure) {
          Alert.showError(
            errorMessage:
                state.retryTranscriptionState.error ?? ExceptionMessage.general,
          );
        }

        if (state.deleteNoteState.isLoaded) {
          Navigator.of(context).pop();

          Alert.showSuccess(message: context.localization.noteDeleted);
        }
      },
      child: ViewModelBuilder<NoteDetailsViewModel, NoteDetailsState>(
        builder: (context, state) {
          return EmptyStateView.error(
            title: context.localization.failedToGenerateSummaryTitle,
            subtitle: state.note.canRetry
                ? context.localization.failedToGenerateSummaryMessageWithRetry
                : context
                    .localization.failedToGenerateSummaryMessageWithoutRetry,
            child: state.note.canRetry
                ? Center(
                    child: OutlineButton.medium(
                      title: context.localization.retry,
                      isLoading: state.retryTranscriptionState.isLoading,
                      onTap: InhertedViewModel.of<NoteDetailsViewModel>(context)
                          .viewModel
                          .retryTranscription,
                    ),
                  )
                : Center(
                    child: OutlineButton.medium(
                      title: context.localization.deleteNote,
                      isLoading: state.deleteNoteState.isLoading,
                      onTap: InhertedViewModel.of<NoteDetailsViewModel>(context)
                          .viewModel
                          .deleteNote,
                    ),
                  ),
          );
        },
      ),
    );
  }
}
