part of 'note_details_view_model.dart';

typedef FetchNoteDetailState = GeneralResponseState<void>;
typedef RetryTranscriptionState = GeneralResponseState<void>;
typedef DeleteNoteState = GeneralResponseState<void>;
typedef PublishNoteState = GeneralResponseState<void>;
typedef UnPublishNoteState = GeneralResponseState<void>;
typedef AudioFileState = GeneralResponseState<File>;
typedef TranslateSummary = GeneralResponseState<String>;

class NoteDetailsState extends Equatable {
  final String id;
  final AiNoteModel note;
  final FetchNoteDetailState fetchNoteDetailState;
  final DeleteNoteState deleteNoteState;
  final PublishNoteState publishNoteState;
  final UnPublishNoteState unPublishNoteState;
  final AudioFileState audioFileState;
  final RetryTranscriptionState retryTranscriptionState;
  final List<NoteAssetModel> selectedAssets;
  final NoteTakerLanguage currentLanguage;
  final TranslateSummary translateSummary;

  const NoteDetailsState({
    required this.id,
    required this.note,
    required this.fetchNoteDetailState,
    required this.deleteNoteState,
    required this.publishNoteState,
    required this.unPublishNoteState,
    required this.audioFileState,
    required this.retryTranscriptionState,
    required this.selectedAssets,
    required this.translateSummary,
    required this.currentLanguage,
  });

  factory NoteDetailsState.initial({
    required String id,
    required AiNoteModel? note,
  }) =>
      NoteDetailsState(
        id: id,
        note: note ?? AiNoteModel.empty,
        fetchNoteDetailState: const FetchNoteDetailState(),
        deleteNoteState: const DeleteNoteState(),
        publishNoteState: const PublishNoteState(),
        unPublishNoteState: const UnPublishNoteState(),
        audioFileState: const AudioFileState(),
        retryTranscriptionState: const RetryTranscriptionState(),
        translateSummary: const TranslateSummary(),
        selectedAssets: const [],
        currentLanguage: note?.summaryLanguage ?? NoteTakerLanguage.fallback,
      );

  NoteDetailsState copyWith({
    AiNoteModel? note,
    FetchNoteDetailState? fetchNoteDetailState,
    DeleteNoteState? deleteNoteState,
    PublishNoteState? publishNoteState,
    UnPublishNoteState? unPublishNoteState,
    AudioFileState? audioFileState,
    RetryTranscriptionState? retryTranscriptionState,
    TranslateSummary? translateSummary,
    List<NoteAssetModel>? selectedAssets,
    NoteTakerLanguage? currentLanguage,
  }) {
    return NoteDetailsState(
      id: id,
      note: note ?? this.note,
      fetchNoteDetailState: fetchNoteDetailState ?? this.fetchNoteDetailState,
      unPublishNoteState: unPublishNoteState ?? this.unPublishNoteState,
      deleteNoteState: deleteNoteState ?? this.deleteNoteState,
      publishNoteState: publishNoteState ?? this.publishNoteState,
      audioFileState: audioFileState ?? this.audioFileState,
      selectedAssets: selectedAssets ?? this.selectedAssets,
      retryTranscriptionState:
          retryTranscriptionState ?? this.retryTranscriptionState,
      translateSummary: translateSummary ?? this.translateSummary,
      currentLanguage: currentLanguage ?? this.currentLanguage,
    );
  }

  List<PublishedNoteModel> get sortedPublishedNotes {
    List<PublishedNoteModel> mlist = List.from(note.publishedNotes);
    return mlist
      ..sort(
        (a, b) => b.createdDate.compareTo(a.createdDate),
      );
  }

  Map<DateTime, List<PublishedNoteModel>> get publishedNotes {
    Map<DateTime, List<PublishedNoteModel>> map = {};

    for (var item in sortedPublishedNotes) {
      final key = DateTime(item.createdDate.year, item.createdDate.month);
      if (map[key] == null) {
        map[key] = [item];
      } else {
        map[key]!.add(item);
      }
    }

    return map;
  }

  bool get isTranslated =>
      !translateSummary.state.isInitial &&
      (translateSummary.data != null && translateSummary.data!.isNotEmpty);

  File? get audioFile => audioFileState.data;

  String get summary =>
      isTranslated ? translateSummary.data! : note.content.summary;

  @override
  List<Object?> get props => [
        note,
        fetchNoteDetailState,
        unPublishNoteState,
        deleteNoteState,
        publishNoteState,
        audioFileState,
        retryTranscriptionState,
        selectedAssets,
        translateSummary,
        currentLanguage,
      ];
}
