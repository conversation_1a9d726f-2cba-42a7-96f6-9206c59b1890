import 'dart:async';
import 'dart:io';

import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:makula_flutter/core/core.dart';
import 'package:makula_flutter/core/utils/utils.dart';
import 'package:makula_flutter/feature/industrial_ai/ai_note_taker/model/model.dart';
import 'package:makula_flutter/feature/assets/model/model.dart';
import 'package:makula_flutter/service/ai_note_taker/ai_note_taker_service.dart';
import 'package:makula_flutter/service/analytics/analytics_service.dart';
import 'package:makula_flutter/service/service.dart';
import 'package:makula_flutter/service/storage/storage_service.dart';

part 'note_details_state.dart';

class NoteDetailsViewModel extends ObservableViewModel<NoteDetailsState> {
  NoteDetailsViewModel({
    required String id,
    required AiNoteTakerService aiNoteTakerService,
    required StorageService storageService,
    AiNoteModel? note,
  })  : _aiNoteTakerService = aiNoteTakerService,
        _storageService = storageService,
        super(NoteDetailsState.initial(id: id, note: note));

  final AiNoteTakerService _aiNoteTakerService;
  final StorageService _storageService;

  StreamSubscription<AiNoteModel?>? _subscription;

  @override
  void dispose() {
    _subscription?.cancel();
    super.dispose();
  }

  void resetPublishState() {
    updateState(
      state.copyWith(publishNoteState: state.publishNoteState.toInitial()),
    );
  }

  void clearSelectedAssets() {
    updateState(state.copyWith(selectedAssets: []));
  }

  void selectedAssetsChanged(List<AssetModel> machines) {
    updateState(
      state.copyWith(
        selectedAssets: machines.map(NoteAssetModel.fromAssetModel).toList(),
      ),
    );
  }

  void subscribeToStream(ValueChanged<AiNoteModel?> onNoteEmit) {
    _subscription?.cancel();
    _subscription = _aiNoteTakerService
        .getOwnNoteDetail(state.id)
        .map(
          (note) => note == null ? null : AiNoteModel.fromServiceModel(note),
        )
        .listen(onNoteEmit);
  }

  Future<void> fetchNoteDetail() async {
    updateState(
      state.copyWith(
          fetchNoteDetailState: state.fetchNoteDetailState.toLoading()),
    );

    try {
      final note = await _aiNoteTakerService.getNoteDetail(state.id);

      final file = await _storageService.getFile(note.content.audioUrl);

      subscribeToStream((note) {
        updateState(state.copyWith(
          note: note,
          fetchNoteDetailState: state.fetchNoteDetailState.toLoaded(),
          translateSummary: state.translateSummary.toInitial(),
          audioFileState:
              file == null ? null : state.audioFileState.toLoaded(data: file),
          currentLanguage: note?.summaryLanguage,
        ));
      });
    } on NoInternetException {
      updateState(
        state.copyWith(
          fetchNoteDetailState: state.fetchNoteDetailState.toFailure(
            error: ExceptionMessage.noInternet,
          ),
        ),
      );
    } catch (e) {
      updateState(
        state.copyWith(
          fetchNoteDetailState: state.fetchNoteDetailState.toFailure(
            error: ExceptionMessage.general,
          ),
        ),
      );
    }
  }

  Future<void> deleteNote() async {
    updateState(
      state.copyWith(
        deleteNoteState: state.deleteNoteState.toLoading(),
      ),
    );

    try {
      await _aiNoteTakerService.deleteNote(state.id);

      _subscription?.cancel();

      updateState(
        state.copyWith(
          deleteNoteState: state.deleteNoteState.toLoaded(),
        ),
      );
    } on NoInternetException {
      updateState(
        state.copyWith(
          deleteNoteState: state.deleteNoteState.toFailure(
            error: ExceptionMessage.noInternet,
          ),
        ),
      );
    } catch (e) {
      updateState(
        state.copyWith(
          deleteNoteState: state.deleteNoteState.toFailure(
            error: ExceptionMessage.general,
          ),
        ),
      );
    }
  }

  Future<void> publishNote() async {
    updateState(
      state.copyWith(
        publishNoteState: state.publishNoteState.toLoading(),
      ),
    );

    try {
      await _aiNoteTakerService.publishNote(
        PublishNoteModel(
          noteId: state.id,
          assets: state.selectedAssets,
        ).toServiceModel(),
      );

      AnalyticsService.instance.sendEvent(
        "AI Note Published to Machine",
        {"no_of_machines": state.selectedAssets.length},
      );

      updateState(
        state.copyWith(
          publishNoteState: state.publishNoteState.toLoaded(),
        ),
      );
    } on NoInternetException {
      updateState(
        state.copyWith(
          publishNoteState: state.publishNoteState.toFailure(
            error: ExceptionMessage.noInternet,
          ),
        ),
      );
    } catch (e) {
      updateState(
        state.copyWith(
          publishNoteState: state.publishNoteState.toFailure(
            error: ExceptionMessage.general,
          ),
        ),
      );
    }
  }

  Future<void> removePublishedNote(PublishedNoteModel note) async {
    updateState(
      state.copyWith(
        unPublishNoteState: state.unPublishNoteState.toLoading(),
      ),
    );

    try {
      await _aiNoteTakerService.removePublishedNote(
        note.toServiceModel(),
        state.id,
      );

      updateState(
        state.copyWith(
          unPublishNoteState: state.unPublishNoteState.toLoaded(),
        ),
      );
    } on NoInternetException {
      updateState(
        state.copyWith(
          unPublishNoteState: state.unPublishNoteState.toFailure(
            error: ExceptionMessage.noInternet,
          ),
        ),
      );
    } catch (e) {
      updateState(
        state.copyWith(
          unPublishNoteState: state.unPublishNoteState.toFailure(
            error: ExceptionMessage.general,
          ),
        ),
      );
    }
  }

  Future<void> downloadAudio(String url) async {
    updateState(
      state.copyWith(
        audioFileState: state.audioFileState.toLoading(),
      ),
    );

    try {
      final file = await _storageService.downloadFile(FileItem(fileURL: url));

      AnalyticsService.instance.sendEvent(
        "AI Note Audio Downloaded",
        {"note_id": state.id, "file_name": url},
      );

      updateState(
        state.copyWith(
          audioFileState: state.audioFileState.toLoaded(data: file),
        ),
      );
    } on NoInternetException {
      updateState(
        state.copyWith(
          audioFileState: state.audioFileState.toFailure(
            error: ExceptionMessage.noInternet,
          ),
        ),
      );
    } catch (e) {
      updateState(
        state.copyWith(
          audioFileState: state.audioFileState.toFailure(
            error: ExceptionMessage.general,
          ),
        ),
      );
    }
  }

  Future<void> retryTranscription() async {
    updateState(
      state.copyWith(
        retryTranscriptionState: state.retryTranscriptionState.toLoading(),
      ),
    );

    try {
      await _aiNoteTakerService.transcribeAiNote(state.id);

      updateState(
        state.copyWith(
          retryTranscriptionState: state.retryTranscriptionState.toLoaded(),
        ),
      );
    } on NoInternetException {
      updateState(
        state.copyWith(
          retryTranscriptionState: state.retryTranscriptionState.toFailure(
            error: ExceptionMessage.noInternet,
          ),
        ),
      );
    } catch (e) {
      updateState(
        state.copyWith(
          retryTranscriptionState: state.retryTranscriptionState.toFailure(
            error: ExceptionMessage.general,
          ),
        ),
      );
    }
  }

  Future<void> translateSummary(NoteTakerLanguage selectedLanguage) async {
    if (state.currentLanguage == selectedLanguage) {
      return;
    }

    if (selectedLanguage == state.note.summaryLanguage) {
      updateState(
        state.copyWith(
          translateSummary:
              state.translateSummary.toLoaded(data: state.note.content.summary),
          currentLanguage: selectedLanguage,
        ),
      );
      return;
    }

    updateState(
      state.copyWith(
        translateSummary: state.translateSummary.toLoading(),
      ),
    );

    try {
      final translation = await _aiNoteTakerService.translateAiNoteSummary(
        state.id,
        selectedLanguage.code,
      );

      updateState(
        state.copyWith(
          translateSummary: state.translateSummary.toLoaded(data: translation),
          currentLanguage: selectedLanguage,
        ),
      );
    } on NoInternetException {
      updateState(
        state.copyWith(
          translateSummary: state.translateSummary.toFailure(
            error: ExceptionMessage.noInternet,
          ),
        ),
      );
    } catch (e) {
      updateState(
        state.copyWith(
          translateSummary: state.translateSummary.toFailure(
            error: ExceptionMessage.general,
          ),
        ),
      );
    }
  }
}
