part of 'view.dart';

class EditNoteView extends StatelessWidget {
  const EditNoteView({super.key});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        closeKeyboard(context);
      },
      child: PopScope(
        canPop: false,
        child: Scaffold(
          backgroundColor: context.makulaBackgroundColors.primary,
          appBar: MakulaBasicAppBar(
            title: context.localization.editNoteScreenTitle,
            backgroundColor: context.makulaBackgroundColors.accent,
            leading: const _BackButton(),
          ),
          body: const _FormBody(),
        ),
      ),
    );
  }
}

class _BackButton extends StatelessWidget {
  const _BackButton();

  @override
  Widget build(BuildContext context) {
    return ViewModelBuilder<EditNoteViewModel, EditNoteState>(
      builder: (context, state) {
        return IconButton(
          icon: const MakulaIcon(MakulaIcons.back, size: 24),
          onPressed: () {
            closeKeyboard(context);
            if (state.isFormChanged) {
              showModalBottomSheet(
                context: context,
                builder: (_) => const ContinueWithoutSavingBottomSheet(),
              );
            } else {
              Navigator.of(context).pop();
            }
          },
        );
      },
    );
  }
}

class _FormBody extends StatelessWidget {
  const _FormBody();

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Expanded(
          child: ListView(
            padding: EdgeInsets.only(
              left: 16.0,
              right: 16.0,
              top: 20.0,
              bottom: 20.0 + MediaQuery.viewInsetsOf(context).bottom,
            ),
            children: const [
              _TitleField(),
              SizedBox(height: 16),
              _SummaryField(),
              SizedBox(height: 16),
              _TranscriptField(),
            ],
          ),
        ),
        const _BottomButtonWidget(),
      ],
    );
  }
}

class _TitleField extends StatelessWidget {
  const _TitleField();

  @override
  Widget build(BuildContext context) {
    return ViewModelBuilder<EditNoteViewModel, EditNoteState>(
      builder: (context, state) {
        return MakulaTextField(
          initialValue: state.title,
          label: context.localization.titleFieldLabel,
          hintText: context.localization.titleFieldHint,
          inputFormatters: [LengthLimitingTextInputFormatter(75)],
          onChanged: InhertedViewModel.of<EditNoteViewModel>(context)
              .viewModel
              .updateTitle,
        );
      },
    );
  }
}

class _SummaryField extends StatelessWidget {
  const _SummaryField();

  @override
  Widget build(BuildContext context) {
    return ViewModelBuilder<EditNoteViewModel, EditNoteState>(
      buildWhen: (previous, current) => previous.summary == current.summary,
      builder: (context, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TextFieldLabel(
              label: context.localization.summaryFieldLabel,
              isRequired: true,
            ),
            const SizedBox(height: 8),
            TextAreaFieldWidget(
              initalValue: state.summary,
              onChangeText: (value) {
                String stringValue = jsonEncode(value);
                InhertedViewModel.of<EditNoteViewModel>(context)
                    .viewModel
                    .updateSummary(stringValue);
              },
            ),
          ],
        );
      },
    );
  }
}

class _TranscriptField extends StatelessWidget {
  const _TranscriptField();

  @override
  Widget build(BuildContext context) {
    return ViewModelBuilder<EditNoteViewModel, EditNoteState>(
      builder: (context, state) {
        return MakulaTextField(
          initialValue: state.content.transcript,
          label: context.localization.transcriptFieldLabel,
          keyboardType: TextInputType.multiline,
          readOnly: true,
          maxLines: null,
        );
      },
    );
  }
}

class TextFieldLabel extends StatelessWidget {
  const TextFieldLabel({
    super.key,
    required this.label,
    required this.isRequired,
  });

  final String label;
  final bool isRequired;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        TextView(
          text: label + (isRequired ? '*' : ''),
          fontSize: 12,
          textFontWeight: FontWeight.w500,
          textColor: kGrey60,
        ),
      ],
    );
  }
}

class _BottomButtonWidget extends StatelessWidget {
  const _BottomButtonWidget();

  @override
  Widget build(BuildContext context) {
    return ViewModelListener<EditNoteViewModel, EditNoteState>(
      listenWhen: (previous, current) =>
          previous.editNoteApiState != current.editNoteApiState,
      listener: (context, state) {
        if (state.editNoteApiState.isLoaded) {
          Navigator.of(context).pop();
          Alert.showSuccess(
            message: context.localization.noteUpdatedSuccessfully,
          );
        }

        if (state.editNoteApiState.isFailure) {
          Alert.showError(errorMessage: state.editNoteApiState.error);
        }
      },
      child: ViewModelBuilder<EditNoteViewModel, EditNoteState>(
        builder: (context, state) {
          return Container(
            padding: EdgeInsets.fromLTRB(
              20,
              12,
              20,
              12 + MediaQuery.paddingOf(context).bottom,
            ),
            width: double.infinity,
            decoration: const BoxDecoration(
              border: Border(
                top: BorderSide(color: kBorderColor),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: PrimaryButton.medium(
                    title: context.localization.editNote,
                    isLoading: state.editNoteApiState.isLoading,
                    onTap: state.isFormValid
                        ? () {
                            closeKeyboard(context);
                            InhertedViewModel.of<EditNoteViewModel>(context)
                                .viewModel
                                .editNote();
                          }
                        : null,
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
