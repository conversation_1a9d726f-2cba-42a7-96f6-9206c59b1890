part of 'view.dart';

class EditNotePage extends StatelessWidget {
  const EditNotePage({
    super.key,
    required this.noteId,
    required this.content,
  });

  final String noteId;
  final AiNoteContentModel content;

  static Route route({
    required String noteId,
    required AiNoteContentModel content,
  }) {
    return MaterialPageRoute<void>(
      builder: (_) => EditNotePage(
        noteId: noteId,
        content: content,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return ViewModelProvider<EditNoteViewModel>(
      create: () => EditNoteViewModel(
        noteId: noteId,
        aiNoteTakerService: get<FeatureModules>()!.aiNoteTakerService,
        content: content,
      ),
      child: const EditNoteView(),
    );
  }
}
