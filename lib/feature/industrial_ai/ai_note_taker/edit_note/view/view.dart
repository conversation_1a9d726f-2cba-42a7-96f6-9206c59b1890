import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:makula_flutter/core/core.dart';
import 'package:makula_flutter/core/ui/components/alert.dart';
import 'package:makula_flutter/core/ui/components/text_view.dart';
import 'package:makula_flutter/core/utils/utils.dart';
import 'package:makula_flutter/feature/industrial_ai/ai_note_taker/model/model.dart';
import 'package:makula_flutter/feature/cmms/work_orders/work_order_overview/procedure/procedure_overview/widgets/generic_two_button_bottom_sheet.dart';
import 'package:makula_flutter/feature/cmms/work_orders/work_order_overview/procedure/procedure_overview/widgets/procedure_item/text_area_field.dart';
import 'package:makula_flutter/feature/feature_modules.dart';
import 'package:makula_theme/makula_theme.dart';

import '../view_model/edit_note_view_model.dart';

part 'edit_note_page.dart';
part 'edit_note_view.dart';
