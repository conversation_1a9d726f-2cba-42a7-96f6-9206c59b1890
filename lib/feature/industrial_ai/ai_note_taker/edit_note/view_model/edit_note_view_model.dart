import 'package:equatable/equatable.dart';
import 'package:makula_flutter/core/core.dart';
import 'package:makula_flutter/core/utils/utils.dart';
import 'package:makula_flutter/feature/industrial_ai/ai_note_taker/model/model.dart';
import 'package:makula_flutter/service/ai_note_taker/ai_note_taker_service.dart';
import 'package:makula_flutter/service/analytics/analytics_service.dart';
import 'package:makula_flutter/service/service.dart';

part 'edit_note_state.dart';

class EditNoteViewModel extends ObservableViewModel<EditNoteState> {
  EditNoteViewModel({
    required String noteId,
    required AiNoteContentModel content,
    required AiNoteTakerService aiNoteTakerService,
  })  : _aiNoteTakerService = aiNoteTakerService,
        super(
          EditNoteState.initial(noteId: noteId, content: content),
        );

  final AiNoteTakerService _aiNoteTakerService;

  Future<void> editNote() async {
    updateState(
      state.copyWith(editNoteApiState: state.editNoteApiState.toLoading()),
    );

    try {
      await _aiNoteTakerService.editAiNote(
        state.noteId,
        state.formData.toServiceModel(),
      );

      AnalyticsService.instance.sendEvent(
        "Ai Note Edited",
        {"note_id": state.noteId},
      );

      updateState(
        state.copyWith(
          editNoteApiState: state.editNoteApiState.toLoaded(),
        ),
      );
    } on NoInternetException {
      updateState(
        state.copyWith(
          editNoteApiState: state.editNoteApiState.toFailure(
            error: ExceptionMessage.noInternet,
          ),
        ),
      );
    } catch (e) {
      updateState(
        state.copyWith(
          editNoteApiState: state.editNoteApiState.toFailure(
            error: ExceptionMessage.general,
          ),
        ),
      );
    }
  }

  void updateTitle(String title) {
    updateState(state.copyWith(title: title));
  }

  void updateSummary(String summary) {
    updateState(state.copyWith(summary: summary));
  }
}
