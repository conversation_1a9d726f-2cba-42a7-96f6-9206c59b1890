part of 'edit_note_view_model.dart';

typedef EditNoteApiState = GeneralResponseState<void>;

class EditNoteState extends Equatable {
  final String noteId;
  final EditNoteApiState editNoteApiState;
  final AiNoteContentModel content;
  final String title;
  final String summary;

  const EditNoteState({
    required this.noteId,
    required this.editNoteApiState,
    required this.content,
    required this.title,
    required this.summary,
  });

  factory EditNoteState.initial({
    required String noteId,
    required AiNoteContentModel content,
  }) =>
      EditNoteState(
        noteId: noteId,
        editNoteApiState: const EditNoteApiState(),
        content: content,
        title: content.title,
        summary: content.summary,
      );

  EditNoteState copyWith({
    EditNoteApiState? editNoteApiState,
    String? title,
    String? summary,
  }) {
    return EditNoteState(
      noteId: noteId,
      content: content,
      editNoteApiState: editNoteApiState ?? this.editNoteApiState,
      title: title ?? this.title,
      summary: summary ?? this.summary,
    );
  }

  bool get isFormValid {
    return title.trim().isNotEmpty &&
        !isSlateJsStringEmpty(summary) &&
        isFormChanged;
  }

  bool get isFormChanged =>
      title != content.title || summary != content.summary;

  EditableAiNoteModel get formData => EditableAiNoteModel(
        title: title,
        summary: summary,
        audioUrl: content.audioUrl,
        transcript: content.transcript,
      );

  @override
  List<Object> get props => [
        editNoteApiState,
        title,
        summary,
      ];
}
