part of 'view.dart';

class NoteListingView extends StatelessWidget {
  const NoteListingView({super.key});

  @override
  Widget build(BuildContext context) {
    return ViewModelBuilder<NoteListingViewModel, NoteListingState>(
      buildWhen: (previous, current) =>
          previous.fetchAiNoteAccess != current.fetchAiNoteAccess,
      builder: (context, state) {
        return Scaffold(
          backgroundColor: context.makulaBackgroundColors.accent,
          appBar: ProductAppBar(
            title: context.localization.aiNotetaker,
            product: ProductModel.industralAi,
            actions: state.hasAiNoteAccess ? [qouta(context)] : [],
          ) as PreferredSizeWidget,
          body: state.fetchAiNoteAccess.isLoaded
              ? state.hasAiNoteAccess
                  ? const _Body()
                  : const NoAccessBody()
              : state.fetchAiNoteAccess.isFailure
                  ? EmptyStateView.warning(
                      title: ExceptionMessage.general,
                      child: Center(
                        child: OutlineButton.medium(
                          title: context.localization.retry,
                          onTap: () {
                            InhertedViewModel.of<NoteListingViewModel>(context)
                                .viewModel
                                .checkAiNoteAccess();
                          },
                        ),
                      ),
                    )
                  : const Center(child: MakulaLoader()),
          floatingActionButton:
              state.hasAiNoteAccess ? const TakeNoteButton() : null,
        );
      },
    );
  }

  IconButton qouta(BuildContext context) {
    return IconButton(
      icon: const MakulaIcon(
        MakulaIcons.error,
        color: kTextColorDark,
        size: 24,
      ),
      onPressed: () {
        MakulaBottomSheet(
          useRootNavigator: true,
          title: context.localization.recordingLimit,
          builder: (_) => InhertedViewModel<NoteListingViewModel>(
            viewModel:
                InhertedViewModel.of<NoteListingViewModel>(context).viewModel,
            child: Column(
              children: [
                const _QuotaWidget(),
                SizedBox(
                  height: MediaQuery.viewInsetsOf(context).bottom + 50,
                ),
              ],
            ),
          ),
        ).show(context);
      },
    );
  }
}

class NoAccessBody extends StatelessWidget {
  const NoAccessBody({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return EmptyStateView(
      title: context.localization.aiNotetakerAccess,
      subtitleChild: RichText(
        textAlign: TextAlign.center,
        text: TextSpan(
          text: context.localization.oemAiAccessMessage,
          style: context.makulaTypography.body.medium.secondary(context),
          children: [
            TextSpan(
              text: Constants.supportEmail,
              style: context.makulaTypography.body.medium.copyWith(
                color: context.makulaContentColors.brand,
              ),
              recognizer: TapGestureRecognizer()
                ..onTap = () async {
                  final couldNotOpenEmail =
                      context.localization.couldNotOpenEmail;

                  try {
                    await launchEmail(Constants.supportEmail);
                  } catch (e) {
                    Alert.showError(errorMessage: couldNotOpenEmail);
                  }
                },
            ),
          ],
        ),
      ),
      illustration: MakulaIllustrations.mail,
    );
  }
}

class _Body extends StatefulWidget {
  const _Body();

  @override
  State<_Body> createState() => _BodyState();
}

class _BodyState extends State<_Body> {
  final _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ViewModelListener<NoteListingViewModel, NoteListingState>(
      listenWhen: (previous, current) =>
          previous.fetchNotesState.searchQuery !=
          current.fetchNotesState.searchQuery,
      listener: (context, state) {
        if (state.fetchNotesState.searchQuery.isEmpty) {
          _searchController.clear();
        }
      },
      child: Column(
        children: [
          Container(
            color: context.makulaBackgroundColors.primary,
            padding: EdgeInsets.symmetric(
              vertical: context.makulaPadding.m,
              horizontal: context.makulaPadding.l,
            ),
            child: MakulaSearchField(
              controller: _searchController,
              onChanged: InhertedViewModel.of<NoteListingViewModel>(context)
                  .viewModel
                  .searchQueryChanged,
            ),
          ),
          const Expanded(child: _NoteListing()),
        ],
      ),
    );
  }
}

class _QuotaWidget extends StatelessWidget {
  const _QuotaWidget();

  @override
  Widget build(BuildContext context) {
    return ViewModelBuilder<NoteListingViewModel, NoteListingState>(
      buildWhen: (previous, current) =>
          previous.fetchConfigurationsState != current.fetchConfigurationsState,
      builder: (context, state) {
        if (state.fetchConfigurationsState.isLoaded) {
          final config = state.fetchConfigurationsState.data!;
          return ColoredBox(
            color: context.makulaBackgroundColors.primary,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 8),
                  LinearProgressIndicator(
                    value: config.consumedQuota / config.totalQuota,
                    backgroundColor: context.makulaBackgroundColors.accent,
                    valueColor: AlwaysStoppedAnimation(
                      config.isQuotaExceeded
                          ? context.makulaBackgroundColors.warning
                          : context.makulaBackgroundColors.brandDark,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    context.localization.aiNoteQuota(
                      config.formattedConsumedQuota,
                      config.formattedTotalQuota,
                    ),
                    style:
                        context.makulaTypography.body.small.secondary(context),
                  ),
                ],
              ),
            ),
          );
        } else {
          return const SizedBox.shrink();
        }
      },
    );
  }
}

class _NoteListing extends StatelessWidget {
  const _NoteListing();

  String formattedDate(DateTime date) {
    final formatter = DateFormat('MMMM yyyy');
    return formatter.format(date);
  }

  @override
  Widget build(BuildContext context) {
    return ViewModelListener<NoteListingViewModel, NoteListingState>(
      listenWhen: (previous, current) =>
          previous.fetchNotesState != current.fetchNotesState,
      listener: (context, state) {
        if (state.fetchNotesState.apiState.isFailure) {
          Alert.showError(errorMessage: state.fetchNotesState.apiState.error);
        }
      },
      child: ViewModelBuilder<NoteListingViewModel, NoteListingState>(
        builder: (context, state) {
          if (state.fetchNotesState.isLoading || !state.isIntialLoaded) {
            return const Center(child: MakulaLoader());
          }
          if (state.fetchNotesState.isFailure) {
            return FullScreenErrorWidget(
              onRetry: () => InhertedViewModel.of<NoteListingViewModel>(
                context,
              ).viewModel.fetchNotes(forceRefresh: true),
            );
          }
          if (state.notes.isEmpty &&
              state.fetchNotesState.apiState.isLoaded &&
              state.fetchNotesState.searchQuery.isNotEmpty) {
            return Center(
              child: TextView(
                text: context.localization.noNotesFound,
                fontSize: 14,
                textFontWeight: FontWeight.w500,
                textColor: kTextColorLight,
              ),
            );
          }

          if (state.notes.isEmpty) {
            return const _EmptyNoteListingBody();
          }

          return PaginatedListWidget(
            padding: EdgeInsets.only(
              top: context.makulaPadding.xs,
              bottom: 80 + MediaQuery.viewInsetsOf(context).bottom,
            ),
            hasMoreItems: state.fetchNotesState.hasMoreData,
            onPullRefresh: () async {
              if (state.fetchNotesState.isPaginatedLoading) {
                return;
              }
              return InhertedViewModel.of<NoteListingViewModel>(context)
                  .viewModel
                  .fetchNotes(forceRefresh: true);
            },
            onScrollEndReached: () {
              if (state.fetchNotesState.apiState.isFailure) return;
              if (state.fetchNotesState.apiState.isLoading) return;
              if (!state.fetchNotesState.isPaginatedLoading) {
                InhertedViewModel.of<NoteListingViewModel>(context)
                    .viewModel
                    .fetchNotes();
              }
            },
            bottomWidget: state.fetchNotesState.apiState.isFailure
                ? const SizedBox.shrink()
                : const Center(child: MakulaLoader()),
            itemCount: state.notesByDate.length,
            separatorBuilder: (context, index) => SizedBox(
              height: context.makulaPadding.xs,
            ),
            itemBuilder: (_, index) {
              final date = state.noteDates[index];
              return Material(
                color: context.makulaBackgroundColors.primary,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      width: double.infinity,
                      color: context.makulaBackgroundColors.primary,
                      padding: EdgeInsets.all(context.makulaPadding.l),
                      child: Text(
                        formattedDate(date),
                        style: context.makulaTypography.label.medium
                            .secondary(context),
                      ),
                    ),
                    for (final note in state.notesByDate[date]!) ...[
                      _AINoteCard(note: note),
                      Divider(
                        color: context.makulaBorderColors.primary,
                        height: 1,
                      ),
                    ]
                  ],
                ),
              );
            },
          );
        },
      ),
    );
  }
}

class _AINoteCard extends StatelessWidget {
  const _AINoteCard({required this.note});

  final AiNoteModel note;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        AnalyticsService.instance.sendEvent(
          'View Note Details',
          {'note_id': note.id},
        );

        context.push(NoteDetailPage.pageRoute(noteId: note.id), extra: note);
      },
      highlightColor: context.makulaBackgroundColors.highlight,
      splashColor: context.makulaBackgroundColors.splash,
      child: Ink(
        color: context.makulaBackgroundColors.primary,
        padding: EdgeInsets.all(context.makulaPadding.l),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            MakulaCardIcon.large(
              icon: MakulaIcons.aiNote,
              tagColor: context.makulaTagColors.violet,
              secondaryIconWidget: buildStatusBadge(
                context: context,
                status: note.transcriptionStatus,
              ),
            ),
            SizedBox(width: context.makulaPadding.l),
            Expanded(
              child: Row(
                children: [
                  Flexible(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            Flexible(
                              child: Text(
                                note.content.title,
                                style: context.makulaTypography.label.large
                                    .primary(context),
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: context.makulaPadding.xxs),
                        Text(
                          note.dateTimeString,
                          style: context.makulaTypography.body.small
                              .secondary(context),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget? buildStatusBadge({
    required BuildContext context,
    required TranscriptionStatusModel status,
  }) {
    if (status.isInprogress) {
      return Container(
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: context.makulaBackgroundColors.warning,
          border: Border.all(
            color: context.makulaBorderColors.inverse,
            width: 1,
          ),
        ),
        child: Center(
          child: MakulaIcon(
            MakulaIcons.loader,
            size: 14,
            color: context.makulaContentColors.inverse,
          ),
        ),
      );
    }
    if (status.isFailure) {
      return Container(
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: context.makulaBackgroundColors.danger,
          border: Border.all(
            color: context.makulaBorderColors.inverse,
            width: 1,
          ),
        ),
        child: Center(
          child: MakulaIcon(
            MakulaIcons.info,
            size: 14,
            color: context.makulaContentColors.inverse,
          ),
        ),
      );
    }
    return null;
  }
}

class TakeNoteButton extends StatelessWidget {
  const TakeNoteButton({super.key});

  Future<bool> isMicrophonePermissionGranted() async {
    return await Permission.microphone.request().isGranted;
  }

  @override
  Widget build(BuildContext context) {
    return ViewModelBuilder<NoteListingViewModel, NoteListingState>(
      buildWhen: (previous, current) =>
          previous.fetchConfigurationsState != current.fetchConfigurationsState,
      builder: (context, state) {
        return MakulaFloatingActionButton(
          icon: MakulaIcons.record,
          onTap: () async {
            WakelockPlus.enable();

            AnalyticsService.instance.sendEvent('Initiate Audio Recording', {});

            if (state.fetchConfigurationsState.isLoaded &&
                state.fetchConfigurationsState.data!.consumedQuota >=
                    state.fetchConfigurationsState.data!.totalQuota) {
              Alert.showError(
                errorMessage: context.localization.aiNoteQuotaExceededMessage,
              );
              return;
            }

            if (await isMicrophonePermissionGranted()) {
              if (context.mounted) {
                closeKeyboard(context);
                InhertedViewModel.of<NoteListingViewModel>(context)
                    .viewModel
                    .clearSearchQuery();
                context.push(AudioInputPage.pageRoute());
              }
            } else {
              if (context.mounted) {
                Alert.showError(
                  errorMessage:
                      context.localization.microphonePermissionMessage,
                );
              }
            }
          },
        );
      },
    );
  }
}

class _EmptyNoteListingBody extends StatelessWidget {
  const _EmptyNoteListingBody();

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            TextView(
              text: context.localization.welcomeToAiNoteTaker1,
              fontSize: 18,
              textFontWeight: FontWeight.w500,
              textColor: kTextColorLight,
            ),
            TextView(
              text: context.localization.welcomeToAiNoteTaker2,
              fontSize: 24,
              textFontWeight: FontWeight.w700,
              textColor: kPrimaryColor,
            ),
            const SizedBox(height: 24),
            TextView(
              text: context.localization.welcomeToAiNoteTaker3,
              fontSize: 14,
              textFontWeight: FontWeight.w500,
              textColor: kTextColorLight,
            ),
            const SizedBox(height: 50),
          ],
        ),
      ),
    );
  }
}
