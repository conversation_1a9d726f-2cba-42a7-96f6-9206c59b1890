import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:makula_flutter/core/core.dart';
import 'package:makula_flutter/core/ui/components/alert.dart';
import 'package:makula_flutter/core/ui/components/text_view.dart';
import 'package:makula_flutter/core/utils/utils.dart';
import 'package:makula_flutter/feature/industrial_ai/ai_note_taker/ai_note_taker.dart';
import 'package:makula_flutter/feature/industrial_ai/ai_note_taker/note_details/note_details.dart';
import 'package:makula_flutter/feature/industrial_ai/ai_note_taker/note_listing/view_model/note_listing_view_model.dart';
import 'package:makula_flutter/feature/feature_modules.dart';
import 'package:makula_flutter/feature/industrial_ai/industrial_ai_dashboard/view/view.dart';
import 'package:makula_flutter/feature/product_dashboard/model/model.dart';
import 'package:makula_flutter/service/analytics/analytics_service.dart';
import 'package:makula_theme/makula_theme.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:wakelock_plus/wakelock_plus.dart';

part 'note_listing_page.dart';
part 'note_listing_view.dart';
