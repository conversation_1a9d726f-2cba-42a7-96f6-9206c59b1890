part of 'view.dart';

class NoteListingPage extends StatelessWidget {
  const NoteListingPage({super.key});

  static String pageName() => 'notetaker';

  static String pageRoute() =>
      '/${IndustrialAiDashboardPage.pageName()}/${pageName()}';

  @override
  Widget build(BuildContext context) {
    return ViewModelProvider<NoteListingViewModel>(
      create: () => NoteListingViewModel(
        aiNoteTakerService: get<FeatureModules>()!.aiNoteTakerService,
      )
        ..checkAiNoteAccess()
        ..initialize()
        ..fetchConfiguration()
        ..fetchNotes(forceRefresh: true),
      child: const NoteListingView(),
    );
  }
}
