part of 'note_listing_view_model.dart';

typedef FetchNotesState = PaginatedResponseState<AiNoteModel>;
typedef FetchConfigurationsState
    = GeneralResponseState<AiNoteConfigurationModel>;
typedef FetchNoteState = GeneralResponseState<AiNoteModel>;
typedef FetchAiNoteAccess = GeneralResponseState<bool>;

class NoteListingState extends Equatable {
  final FetchNotesState fetchNotesState;
  final FetchConfigurationsState fetchConfigurationsState;
  final FetchAiNoteAccess fetchAiNoteAccess;
  final bool isIntialLoaded;

  const NoteListingState({
    required this.fetchNotesState,
    required this.fetchConfigurationsState,
    required this.fetchAiNoteAccess,
    this.isIntialLoaded = false,
  });

  factory NoteListingState.initial() => NoteListingState(
        fetchNotesState: PaginatedResponseState.initial(),
        fetchConfigurationsState: const FetchConfigurationsState(),
        fetchAiNoteAccess: const FetchAiNoteAccess(),
      );

  NoteListingState copyWith({
    FetchNotesState? fetchNotesState,
    FetchConfigurationsState? fetchConfigurationsState,
    FetchAiNoteAccess? fetchAiNoteAccess,
    bool? isIntialLoaded,
  }) {
    return NoteListingState(
      fetchNotesState: fetchNotesState ?? this.fetchNotesState,
      fetchConfigurationsState:
          fetchConfigurationsState ?? this.fetchConfigurationsState,
      fetchAiNoteAccess: fetchAiNoteAccess ?? this.fetchAiNoteAccess,
      isIntialLoaded: isIntialLoaded ?? this.isIntialLoaded,
    );
  }

  List<AiNoteModel> get notes => fetchNotesState.data;

  AiListFilterModel get filter => AiListFilterModel(
        searchQuery: fetchNotesState.searchQuery,
      );

  bool get hasAiNoteAccess => fetchAiNoteAccess.data ?? false;

  Map<DateTime, List<AiNoteModel>> get notesByDate {
    final notes = fetchNotesState.data;
    final notesByDate = <DateTime, List<AiNoteModel>>{};

    for (final note in notes) {
      final date = DateTime(note.createdAt.year, note.createdAt.month);
      if (notesByDate.containsKey(date)) {
        notesByDate[date]!.add(note);
      } else {
        notesByDate[date] = [note];
      }
    }

    return notesByDate;
  }

  List<DateTime> get noteDates => notesByDate.keys.toList();

  @override
  List<Object?> get props => [
        fetchNotesState,
        fetchAiNoteAccess,
        fetchConfigurationsState,
        isIntialLoaded,
      ];
}
