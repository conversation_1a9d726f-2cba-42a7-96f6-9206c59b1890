import 'dart:async';

import 'package:equatable/equatable.dart';
import 'package:makula_flutter/core/core.dart';
import 'package:makula_flutter/core/utils/utils.dart';
import 'package:makula_flutter/feature/industrial_ai/ai_note_taker/model/model.dart';
import 'package:makula_flutter/feature/feature.dart';
import 'package:makula_flutter/service/ai_note_taker/ai_note_taker_service.dart';
import 'package:makula_flutter/service/service.dart';

part 'note_listing_state.dart';

class NoteListingViewModel extends ObservableViewModel<NoteListingState> {
  NoteListingViewModel({
    required AiNoteTakerService aiNoteTakerService,
  })  : _aiNoteTakerService = aiNoteTakerService,
        super(NoteListingState.initial());

  final AiNoteTakerService _aiNoteTakerService;
  StreamSubscription<List<AiNoteModel>>? _subscription;
  StreamSubscription<AiNoteConfigurationModel?>? _configurationSubscription;

  Timer? _searchDebounceTimer;

  @override
  void dispose() {
    _subscription?.cancel();
    _searchDebounceTimer?.cancel();
    _configurationSubscription?.cancel();
    super.dispose();
  }

  void initialize() {
    _subscription?.cancel();
    _subscription = _aiNoteTakerService.getNotes().map((notes) {
      return notes.map((note) => AiNoteModel.fromServiceModel(note)).toList();
    }).listen((notes) {
      updateState(
        state.copyWith(
          fetchNotesState: state.fetchNotesState.copyWith(
            apiState: state.fetchNotesState.apiState.toLoaded(data: notes),
          ),
        ),
      );
    });

    _configurationSubscription?.cancel();
    _configurationSubscription = _aiNoteTakerService
        .getAiNoteConfiguration()
        .map(
          (configuration) => configuration == null
              ? null
              : AiNoteConfigurationModel.fromServiceModel(configuration),
        )
        .listen(
      (configuration) {
        if (configuration == null) {
          updateState(
            state.copyWith(
              fetchConfigurationsState:
                  state.fetchConfigurationsState.toFailure(
                error: ExceptionMessage.general,
              ),
            ),
          );
          return;
        }
        updateState(
          state.copyWith(
            fetchConfigurationsState: state.fetchConfigurationsState.toLoaded(
              data: configuration,
            ),
          ),
        );
      },
    );
  }

  void searchQueryChanged(String value) {
    if (_searchDebounceTimer?.isActive ?? false) {
      _searchDebounceTimer?.cancel();
    }

    _searchDebounceTimer = Timer(
      const Duration(milliseconds: 500),
      () => fetchNotes(forceRefresh: true, searchQuery: value),
    );
  }

  void clearSearchQuery() {
    updateState(
      state.copyWith(
        fetchNotesState: state.fetchNotesState.copyWith(searchQuery: ''),
      ),
    );
  }

  Future<void> checkAiNoteAccess() async {
    await ServiceRequestHandlingStrategy<bool>(
      initialState: state.fetchAiNoteAccess,
      request: () async {
        return await _aiNoteTakerService.checkAiNoteAccess();
      },
      onChange: (responseState) => updateState(
        state.copyWith(fetchAiNoteAccess: responseState),
      ),
    ).execute();
  }

  Future<void> fetchConfiguration() async {
    updateState(
      state.copyWith(
        fetchConfigurationsState: state.fetchConfigurationsState.toLoading(),
      ),
    );

    try {
      final configuration =
          await _aiNoteTakerService.fetchAiNoteConfiguration();

      updateState(
        state.copyWith(
          fetchConfigurationsState: state.fetchConfigurationsState.toLoaded(
            data: AiNoteConfigurationModel.fromServiceModel(configuration),
          ),
        ),
      );
    } catch (e) {
      updateState(
        state.copyWith(
          fetchConfigurationsState: state.fetchConfigurationsState.toFailure(
            error: ExceptionMessage.general,
          ),
        ),
      );
    }
  }

  Future<void> fetchNotes({
    bool forceRefresh = false,
    String? searchQuery,
  }) async {
    updateState(
      state.copyWith(
        fetchNotesState: state.fetchNotesState.copyWith(
          apiState: state.fetchNotesState.apiState.toLoading(),
          searchQuery: searchQuery,
        ),
      ),
    );

    try {
      final hasMoreData = await _aiNoteTakerService.fetchNotes(
        state.filter.toServiceModel(),
        forceRefresh: forceRefresh,
      );

      updateState(
        state.copyWith(
          fetchNotesState: state.fetchNotesState.copyWith(
            apiState: state.fetchNotesState.apiState.toLoaded(),
            hasMoreData: hasMoreData,
          ),
          isIntialLoaded: true,
        ),
      );
    } on NoInternetException {
      updateState(
        state.copyWith(
          fetchNotesState: state.fetchNotesState.copyWith(
            apiState: state.fetchNotesState.apiState.toFailure(
              error: ExceptionMessage.noInternet,
            ),
          ),
          isIntialLoaded: true,
        ),
      );
    } catch (e) {
      updateState(
        state.copyWith(
          fetchNotesState: state.fetchNotesState.copyWith(
            apiState: state.fetchNotesState.apiState.toFailure(
              error: ExceptionMessage.general,
            ),
          ),
          isIntialLoaded: true,
        ),
      );
    }
  }
}
