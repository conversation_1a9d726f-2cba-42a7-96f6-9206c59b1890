part of 'model.dart';

class PublishNoteModel extends Equatable {
  final String noteId;
  final List<NoteAssetModel> assets;

  const PublishNoteModel({required this.noteId, required this.assets});

  PublishNote toServiceModel() {
    return PublishNote(
      noteId: noteId,
      assets: assets.map((e) => e.toServiceModel()).toList(),
    );
  }

  @override
  List<Object?> get props => [noteId, assets];
}
