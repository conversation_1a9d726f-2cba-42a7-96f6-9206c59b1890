import 'package:equatable/equatable.dart';
import 'package:intl/intl.dart';
import 'package:makula_flutter/core/core.dart';
import 'package:makula_flutter/core/utils/utils.dart';
import 'package:makula_flutter/feature/assets/model/model.dart';
import 'package:makula_flutter/service/ai_note_taker/ai_note_taker_service.dart';
import 'package:makula_flutter/service/user/model/user.dart';

part 'ai_note_model.dart';
part 'editable_ai_note_model.dart';
part 'ai_list_filter_model.dart';
part 'note_asset_model.dart';
part 'publish_note_model.dart';
part 'ai_note_configuration_model.dart';
