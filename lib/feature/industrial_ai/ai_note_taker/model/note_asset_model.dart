part of 'model.dart';

class PublishedNoteModel extends Equatable {
  final String id;
  final DateTime createdDate;
  final NoteAssetModel asset;

  const PublishedNoteModel({
    required this.id,
    required this.createdDate,
    required this.asset,
  });

  factory PublishedNoteModel.fromServiceModel(PublishedNote model) =>
      PublishedNoteModel(
        id: model.id,
        createdDate: model.createdAt,
        asset: NoteAssetModel.fromServiceModel(model.asset),
      );

  PublishedNote toServiceModel() => PublishedNote(
        id: id,
        createdAt: createdDate,
        asset: asset.toServiceModel(),
      );

  String get dateTimeString =>
      DateFormat("MMM dd, yyyy • hh:mm a").format(createdDate);

  @override
  List<Object?> get props => [id, createdDate, asset];
}

class NoteAssetModel extends Equatable {
  final String id;
  final String name;
  final String serialNumber;
  final NoteAssetFacilityModel facility;
  final String? typeId;
  final String? image;

  const NoteAssetModel({
    required this.id,
    required this.name,
    required this.serialNumber,
    required this.facility,
    required this.typeId,
    required this.image,
  });

  factory NoteAssetModel.fromServiceModel(NoteAsset model) => NoteAssetModel(
        id: model.id,
        name: model.name ?? "",
        serialNumber: model.serialNumber ?? "",
        typeId: model.typeId,
        facility: NoteAssetFacilityModel.fromServiceModel(model.facility),
        image: model.image,
      );

  factory NoteAssetModel.fromAssetModel(AssetModel model) => NoteAssetModel(
        id: model.id,
        name: model.name,
        typeId: model.typeId,
        serialNumber: model.serialNumber,
        facility: NoteAssetFacilityModel.fromAssetModel(model.facility),
        image: model.image,
      );

  NoteAsset toServiceModel() => NoteAsset(
        id: id,
        name: name,
        serialNumber: serialNumber,
        facility: facility.toServiceModel(),
        image: image,
      );

  AssetModel toAssetModel() => AssetModel(
        id: id,
        name: name,
        typeId: typeId,
        serialNumber: serialNumber,
        facility: facility.toAssetModel(),
        image: image,
      );

  @override
  List<Object?> get props => [
        id,
        typeId,
        name,
        serialNumber,
        facility,
        image,
      ];
}

class NoteAssetFacilityModel extends Equatable {
  final String id;
  final String _name;

  const NoteAssetFacilityModel({
    required this.id,
    required String name,
  }) : _name = name;

  String get name => _name.isEmpty ? "No facility" : _name;

  static const empty = NoteAssetFacilityModel(
    id: "",
    name: "",
  );

  bool get isEmpty => this == empty;

  factory NoteAssetFacilityModel.fromServiceModel(
    NoteAssetFacility model,
  ) =>
      NoteAssetFacilityModel(
        id: model.id,
        name: model.name,
      );

  factory NoteAssetFacilityModel.fromAssetModel(
    AssetFacilityModel model,
  ) =>
      NoteAssetFacilityModel(
        id: model.id,
        name: model.name,
      );

  NoteAssetFacility toServiceModel() => NoteAssetFacility(
        id: id,
        name: name,
      );

  AssetFacilityModel toAssetModel() => AssetFacilityModel(
        id: id,
        name: name,
      );

  @override
  List<Object?> get props => [id, name];
}
