part of 'model.dart';

class AiNoteModel extends Equatable {
  final String id;
  final DateTime createdAt;
  final AiNoteContentModel content;
  final List<PublishedNoteModel> publishedNotes;
  final CreatedByModel createdBy;
  final TranscriptionStatusModel transcriptionStatus;
  final TranscriptionErrorModel? transcriptionError;
  final NoteTakerLanguage summaryLanguage;

  const AiNoteModel({
    required this.id,
    required this.content,
    required this.createdAt,
    required this.publishedNotes,
    required this.createdBy,
    required this.transcriptionStatus,
    required this.transcriptionError,
    required this.summaryLanguage,
  });

  factory AiNoteModel.fromServiceModel(AiNote note) {
    return AiNoteModel(
      id: note.id,
      content: AiNoteContentModel.fromServiceModel(note.content),
      createdAt: note.createdAt,
      publishedNotes: note.publishedNotes
          .map((e) => PublishedNoteModel.fromServiceModel(e))
          .toList(),
      createdBy: CreatedByModel.fromServiceModel(note.createdBy),
      transcriptionStatus:
          TranscriptionStatusModel.fromServiceModel(note.transcriptionStatus),
      transcriptionError: note.transcriptionError != null
          ? TranscriptionErrorModel.fromServiceModel(note.transcriptionError!)
          : null,
      summaryLanguage: NoteTakerLanguage.getlanguageFor(note.languageCode),
    );
  }

  static AiNoteModel empty = AiNoteModel(
    id: "",
    content: const AiNoteContentModel(
      title: "",
      summary: "",
      transcript: "",
      audioUrl: "",
    ),
    createdBy: const CreatedByModel(id: null, name: ""),
    publishedNotes: const [],
    createdAt: DateTime(1000),
    transcriptionStatus: TranscriptionStatusModel.unknown,
    transcriptionError: null,
    summaryLanguage: NoteTakerLanguage.fallback,
  );

  String get dateTimeString =>
      DateFormat("MMM dd, yyyy • hh:mm a").format(createdAt);

  String get dateString => DateFormat("yyyy/MM/dd").format(createdAt);

  bool get cannotRetry =>
      transcriptionStatus.isFailure && transcriptionError?.code == 400;

  bool get canRetry => transcriptionStatus.isFailure && !cannotRetry;

  @override
  List<Object?> get props => [
        id,
        content,
        createdAt,
        publishedNotes,
        createdBy,
        transcriptionStatus,
        transcriptionError,
        summaryLanguage,
      ];
}

enum TranscriptionStatusModel {
  unknown,
  completed,
  failed,
  inProgress;

  bool get isInprogress =>
      this == TranscriptionStatusModel.inProgress ||
      this == TranscriptionStatusModel.unknown;

  bool get isComplete => this == TranscriptionStatusModel.completed;

  bool get isFailure => this == TranscriptionStatusModel.failed;

  factory TranscriptionStatusModel.fromServiceModel(TranscriptionStatus state) {
    switch (state) {
      case TranscriptionStatus.completed:
        return TranscriptionStatusModel.completed;
      case TranscriptionStatus.failed:
        return TranscriptionStatusModel.failed;
      case TranscriptionStatus.inProgress:
        return TranscriptionStatusModel.inProgress;
      default:
        return TranscriptionStatusModel.unknown;
    }
  }
}

class AiNoteContentModel extends Equatable {
  final String title;
  final String summary;
  final String transcript;
  final String audioUrl;

  const AiNoteContentModel({
    required this.title,
    required this.summary,
    required this.transcript,
    required this.audioUrl,
  });

  factory AiNoteContentModel.fromServiceModel(AiNoteContent content) {
    return AiNoteContentModel(
      title: content.title,
      summary: content.summary,
      transcript: content.transcript,
      audioUrl: content.audioUrl,
    );
  }

  EditableAiNoteModel toEditableModelForCreate() {
    return EditableAiNoteModel(
      title: title,
      summary: summary,
      transcript: transcript,
      audioUrl: audioUrl,
    );
  }

  @override
  List<Object?> get props => [
        title,
        summary,
        transcript,
        audioUrl,
      ];
}

class CreatedByModel extends Equatable {
  final String? id;
  final String name;

  const CreatedByModel({this.id, required this.name});

  factory CreatedByModel.fromServiceModel(CreatedBy model) {
    return CreatedByModel(id: model.id, name: model.name);
  }

  @override
  List<Object?> get props => [id];
}

class TranscriptionErrorModel extends Equatable {
  final int code;
  final String? message;

  const TranscriptionErrorModel({required this.code, this.message});

  factory TranscriptionErrorModel.fromServiceModel(TranscriptionError error) {
    return TranscriptionErrorModel(
      message: error.message,
      code: error.code,
    );
  }

  @override
  List<Object?> get props => [message, code];
}
