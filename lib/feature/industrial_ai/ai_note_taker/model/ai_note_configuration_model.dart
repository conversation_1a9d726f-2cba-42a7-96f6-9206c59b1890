part of 'model.dart';

class AiNoteConfigurationModel extends Equatable {
  final int consumedQuota;
  final int totalQuota;

  const AiNoteConfigurationModel({
    required this.consumedQuota,
    required this.totalQuota,
  });

  factory AiNoteConfigurationModel.fromServiceModel(
          AiNoteConfiguration configuration) =>
      AiNoteConfigurationModel(
        consumedQuota: configuration.usedQuota,
        totalQuota: configuration.allowedQuota,
      );

  int get remainingQuota => totalQuota - consumedQuota;

  bool get isQuotaExceeded => consumedQuota >= totalQuota;

  String get formattedConsumedQuota => Duration(seconds: consumedQuota).toMM;

  String get formattedTotalQuota => Duration(seconds: totalQuota).toMM;

  String get formattedRemainingQuota => Duration(seconds: remainingQuota).toMM;

  @override
  List<Object?> get props => [consumedQuota, totalQuota];
}
