part of 'model.dart';

class EditableAiNoteModel extends Equatable {
  final String? title;
  final String? transcript;
  final String? summary;
  final String? audioUrl;

  const EditableAiNoteModel({
    this.title,
    this.summary,
    this.audioUrl,
    this.transcript,
  });

  EditableAiNote toServiceModel() {
    return EditableAiNote(
      title: title,
      summary: summary,
      transcript: transcript,
      audioUrl: audioUrl,
    );
  }

  @override
  List<Object?> get props => [
        title,
        summary,
        audioUrl,
        transcript,
      ];
}
