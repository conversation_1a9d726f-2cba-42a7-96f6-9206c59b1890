part of 'model.dart';

class AiListFilterModel extends Equatable {
  final String? searchQuery;

  const AiListFilterModel({
    this.searchQuery,
  });

  AiListFilterModel copyWith({
    String? searchQuery,
  }) =>
      AiListFilterModel(searchQuery: searchQuery ?? this.searchQuery);

  AiListFilter toServiceModel() => AiListFilter(searchQuery: searchQuery);

  @override
  List<Object?> get props => [searchQuery];
}
