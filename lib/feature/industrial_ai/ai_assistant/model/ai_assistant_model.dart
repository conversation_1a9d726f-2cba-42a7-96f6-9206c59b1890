part of 'model.dart';

enum AiAssistantTypeModel {
  internalAssistant,
  externalAssistant,
  sharedAssistant,
  unknown;

  factory AiAssistantTypeModel.fromServiceModel(
    AiAssistantType type,
  ) {
    switch (type) {
      case AiAssistantType.internalAssistant:
        return AiAssistantTypeModel.internalAssistant;
      case AiAssistantType.externalAssistant:
        return AiAssistantTypeModel.externalAssistant;
      case AiAssistantType.sharedAssistant:
        return AiAssistantTypeModel.sharedAssistant;
      case AiAssistantType.unknown:
        return AiAssistantTypeModel.unknown;
    }
  }
}

class AiAssistantModel extends Equatable {
  final String id;
  final String name;
  final AiAssistantTypeModel type;
  final AiAssistantAssetModel asset;
  final AiAssistantOemModel oem;

  const AiAssistantModel({
    required this.id,
    required this.name,
    required this.type,
    required this.asset,
    required this.oem,
  });

  factory AiAssistantModel.fromServiceModel(AiAssistant model) {
    return AiAssistantModel(
      id: model.id,
      name: model.name,
      oem: model.oem == null
          ? AiAssistantOemModel.empty
          : AiAssistantOemModel.fromServiceModel(model.oem!),
      asset: AiAssistantAssetModel.fromServiceModel(model.asset),
      type: AiAssistantTypeModel.fromServiceModel(model.type),
    );
  }

  bool get isExternal => type == AiAssistantTypeModel.externalAssistant;

  bool get isShared => type == AiAssistantTypeModel.sharedAssistant;

  bool get isDemo => oem.isEmpty;

  String? get brandLogo => isShared ? oem.brandLogo : null;

  @override
  List<Object?> get props => [id, name, isDemo];
}

class AiAssistantAssetModel extends Equatable {
  final String id;
  final String name;
  final bool isTemplate;

  const AiAssistantAssetModel({
    required this.id,
    required this.name,
    this.isTemplate = false,
  });

  factory AiAssistantAssetModel.fromServiceModel(AiAssistantAsset model) {
    if (model.isEmpty) return empty;

    return AiAssistantAssetModel(
      id: model.id,
      name: model.name,
      isTemplate: model.isTemplate,
    );
  }

  static const empty =
      AiAssistantAssetModel(id: '', name: '', isTemplate: false);

  bool get isEmpty => this == empty;

  @override
  List<Object?> get props => [id, name, isTemplate];
}

class AiAssistantOemModel extends Equatable {
  final String id;
  final String? brandLogo;

  const AiAssistantOemModel({
    required this.id,
    this.brandLogo,
  });

  factory AiAssistantOemModel.fromServiceModel(AiAssistantOem model) {
    return AiAssistantOemModel(
      id: model.id,
      brandLogo: model.brandLogo,
    );
  }

  static const empty = AiAssistantOemModel(id: '');

  bool get isEmpty => this == empty;

  @override
  List<Object?> get props => [id];
}
