import 'dart:async';

import 'package:equatable/equatable.dart';
import 'package:makula_flutter/core/core.dart';
import 'package:makula_flutter/core/utils/utils.dart';
import 'package:makula_flutter/feature/feature.dart';
import 'package:makula_flutter/service/ai_assistant/ai_assistant_service.dart';
import 'package:makula_flutter/service/exceptions.dart';

import '../../model/model.dart';
import '../view/view.dart';

part 'ai_assistant_listing_state.dart';

class AiAssistantListingViewModel
    extends ObservableViewModel<AiAssistantListingState> {
  AiAssistantListingViewModel({
    required AiAssistantPageType type,
    required AiAssistantService aiAssistantService,
  })  : _aiAssistantService = aiAssistantService,
        super(AiAssistantListingState.initial(type: type));

  final AiAssistantService _aiAssistantService;

  StreamSubscription<List<AiAssistantModel>>? _subscription;

  @override
  void dispose() {
    _subscription?.cancel();
    super.dispose();
  }

  void initialize() {
    _subscription?.cancel();
    _subscription = _aiAssistantService
        .getAssistants(state.type.toRepositoryModel())
        .map((assistants) {
      return assistants
          .map((assistant) => AiAssistantModel.fromServiceModel(assistant))
          .toList();
    }).listen((assistants) {
      updateState(
        state.copyWith(
          fetchAssistantsState: state.fetchAssistantsState.copyWith(
            apiState: state.fetchAssistantsState.apiState.toLoaded(
              data: assistants,
            ),
          ),
        ),
      );
    })
      ..onError((error, stackTrace) {
        updateState(
          state.copyWith(
            fetchAssistantsState: state.fetchAssistantsState.copyWith(
              apiState: state.fetchAssistantsState.apiState.toFailure(
                error: ExceptionMessage.general,
              ),
            ),
          ),
        );
      });
  }

  Future<void> fetchAssistants({bool forceRefresh = false}) async {
    try {
      bool hasMoreData = await _aiAssistantService.fetchAssistants(
        AiAssistantListFilter(type: state.type.toRepositoryModel()),
        forceRefresh: forceRefresh,
      );

      updateState(
        state.copyWith(
          fetchAssistantsState: state.fetchAssistantsState.copyWith(
            apiState: state.fetchAssistantsState.apiState.toLoaded(),
            hasMoreData: hasMoreData,
          ),
        ),
      );
    } on NoInternetException {
      updateState(
        state.copyWith(
          fetchAssistantsState: state.fetchAssistantsState.copyWith(
            apiState: state.fetchAssistantsState.apiState.toFailure(
              error: ExceptionMessage.noInternet,
            ),
          ),
        ),
      );
    } catch (e) {
      updateState(
        state.copyWith(
          fetchAssistantsState: state.fetchAssistantsState.copyWith(
            apiState: state.fetchAssistantsState.apiState.toFailure(
              error: ExceptionMessage.general,
            ),
          ),
        ),
      );
    }
  }

  Future<void> checkAiAssistantAccess() async {
    await ServiceRequestHandlingStrategy<bool>(
      initialState: state.fetchAiAssistantAccess,
      request: () async {
        return await _aiAssistantService.oemHasAiAssistantsAccess();
      },
      onChange: (responseState) => updateState(
        state.copyWith(fetchAiAssistantAccess: responseState),
      ),
    ).execute();
  }
}
