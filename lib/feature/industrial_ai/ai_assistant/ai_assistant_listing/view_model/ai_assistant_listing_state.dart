part of 'ai_assistant_listing_view_model.dart';

typedef FetchAssistantsState = PaginatedResponseState<AiAssistantModel>;
typedef FetchAiAssistantAccess = GeneralResponseState<bool>;

class AiAssistantListingState extends Equatable {
  final AiAssistantPageType type;
  final FetchAssistantsState fetchAssistantsState;
  final FetchAiAssistantAccess fetchAiAssistantAccess;

  const AiAssistantListingState({
    required this.type,
    required this.fetchAssistantsState,
    required this.fetchAiAssistantAccess,
  });

  factory AiAssistantListingState.initial({required AiAssistantPageType type}) {
    return AiAssistantListingState(
      type: type,
      fetchAssistantsState: FetchAssistantsState.initial(),
      fetchAiAssistantAccess: const FetchAiAssistantAccess(),
    );
  }

  AiAssistantListingState copyWith({
    FetchAssistantsState? fetchAssistantsState,
    FetchAiAssistantAccess? fetchAiAssistantAccess,
  }) {
    return AiAssistantListingState(
      type: type,
      fetchAssistantsState: fetchAssistantsState ?? this.fetchAssistantsState,
      fetchAiAssistantAccess:
          fetchAiAssistantAccess ?? this.fetchAiAssistantAccess,
    );
  }

  @override
  List<Object?> get props => [
        type,
        fetchAssistantsState,
        fetchAiAssistantAccess,
      ];
}
