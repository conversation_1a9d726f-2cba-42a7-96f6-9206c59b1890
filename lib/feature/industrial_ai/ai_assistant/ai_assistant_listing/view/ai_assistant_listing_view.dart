part of 'view.dart';

class AiAssistantListingView extends StatefulWidget {
  const AiAssistantListingView({
    super.key,
    required this.type,
  });

  final AiAssistantPageType type;

  @override
  State<AiAssistantListingView> createState() => _AiAssistantListingViewState();
}

class _AiAssistantListingViewState extends State<AiAssistantListingView>
    with AutomaticKeepAliveClientMixin {
  @override
  Widget build(BuildContext context) {
    super.build(context);
    return ViewModelProvider(
      create: () => AiAssistantListingViewModel(
        aiAssistantService: get<FeatureModules>()!.aiAssistantService,
        type: widget.type,
      )
        ..initialize()
        ..checkAiAssistantAccess()
        ..fetchAssistants(forceRefresh: true),
      child: const _AssistantList(),
    );
  }

  @override
  bool get wantKeepAlive => true;
}

class _AssistantList extends StatelessWidget {
  const _AssistantList();

  @override
  Widget build(BuildContext context) {
    return ViewModelBuilder<AiAssistantListingViewModel,
        AiAssistantListingState>(
      builder: (context, state) {
        if (state.fetchAiAssistantAccess.isLoading ||
            state.fetchAssistantsState.isLoading) {
          return const Center(child: MakulaLoader());
        }

        if (state.fetchAiAssistantAccess.isFailure) {
          return GeneralErrorWidget(
            message: state.fetchAiAssistantAccess.error,
            onRetry: () =>
                InhertedViewModel.of<AiAssistantListingViewModel>(context)
                    .viewModel
                    .checkAiAssistantAccess(),
          );
        }

        if (!state.fetchAiAssistantAccess.data!) {
          return const NoAccessBody();
        }

        if (state.fetchAssistantsState.isFailure) {
          return GeneralErrorWidget(
            message: state.fetchAssistantsState.apiState.error,
            onRetry: () =>
                InhertedViewModel.of<AiAssistantListingViewModel>(context)
                    .viewModel
                    .fetchAssistants(forceRefresh: true),
          );
        }

        if (state.fetchAssistantsState.apiState.isEmpty) {
          return EmptyStateView(
            title: context.localization.noAiAssistantsFound,
            subtitle: context.localization.noAiAssistantsFoundSubtitle,
          );
        }
        final assistants = state.fetchAssistantsState.apiState.data!;
        return PaginatedListWidget(
          itemCount: assistants.length,
          hasMoreItems: state.fetchAssistantsState.hasMoreData,
          onScrollEndReached:
              InhertedViewModel.of<AiAssistantListingViewModel>(context)
                  .viewModel
                  .fetchAssistants,
          bottomWidget: const SizedBox(),
          onPullRefresh: () =>
              InhertedViewModel.of<AiAssistantListingViewModel>(context)
                  .viewModel
                  .fetchAssistants(forceRefresh: true),
          itemBuilder: (_, i) => AiAssistantCard(assistant: assistants[i]),
          padding: EdgeInsets.zero,
          separatorBuilder: (_, __) =>
              SizedBox(height: context.makulaPadding.xs),
        );
      },
    );
  }
}

class AiAssistantCard extends StatelessWidget {
  const AiAssistantCard({super.key, required this.assistant});

  final AiAssistantModel assistant;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        AnalyticsService.instance.sendEvent(
          'Tapped on AI Copilot Button',
          {
            'copilotId': assistant.id,
            'origin': 'ai-assistant-listing',
          },
        );
        context.push(AiChatPage.pageRoute(assistantId: assistant.id));
      },
      highlightColor: context.makulaBackgroundColors.highlight,
      splashColor: context.makulaBackgroundColors.splash,
      child: Ink(
        color: context.makulaBackgroundColors.primary,
        padding: EdgeInsets.all(context.makulaPadding.l),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            MakulaCardIcon.large(
              icon: assistant.isShared
                  ? MakulaIcons.customerPortal
                  : MakulaIcons.assetHub,
              backgroundColor: assistant.isShared
                  ? context.makulaBackgroundColors.danger
                  : context.makulaBackgroundColors.warning,
              backgroundImage: assistant.brandLogo,
              iconColor: context.makulaContentColors.inverse,
              showExternalBadge: assistant.isExternal,
              secondaryIconSize: 24,
              secondaryIconWidget: assistant.brandLogo == null
                  ? null
                  : MakulaCardIcon.small(
                      icon: assistant.isShared
                          ? MakulaIcons.customerPortal
                          : MakulaIcons.assetHub,
                      backgroundColor: assistant.isShared
                          ? context.makulaBackgroundColors.danger
                          : context.makulaBackgroundColors.warning,
                      iconColor: context.makulaContentColors.inverse,
                    ),
            ),
            SizedBox(width: context.makulaPadding.l),
            Expanded(
              child: Row(
                children: [
                  Flexible(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            Flexible(
                              child: Text(
                                assistant.name,
                                style: context.makulaTypography.headline.medium
                                    .primary(context),
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: context.makulaPadding.xxs),
                        Row(
                          children: [
                            Padding(
                              padding: EdgeInsets.only(
                                right: context.makulaPadding.xs,
                              ),
                              child: MakulaIcon(
                                assistant.asset.isTemplate
                                    ? MakulaIcons.machineTemplate
                                    : MakulaIcons.machines,
                                color: context.makulaContentColors.tertiary,
                              ),
                            ),
                            Flexible(
                              child: Text(
                                assistant.isDemo
                                    ? context.localization.demo
                                    : assistant.asset.isEmpty
                                        ? '-'
                                        : assistant.asset.name,
                                style: context.makulaTypography.body.medium
                                    .secondary(context),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class NoAccessBody extends StatelessWidget {
  const NoAccessBody({super.key});

  @override
  Widget build(BuildContext context) {
    return EmptyStateView(
      title: context.localization.aiAssistantAccess,
      subtitleChild: RichText(
        textAlign: TextAlign.center,
        text: TextSpan(
          text: context.localization.oemAiAssistantAccessMessage,
          style: context.makulaTypography.body.medium.secondary(context),
          children: [
            TextSpan(
              text: Constants.supportEmail,
              style: context.makulaTypography.body.medium.copyWith(
                color: context.makulaContentColors.brand,
              ),
              recognizer: TapGestureRecognizer()
                ..onTap = () async {
                  final couldNotOpenEmail =
                      context.localization.couldNotOpenEmail;

                  try {
                    await launchEmail(Constants.supportEmail);
                  } catch (e) {
                    Alert.showError(errorMessage: couldNotOpenEmail);
                  }
                },
            ),
          ],
        ),
      ),
      illustration: MakulaIllustrations.mail,
    );
  }
}
