part of 'view.dart';

class AiAssistantListingPage extends StatelessWidget {
  const AiAssistantListingPage({super.key});

  static String pageName() => 'assistants';

  static String pageRoute() =>
      '/${IndustrialAiDashboardPage.pageName()}/${pageName()}';

  @override
  Widget build(BuildContext context) {
    return const AiAssistantPage();
  }
}

enum AiAssistantPageType {
  myAssistants,
  sharedAssistants;

  String getTitle(AppLocalizations localization) {
    switch (this) {
      case myAssistants:
        return localization.myAssistants;
      case sharedAssistants:
        return localization.sharedAssistants;
    }
  }

  Widget getTabView() => AiAssistantListingView(type: this);

  AiAssistantListType toRepositoryModel() {
    switch (this) {
      case myAssistants:
        return AiAssistantListType.my;
      case sharedAssistants:
        return AiAssistantListType.shared;
    }
  }
}

class AiAssistantPage extends StatelessWidget {
  const AiAssistantPage({super.key});

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 2,
      child: Scaffold(
        appBar: ProductAppBar(
          title: context.localization.assistants,
          product: ProductModel.industralAi,
          actions: [
            TextLinkButton.mediumIcon(
              icon: MakulaIcons.history,
              onTap: () {
                AnalyticsService.instance.sendEvent(
                  "View AI Assistant History Listing",
                  {},
                );
                context.push(AiChatHistoryPage.pageRoute());
              },
            ),
            SizedBox(width: context.makulaPadding.xxs),
          ],
          bottom: const _AiAssistantTabBar(),
        ),
        body: TabBarView(
          children:
              AiAssistantPageType.values.map((e) => e.getTabView()).toList(),
        ),
      ),
    );
  }
}

class _AiAssistantTabBar extends StatelessWidget
    implements PreferredSizeWidget {
  const _AiAssistantTabBar();

  @override
  Widget build(BuildContext context) {
    return Material(
      color: context.makulaBackgroundColors.primary,
      child: TabBar(
        dividerHeight: 1.0,
        dividerColor: context.makulaBorderColors.primary,
        overlayColor: WidgetStateProperty.all(
          context.makulaBackgroundColors.accent.withTransparency25,
        ),
        indicatorSize: TabBarIndicatorSize.tab,
        labelStyle: context.makulaTypography.label.medium.primary(context),
        unselectedLabelStyle:
            context.makulaTypography.label.medium.secondary(context),
        indicatorColor: context.makulaBorderColors.brand,
        labelPadding:
            EdgeInsets.symmetric(horizontal: context.makulaPadding.xl),
        tabs: AiAssistantPageType.values
            .map((e) => Tab(text: e.getTitle(context.localization)))
            .toList(),
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kTextTabBarHeight);
}
