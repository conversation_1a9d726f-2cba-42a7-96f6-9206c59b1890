part of 'widget.dart';

class PromptBubble extends StatelessWidget {
  const PromptBubble({
    super.key,
    required this.message,
    required this.username,
  });

  final String message;
  final String username;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Flexible(
          child: Padding(
            padding: const EdgeInsets.only(top: 2.0),
            child: Maku<PERSON><PERSON>hatBubble(
              isAlignRight: true,
              child: Text(
                message,
                style: context.makulaTypography.body.medium.primary(context),
              ),
            ),
          ),
        ),
        const SizedBox(width: 6),
        InitialAvatar(
          text: username,
          containerSize: 32,
          textSize: 12,
        ),
      ],
    );
  }
}
