part of 'widget.dart';

class ResponseBubble extends StatelessWidget {
  const ResponseBubble({
    super.key,
    required this.response,
    required this.assistantId,
    required this.isSharedAsset,
    this.isLoading = false,
  });

  final String assistantId;
  final bool isSharedAsset;
  final CopilotResponseModel response;
  final bool isLoading;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 32,
          height: 32,
          child: CircularBackground.withIcon(
            isSharedAsset ? MakulaIcons.customerPortal : MakulaIcons.assetHub,
            backgroundColor: isSharedAsset
                ? context.makulaBackgroundColors.danger
                : context.makulaBackgroundColors.warning,
            iconColor: context.makulaContentColors.inverse,
            padding: const EdgeInsets.all(6),
          ),
        ),
        const SizedBox(width: 6),
        Flexible(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.only(top: 2.0),
                child: MakulaChatBubble(
                  isAlignRight: false,
                  backgroundColor: context.makulaBackgroundColors.accent,
                  child: AnimatedSwitcher(
                    duration: const Duration(milliseconds: 300),
                    child: isLoading
                        ? const MakulaAnimation(
                            MakulaAnimations.aiLoading,
                            height: 20,
                            width: 40,
                          )
                        : _Text(response: response),
                  ),
                ),
              ),
              SizedBox(height: context.makulaPadding.m),
              if (response.referencesIndex.isNotEmpty) ...[
                _ReferencesWidget(response: response, assistantId: assistantId),
              ]
            ],
          ),
        ),
      ],
    );
  }
}

class _ReferencesWidget extends StatelessWidget {
  const _ReferencesWidget({required this.assistantId, required this.response});

  final String assistantId;
  final CopilotResponseModel response;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          context.localization.relevantSources,
          style: context.makulaTypography.body.small.secondary(context),
        ),
        SizedBox(height: context.makulaPadding.s),
        Wrap(
          spacing: context.makulaPadding.s,
          runSpacing: context.makulaPadding.s,
          children: [
            for (int i = 0; i < response.references.length; i++)
              if (response.referencesIndex.contains(i + 1))
                GestureDetector(
                  onTap: () {
                    Navigator.of(context).push(
                      DocumentPreviewPage.route(
                        assistantId: assistantId,
                        documentId: response.references[i].id,
                        documentName: response.references[i].title,
                        page: response.references[i].page,
                        referenceText: response.references[i].text,
                      ),
                    );
                  },
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: context.makulaBackgroundColors.brandLightest,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      "${i + 1} - ${response.references[i].title}",
                      style: context.makulaTypography.body.small.copyWith(
                        color: context.makulaContentColors.brand,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                )
          ],
        ),
      ],
    );
  }
}

class _Text extends StatelessWidget {
  const _Text({required this.response});

  final CopilotResponseModel response;

  @override
  Widget build(BuildContext context) {
    return RichText(
      text: TextSpan(
        children: [
          for (var i = 0; i < response.referencesAsStringList.length; i++)
            if (RegExp(r'^\[.*\]$')
                .hasMatch(response.referencesAsStringList[i]))
              TextSpan(
                text: response.referencesAsStringList[i],
                style: context.makulaTypography.label.small.copyWith(
                  color: context.makulaContentColors.brand,
                ),
              )
            else
              TextSpan(
                text: "${response.referencesAsStringList[i]} ",
                style: context.makulaTypography.body.medium.primary(context),
              ),
        ],
      ),
    );
  }
}
