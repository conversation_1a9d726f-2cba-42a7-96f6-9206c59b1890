part of 'ai_chat_view_model.dart';

typedef SendPromptState = GeneralResponseState<void>;
typedef InitializeAiAssistantState = GeneralResponseState<void>;
typedef FetchAiAssistantDetail = GeneralResponseState<AiAssistantModel>;

class AiChatState extends Equatable {
  final String? chatHistoryId;
  final String assistantId;
  final SendPromptState sendPromptState;
  final InitializeAiAssistantState initializeAiAssistantState;
  final FetchAiAssistantDetail fetchAiAssistantDetail;
  final Map<PromptModel, CopilotResponseModel> conversation;
  final bool exceededQuota;

  const AiChatState({
    required this.chatHistoryId,
    required this.assistantId,
    required this.sendPromptState,
    required this.initializeAiAssistantState,
    required this.fetchAiAssistantDetail,
    required this.conversation,
    required this.exceededQuota,
  });

  factory AiChatState.initial({
    required String assistantId,
    required String? chatHistoryId,
  }) {
    return AiChatState(
      assistantId: assistantId,
      chatHistoryId: chatHistoryId,
      sendPromptState: const SendPromptState(),
      initializeAiAssistantState: const InitializeAiAssistantState(),
      fetchAiAssistantDetail: const FetchAiAssistantDetail(),
      conversation: const {},
      exceededQuota: false,
    );
  }

  AiChatState copyWith({
    SendPromptState? sendPromptState,
    InitializeAiAssistantState? initializeAiAssistantState,
    FetchAiAssistantDetail? fetchAiAssistantDetail,
    Map<PromptModel, CopilotResponseModel>? conversation,
    bool? exceededQuota,
  }) {
    return AiChatState(
      assistantId: assistantId,
      chatHistoryId: chatHistoryId,
      sendPromptState: sendPromptState ?? this.sendPromptState,
      fetchAiAssistantDetail:
          fetchAiAssistantDetail ?? this.fetchAiAssistantDetail,
      initializeAiAssistantState:
          initializeAiAssistantState ?? this.initializeAiAssistantState,
      conversation: conversation ?? this.conversation,
      exceededQuota: exceededQuota ?? this.exceededQuota,
    );
  }

  String get supportEmail => Constants.supportEmail;

  bool get hasNoDocumentsUploadedException =>
      initializeAiAssistantState.exception is NoDocumentsUploadedExceptionModel;

  bool get hasUnauthorizedOemException =>
      initializeAiAssistantState.exception is UnauthorizedOemModel;

  bool get hasUnauthorizedUserException =>
      initializeAiAssistantState.exception is UnauthorizedUserModel;

  @override
  List<Object?> get props => [
        fetchAiAssistantDetail,
        sendPromptState,
        initializeAiAssistantState,
        conversation,
        exceededQuota,
      ];
}

class PromptModel extends Equatable {
  final String text;
  final String timestamp;

  PromptModel({
    required this.text,
  }) : timestamp = DateTime.now().toIso8601String();

  @override
  List<Object?> get props => [text, timestamp];
}
