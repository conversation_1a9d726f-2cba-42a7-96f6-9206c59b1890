import 'dart:async';

import 'package:equatable/equatable.dart';
import 'package:makula_flutter/core/core.dart';
import 'package:makula_flutter/core/utils/utils.dart';
import 'package:makula_flutter/feature/feature.dart';
import 'package:makula_flutter/feature/industrial_ai/ai_assistant/model/model.dart';
import 'package:makula_flutter/service/ai_assistant/ai_assistant_service.dart';

import '../model/model.dart';

part 'ai_chat_state.dart';

class AiCopilotViewModelException implements Exception {}

class NoDocumentsUploadedExceptionModel
    implements AiCopilotViewModelException {}

class UnauthorizedOemModel implements AiCopilotViewModelException {}

class UnauthorizedUserModel implements AiCopilotViewModelException {}

class AiChatViewModel extends ObservableViewModel<AiChatState> {
  final AiAssistantService _aiAssistantService;

  StreamSubscription<bool>? _aiAssistantUsageSubscription;

  AiChatViewModel({
    required String assistantId,
    required AiAssistantService aiAssistantService,
    String? chatHistoryId,
  })  : _aiAssistantService = aiAssistantService,
        super(
          AiChatState.initial(
            assistantId: assistantId,
            chatHistoryId: chatHistoryId,
          ),
        );

  Future<void> fetchAiAssistantDetail() async {
    final strategy = ServiceRequestHandlingStrategy<AiAssistantModel>(
      request: () async {
        final reponse = await _aiAssistantService
            .fetchAiAssistantDetails(state.assistantId);
        return AiAssistantModel.fromServiceModel(reponse);
      },
      onLoaded: initializeAiAssistant,
      onChange: (updatedState) {
        updateState(state.copyWith(fetchAiAssistantDetail: updatedState));
      },
    );

    await strategy.execute();
  }

  Future<void> initializeAiAssistant() async {
    await Future.delayed(const Duration(microseconds: 100));
    updateState(
      state.copyWith(
        initializeAiAssistantState:
            state.initializeAiAssistantState.toLoading(),
      ),
    );

    Map<PromptModel, CopilotResponseModel> conversation = {};
    String? chatId;

    try {
      _aiAssistantUsageSubscription =
          _aiAssistantService.aiAssistantUsage.listen(
        (isExceeded) => updateState(state.copyWith(exceededQuota: isExceeded)),
      );

      if (state.chatHistoryId != null) {
        final history = await _aiAssistantService.fetchAiChatMessages(
          state.chatHistoryId!,
        );

        chatId = history.chatId;

        conversation =
            history.messages.fold<Map<PromptModel, CopilotResponseModel>>(
          {},
          (previousValue, element) {
            final prompt = PromptModel(text: element.query);
            final response =
                CopilotResponseModel.fromServiceModel(element.response);
            return Map.of(previousValue)..addAll({prompt: response});
          },
        );
      }

      await _aiAssistantService.intializeAiAssistant(
        assistantId: state.assistantId,
        chatHistoryId: state.chatHistoryId,
        chatId: chatId,
      );

      updateState(
        state.copyWith(
          initializeAiAssistantState:
              state.initializeAiAssistantState.toLoaded(),
          conversation: conversation,
        ),
      );
    } on UnauthorizedOem {
      updateState(
        state.copyWith(
          initializeAiAssistantState:
              state.initializeAiAssistantState.toFailure(
            exception: UnauthorizedOemModel(),
          ),
        ),
      );
    } on UnauthorizedUser {
      updateState(
        state.copyWith(
          initializeAiAssistantState:
              state.initializeAiAssistantState.toFailure(
            exception: UnauthorizedUserModel(),
          ),
        ),
      );
    } on NoDocumentsUploadedException {
      updateState(
        state.copyWith(
          initializeAiAssistantState:
              state.initializeAiAssistantState.toFailure(
            exception: NoDocumentsUploadedExceptionModel(),
          ),
        ),
      );
    } on QuotaExceededException {
      updateState(
        state.copyWith(
          initializeAiAssistantState:
              state.initializeAiAssistantState.toLoaded(),
          exceededQuota: true,
          conversation: conversation,
        ),
      );
    } catch (e) {
      updateState(
        state.copyWith(
          initializeAiAssistantState:
              state.initializeAiAssistantState.toFailure(),
        ),
      );
    }
  }

  Future<void> sendPrompt(PromptModel prompt) async {
    bool isFirstMessage = state.conversation.isEmpty;

    updateState(
      state.copyWith(
        sendPromptState: state.sendPromptState.toLoading(),
        conversation: Map.of(state.conversation)
          ..addAll({prompt: CopilotResponseModel.empty}),
      ),
    );

    try {
      final response = await _aiAssistantService.sendPrompt(
        AiChatPrompt(query: prompt.text, id: state.assistantId),
        isFirstMessage: isFirstMessage,
      );

      final conversation = Map.of(state.conversation)
        ..addAll({prompt: CopilotResponseModel.fromServiceModel(response)});

      updateState(
        state.copyWith(
          sendPromptState: state.sendPromptState.toLoaded(),
          conversation: conversation,
        ),
      );
    } catch (e) {
      updateState(
        state.copyWith(
          sendPromptState: state.sendPromptState.toFailure(),
          conversation: Map.of(state.conversation)
            ..removeWhere((key, value) => key == prompt),
        ),
      );
    }
  }

  @override
  void dispose() {
    _aiAssistantUsageSubscription?.cancel();
    super.dispose();
  }
}
