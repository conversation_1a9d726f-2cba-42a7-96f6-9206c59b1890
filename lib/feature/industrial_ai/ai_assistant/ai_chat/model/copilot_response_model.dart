part of 'model.dart';

class CopilotResponseModel extends Equatable {
  final String message;
  final List<RefereneceModel> references;

  const CopilotResponseModel({
    required this.message,
    this.references = const [],
  });

  factory CopilotResponseModel.fromServiceModel(AiChatResponse model) {
    return CopilotResponseModel(
      message: model.message,
      references: model.references
          .map((e) => RefereneceModel.fromServiceModel(e))
          .toList(),
    );
  }

  static const empty = CopilotResponseModel(message: '');

  List<int> get referencesIndex {
    final regex = RegExp(r'\[(\d+(?:\s*,\s*\d+)*)\]');
    final matches = regex.allMatches(message);
    final numbers = <int>{};
    for (final match in matches) {
      final group = match.group(1);
      if (group != null) {
        numbers.addAll(group.split(',').map(int.parse));
      }
    }
    return numbers.where((number) => number <= references.length).toList();
  }

  List<String> get referencesAsStringList {
    final regex = RegExp(r'\[(\d+(?:\s*,\s*\d+)*)\]');
    final matches = regex.allMatches(message);
    final parts = <String>[];
    int lastMatchEnd = 0;

    for (final match in matches) {
      // Add the text before the current match
      if (match.start > lastMatchEnd) {
        parts.add(message.substring(lastMatchEnd, match.start).trim());
      }

      // Add each number in the current match as a separate bracketed string
      final group = match.group(1);
      if (group != null) {
        final numbers = group
            .split(',')
            .map((e) => int.tryParse(e.trim()) ?? 100000)
            .where((number) => number <= references.length)
            .map((number) => '[$number]')
            .toList();

        parts.addAll(numbers);
      }

      lastMatchEnd = match.end;
    }

    // Add the remaining text after the last match
    if (lastMatchEnd < message.length) {
      parts.add(message.substring(lastMatchEnd));
    }

    return parts;
  }

  @override
  List<Object?> get props => [message, references];
}

class RefereneceModel extends Equatable {
  final String id;
  final String title;
  final String text;
  final int page;

  const RefereneceModel({
    required this.id,
    required this.title,
    required this.text,
    required this.page,
  });

  factory RefereneceModel.fromServiceModel(Referenece model) {
    return RefereneceModel(
      id: model.id,
      title: model.title,
      text: model.text,
      page: model.page,
    );
  }

  @override
  List<Object?> get props => [id];
}
