part of 'view.dart';

class AiChatView extends StatelessWidget {
  const AiChatView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.makulaBackgroundColors.primary,
      body: ViewModelBuilder<AiChatViewModel, AiChatState>(
        buildWhen: (previous, current) =>
            previous.fetchAiAssistantDetail != current.fetchAiAssistantDetail,
        builder: (context, state) {
          if (state.fetchAiAssistantDetail.isLoading) {
            return const Center(child: Maku<PERSON><PERSON>oader());
          }

          if (state.fetchAiAssistantDetail.isLoaded) {
            return Scaffold(
              backgroundColor: context.makulaBackgroundColors.primary,
              appBar: MakulaBasicAppBar(
                title: state.fetchAiAssistantDetail.data!.name,
                backgroundColor: context.makulaBackgroundColors.accent,
              ),
              body: const SafeArea(child: _Body()),
            );
          }

          return GeneralErrorWidget(
            message: state.fetchAiAssistantDetail.error,
            onRetry: () {
              InhertedViewModel.of<AiChatViewModel>(context)
                  .viewModel
                  .fetchAiAssistantDetail();
            },
          );
        },
      ),
    );
  }
}

class _Body extends StatelessWidget {
  const _Body();

  @override
  Widget build(BuildContext context) {
    return ViewModelBuilder<AiChatViewModel, AiChatState>(
      builder: (context, state) {
        if (state.initializeAiAssistantState.isLoaded) {
          return Column(
            children: [
              MakulaBanner(
                text: context.localization.consumedAllQueriesMessage,
                onTap: () {},
                showIcon: true,
                showBanner: state.exceededQuota,
                backgroundColor: context.makulaBackgroundColors.dangerLight,
                foregroundColor: context.makulaContentColors.danger,
              ),
              const Expanded(child: _ChatView()),
              if (!state.exceededQuota) const _SendPromptField(),
            ],
          );
        }

        if (state.initializeAiAssistantState.isFailure) {
          if (state.hasNoDocumentsUploadedException) {
            return const NoDocumentUploaded();
          } else if (state.hasUnauthorizedOemException) {
            return NoAiAccessErrorView(
              description: context.localization.noOemAiAccessError,
              showContactButton: true,
              supportEmail: state.supportEmail,
            );
          } else if (state.hasUnauthorizedUserException) {
            return NoAiAccessErrorView(
              description: context.localization.noUserAiAccessError,
              showContactButton: false,
              supportEmail: state.supportEmail,
            );
          } else {
            return FullScreenErrorWidget(
              onRetry: () {
                InhertedViewModel.of<AiChatViewModel>(context)
                    .viewModel
                    .initializeAiAssistant();
              },
            );
          }
        }

        return const Center(child: MakulaLoader());
      },
    );
  }
}

class _SendPromptField extends StatefulWidget {
  const _SendPromptField({this.disabled = false});

  final bool disabled;

  @override
  State<_SendPromptField> createState() => _SendPromptFieldState();
}

class _SendPromptFieldState extends State<_SendPromptField> {
  final controller = TextEditingController();

  @override
  void dispose() {
    controller.clear();
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ViewModelListener<AiChatViewModel, AiChatState>(
      listenWhen: (previous, current) =>
          previous.sendPromptState != current.sendPromptState,
      listener: (context, state) {
        if (state.sendPromptState.isLoaded) {
          controller.clear();
        } else if (state.sendPromptState.isFailure) {
          closeKeyboard(context);
          Alert.showError(errorMessage: state.sendPromptState.error);
        }
      },
      child: ViewModelBuilder<AiChatViewModel, AiChatState>(
        builder: (context, state) {
          return IgnorePointer(
            ignoring: widget.disabled,
            child: ChatTextArea(
              controller: controller,
              disabled: state.sendPromptState.isLoading,
              onSend: (prompt) {
                AnalyticsService.instance.sendEvent(
                  'Prompt Send AI Copilot Chat',
                  {"prompt": prompt},
                );
                InhertedViewModel.of<AiChatViewModel>(context)
                    .viewModel
                    .sendPrompt(PromptModel(text: prompt));
              },
            ),
          );
        },
      ),
    );
  }
}

class _ChatView extends StatefulWidget {
  const _ChatView();

  @override
  State<_ChatView> createState() => _ChatViewState();
}

class _ChatViewState extends State<_ChatView> {
  final scrollController = ScrollController();

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (scrollController.hasClients) {
        scrollController.animateTo(
          scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 500),
          curve: Curves.easeOut,
        );
      }
    });
  }

  @override
  void dispose() {
    scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final name =
        InhertedViewModel.of<AppViewModel>(context).viewModel.state.user.name;
    return ViewModelListener<AiChatViewModel, AiChatState>(
      listenWhen: (previous, current) =>
          previous.sendPromptState != current.sendPromptState ||
          previous.conversation != current.conversation,
      listener: (context, state) {
        if (state.sendPromptState.isLoaded || state.sendPromptState.isLoading) {
          _scrollToBottom();
        }

        if (state.initializeAiAssistantState.isLoaded &&
            state.conversation.isNotEmpty) {
          _scrollToBottom();
        }
      },
      child: ViewModelBuilder<AiChatViewModel, AiChatState>(
        builder: (context, state) {
          return state.conversation.isEmpty
              ? const EmptyChatView()
              : ListView.separated(
                  physics: const ClampingScrollPhysics(),
                  controller: scrollController,
                  padding: EdgeInsets.symmetric(
                    horizontal: context.makulaPadding.l,
                    vertical: context.makulaPadding.m,
                  ),
                  itemCount: state.conversation.length,
                  itemBuilder: (context, index) {
                    final prompt = state.conversation.keys.toList()[index];
                    final response = state.conversation.values.toList()[index];
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        PromptBubble(message: prompt.text, username: name),
                        SizedBox(height: context.makulaPadding.s),
                        ResponseBubble(
                          assistantId: state.assistantId,
                          isSharedAsset:
                              state.fetchAiAssistantDetail.data!.isShared,
                          response: response,
                          isLoading: state.sendPromptState.isLoading &&
                              prompt == state.conversation.keys.last,
                        ),
                        SizedBox(height: context.makulaPadding.s),
                      ],
                    );
                  },
                  separatorBuilder: (context, index) => SizedBox(
                    height: context.makulaPadding.l,
                  ),
                );
        },
      ),
    );
  }
}

class EmptyChatView extends StatelessWidget {
  const EmptyChatView({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: context.makulaPadding.l),
      child: Column(
        mainAxisSize: MainAxisSize.max,
        children: [
          const Spacer(),
          const MakulaIcon(MakulaIcons.makula, size: 56),
          SizedBox(height: context.makulaPadding.xxl),
          Text(
            context.localization.welcomeAssistant,
            style: context.makulaTypography.headline.large.primary(context),
          ),
          SizedBox(height: context.makulaPadding.m),
          Text(
            context.localization.welcomeAssistantSubtitle,
            style: context.makulaTypography.body.medium.secondary(context),
            textAlign: TextAlign.center,
          ),
          const Spacer(),
        ],
      ),
    );
  }
}

class NoDocumentUploaded extends StatelessWidget {
  const NoDocumentUploaded({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Expanded(
          child: Padding(
            padding:
                EdgeInsets.symmetric(horizontal: context.makulaPadding.xxl),
            child: Column(
              mainAxisSize: MainAxisSize.max,
              children: [
                const Spacer(),
                Image.asset(
                  MakulaIllustrations.noIndexedDocuments,
                  height: 180,
                ),
                Text(
                  context.localization.noIndexedDocuments,
                  style:
                      context.makulaTypography.headline.large.primary(context),
                ),
                SizedBox(height: context.makulaPadding.xs),
                Text(
                  context.localization.noIndexedDocumentsSubtitle,
                  style:
                      context.makulaTypography.body.medium.secondary(context),
                  textAlign: TextAlign.center,
                ),
                const Spacer(),
              ],
            ),
          ),
        ),
        const _SendPromptField(disabled: true),
      ],
    );
  }
}

class ChatTextArea extends StatefulWidget {
  const ChatTextArea({
    super.key,
    required this.onSend,
    required this.controller,
    this.disabled = false,
  });

  final bool disabled;
  final TextEditingController controller;
  final ValueChanged<String> onSend;

  @override
  State<ChatTextArea> createState() => _ChatTextAreaState();
}

class _ChatTextAreaState extends State<ChatTextArea> {
  final scrollController = ScrollController();

  @override
  void initState() {
    widget.controller.addListener(() => setState(() {}));
    super.initState();
  }

  @override
  void dispose() {
    widget.controller.clear();
    scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.fromLTRB(20, 12, 20, 12),
      decoration: const BoxDecoration(
        border: Border(top: BorderSide(color: kBorderColor)),
      ),
      child: Row(
        children: [
          Expanded(
            child: Scrollbar(
              controller: scrollController,
              child: MakulaTextField(
                controller: widget.controller,
                scrollController: scrollController,
                keyboardType: TextInputType.multiline,
                maxLines: 4,
                decoration: InputDecoration(
                  border: InputBorder.none,
                  focusedBorder: InputBorder.none,
                  enabledBorder: InputBorder.none,
                  disabledBorder: InputBorder.none,
                  errorBorder: InputBorder.none,
                  hintStyle: context.makulaTypography.body.small.copyWith(
                    color: context.makulaContentColors.secondary,
                  ),
                  hintText: context.localization.writeYourMessage,
                  fillColor: Colors.white12,
                  contentPadding: const EdgeInsets.fromLTRB(2, 8, 16, 8),
                ),
              ),
            ),
          ),
          const SizedBox(width: 8),
          PrimaryButton.mediumIcon(
            icon: MakulaIcons.send,
            onTap: widget.controller.text.trim().isEmpty || widget.disabled
                ? null
                : () => widget.onSend(widget.controller.text),
          ),
        ],
      ),
    );
  }
}

class NoAiAccessErrorView extends StatelessWidget {
  const NoAiAccessErrorView({
    super.key,
    required this.description,
    required this.supportEmail,
    required this.showContactButton,
  });

  final String description;
  final String supportEmail;
  final bool showContactButton;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(context.makulaPadding.xxl),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              context.localization.noAICopilotAccess,
              style: context.makulaTypography.headline.large,
            ),
            SizedBox(height: context.makulaPadding.m),
            if (showContactButton)
              RichText(
                textAlign: TextAlign.center,
                text: TextSpan(
                  text: description,
                  style:
                      context.makulaTypography.body.medium.secondary(context),
                  children: [
                    TextSpan(
                      text: supportEmail,
                      style: context.makulaTypography.body.medium.copyWith(
                        color: context.makulaContentColors.brand,
                      ),
                      recognizer: TapGestureRecognizer()
                        ..onTap = () async {
                          final couldNotOpenEmail =
                              context.localization.couldNotOpenEmail;
                          try {
                            await launchEmail(supportEmail);
                          } catch (e) {
                            Alert.showError(errorMessage: couldNotOpenEmail);
                          }
                        },
                    ),
                  ],
                ),
              )
            else
              Text(
                description,
                style: context.makulaTypography.body.medium.secondary(context),
                textAlign: TextAlign.center,
              ),
            SizedBox(height: context.makulaPadding.xxl),
          ],
        ),
      ),
    );
  }
}
