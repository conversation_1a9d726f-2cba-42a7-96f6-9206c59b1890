part of 'view.dart';

class AiChatPage extends StatelessWidget {
  const AiChatPage({
    super.key,
    required this.assistantId,
    this.chatHistoryId,
  });

  static String pageName() => ':assistantId';

  static String pageNameWithHistory() => ':chatHistoryId';

  static String pageRoute(
      {required String assistantId, String? chatHistoryId}) {
    if (chatHistoryId == null) {
      return '${AiAssistantListingPage.pageRoute()}/${pageName().replaceFirst(':assistantId', assistantId)}';
    }
    return '${AiAssistantListingPage.pageRoute()}/${pageName().replaceFirst(':assistantId', assistantId)}/${pageNameWithHistory().replaceFirst(':assistantId', assistantId).replaceFirst(':chatHistoryId', chatHistoryId)}';
  }

  static Route<void> route({
    required String assistantId,
    String? chatHistoryId,
  }) {
    AnalyticsService.instance.sendEvent(
      'View AI Copilot Chat',
      {"copilotId": assistantId},
    );
    return MaterialPageRoute<void>(
      builder: (_) =>
          AiChatPage(assistantId: assistantId, chatHistoryId: chatHistoryId),
    );
  }

  final String assistantId;
  final String? chatHistoryId;

  @override
  Widget build(BuildContext context) {
    return ViewModelProvider(
      create: () => AiChatViewModel(
        aiAssistantService: get<FeatureModules>()!.aiAssistantService,
        assistantId: assistantId,
        chatHistoryId: chatHistoryId,
      )..fetchAiAssistantDetail(),
      child: const AiChatView(),
    );
  }
}
