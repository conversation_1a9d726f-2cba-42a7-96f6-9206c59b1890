import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:makula_flutter/core/core.dart';
import 'package:makula_flutter/core/ui/components/alert.dart';
import 'package:makula_flutter/feature/feature_modules.dart';
import 'package:makula_flutter/feature/industrial_ai/ai_assistant/ai_assistant.dart';
import 'package:makula_flutter/service/analytics/analytics_service.dart';
import 'package:makula_theme/makula_theme.dart';

import '../model/model.dart';
import '../view_model/ai_chat_history_view_model.dart';

part 'ai_chat_history_page.dart';
part 'ai_chat_history_view.dart';
