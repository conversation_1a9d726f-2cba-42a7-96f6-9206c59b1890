part of 'view.dart';

class AiChatHistoryPage extends StatelessWidget {
  const AiChatHistoryPage({super.key});

  static String pageName() => 'history';

  static String pageRoute() =>
      '${AiAssistantListingPage.pageRoute()}/${pageName()}';

  @override
  Widget build(BuildContext context) {
    return ViewModelProvider<AiChatHistoryViewModel>(
      create: () => AiChatHistoryViewModel(
        aiAssistantService: get<FeatureModules>()!.aiAssistantService,
      )
        ..initialize()
        ..fetchChatHistory(forceRefresh: true),
      child: const AiChatHistoryView(),
    );
  }
}
