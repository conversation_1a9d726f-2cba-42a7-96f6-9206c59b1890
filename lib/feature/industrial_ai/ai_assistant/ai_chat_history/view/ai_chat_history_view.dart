part of 'view.dart';

class AiChatHistoryView extends StatelessWidget {
  const AiChatHistoryView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      backgroundColor: context.makulaBackgroundColors.accent,
      appBar: MakulaBasicAppBar(title: context.localization.chatHistory),
      body: const _Body(),
    );
  }
}

class _Body extends StatelessWidget {
  const _Body();

  String formattedDate(DateTime date) {
    final formatter = DateFormat('MMMM yyyy');
    return formatter.format(date);
  }

  @override
  Widget build(BuildContext context) {
    return ViewModelBuilder<AiChatHistoryViewModel, AiChatHistoryState>(
      builder: (context, state) {
        if (state.fetchChatHistoryState.isLoading) {
          return const Center(child: MakulaLoader());
        }
        if (state.fetchChatHistoryState.isFailure) {
          return FullScreenErrorWidget(
            onRetry: () => InhertedViewModel.of<AiChatHistoryViewModel>(
              context,
            ).viewModel.fetchChatHistory(forceRefresh: true),
          );
        }

        if (state.fetchChatHistoryState.data.isEmpty &&
            state.fetchChatHistoryState.apiState.isLoaded) {
          return EmptyStateView(
            title: context.localization.noChatHistoryFound,
            subtitle: context.localization.noChatHistoryFoundSubtitle,
          );
        }

        return PaginatedListWidget(
          padding: EdgeInsets.only(
            top: context.makulaPadding.xs,
            bottom: 80 + MediaQuery.viewInsetsOf(context).bottom,
          ),
          hasMoreItems: state.fetchChatHistoryState.hasMoreData,
          onPullRefresh: () async {
            if (state.fetchChatHistoryState.isPaginatedLoading) {
              return;
            }
            return InhertedViewModel.of<AiChatHistoryViewModel>(context)
                .viewModel
                .fetchChatHistory(forceRefresh: true);
          },
          onScrollEndReached: () {
            if (state.fetchChatHistoryState.apiState.isFailure) return;
            if (state.fetchChatHistoryState.apiState.isLoading) return;
            if (!state.fetchChatHistoryState.isPaginatedLoading) {
              InhertedViewModel.of<AiChatHistoryViewModel>(context)
                  .viewModel
                  .fetchChatHistory(forceRefresh: false);
            }
          },
          bottomWidget: state.fetchChatHistoryState.apiState.isFailure
              ? const SizedBox.shrink()
              : const Center(child: MakulaLoader()),
          itemCount: state.chatsByDate.length,
          separatorBuilder: (BuildContext context, int index) => SizedBox(
            height: context.makulaPadding.xs,
          ),
          itemBuilder: (context, index) {
            final date = state.chatDates[index];
            return Material(
              color: context.makulaBackgroundColors.primary,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: double.infinity,
                    color: context.makulaBackgroundColors.primary,
                    padding: EdgeInsets.all(context.makulaPadding.l),
                    child: Text(
                      formattedDate(date),
                      style: context.makulaTypography.label.medium
                          .secondary(context),
                    ),
                  ),
                  for (final chat in state.chatsByDate[date]!) ...[
                    ChatHistoryItem(model: chat),
                    const Divider(),
                  ]
                ],
              ),
            );
          },
        );
      },
    );
  }
}

class ChatHistoryItem extends StatelessWidget {
  const ChatHistoryItem({super.key, required this.model});

  final AiChatHistoryModel model;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        AnalyticsService.instance.sendEvent(
          'View AI Assistant Chat History',
          {
            'chat_id': model.id,
            'assistant_id': model.assistantId,
          },
        );
        context.push(
          AiChatPage.pageRoute(
            assistantId: model.assistantId,
            chatHistoryId: model.id,
          ),
        );
      },
      onLongPress: () {
        MakulaBottomSheet(
          builder: (_) {
            return InhertedViewModel<AiChatHistoryViewModel>(
              viewModel: InhertedViewModel.of<AiChatHistoryViewModel>(context)
                  .viewModel,
              child: ActionsBottomSheet(
                model: model,
                viewModel: InhertedViewModel.of<AiChatHistoryViewModel>(context)
                    .viewModel,
              ),
            );
          },
        ).show(context);
      },
      highlightColor: context.makulaBackgroundColors.highlight,
      splashColor: context.makulaBackgroundColors.splash,
      child: Ink(
        color: context.makulaBackgroundColors.primary,
        padding: EdgeInsets.symmetric(
          vertical: context.makulaPadding.m,
          horizontal: context.makulaPadding.l,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            MakulaIcon(
              MakulaIcons.aiAssistant,
              color: context.makulaContentColors.tertiary,
              size: 24,
            ),
            SizedBox(width: context.makulaPadding.l),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    model.name,
                    style: context.makulaTypography.body.large.primary(context),
                  ),
                  Text(
                    model.assistantName,
                    style: context.makulaTypography.body.medium.secondary(
                      context,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class ActionsBottomSheet extends StatelessWidget {
  const ActionsBottomSheet({
    super.key,
    required this.model,
    required this.viewModel,
  });

  final AiChatHistoryModel model;
  final AiChatHistoryViewModel viewModel;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        BottomSheetActionItem(
          icon: MakulaIcons.edit,
          title: context.localization.rename,
          onTap: () {
            AnalyticsService.instance.sendEvent(
              "Intiate Rename AI Assistant Chat",
              {
                'chat_id': model.id,
                'assistant_id': model.assistantId,
              },
            );
            Navigator.of(context).pop();
            MakulaBottomSheet(
              title: context.localization.editChatName,
              isScrollControlled: true,
              builder: (_) {
                return InhertedViewModel<AiChatHistoryViewModel>(
                  viewModel: viewModel,
                  child: EditNameBottomSheet(chatItem: model),
                );
              },
            ).show(context);
          },
        ),
        const Divider(),
        BottomSheetActionItem(
          icon: MakulaIcons.delete,
          title: context.localization.delete,
          onTap: () {
            AnalyticsService.instance.sendEvent(
              "Intiate Delete AI Assistant Chat",
              {
                'chat_id': model.id,
                'assistant_id': model.assistantId,
              },
            );
            Navigator.of(context).pop();
            MakulaBottomSheet(
              title: context.localization.deleteChat,
              subtitle: context.localization.deleteChatSubtitle,
              builder: (_) {
                return InhertedViewModel<AiChatHistoryViewModel>(
                  viewModel:
                      InhertedViewModel.of<AiChatHistoryViewModel>(context)
                          .viewModel,
                  child: DeleteConfimationSheet(chatItem: model),
                );
              },
            ).show(context);
          },
        ),
        const Divider(),
        SizedBox(height: MediaQuery.paddingOf(context).bottom)
      ],
    );
  }
}

class EditNameBottomSheet extends StatefulWidget {
  const EditNameBottomSheet({super.key, required this.chatItem});

  final AiChatHistoryModel chatItem;

  @override
  State<EditNameBottomSheet> createState() => _EditNameBottomSheetState();
}

class _EditNameBottomSheetState extends State<EditNameBottomSheet> {
  late TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.chatItem.name)
      ..addListener(() {
        setState(() {});
      });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ViewModelListener<AiChatHistoryViewModel, AiChatHistoryState>(
      listener: (context, state) {
        if (state.renameChatHistoryItemState.isFailure) {
          Alert.showGeneralError();
        }
        if (state.renameChatHistoryItemState.isLoaded) {
          Alert.showSuccess(
            message: context.localization.chatRenamedSuccessMessage,
          );
          Navigator.of(context).pop();
        }
      },
      child: ViewModelBuilder<AiChatHistoryViewModel, AiChatHistoryState>(
        builder: (context, state) {
          bool isLoading = state.renameChatHistoryItemState.isLoading;
          return Padding(
            padding: EdgeInsets.all(context.makulaPadding.l),
            child: Column(
              children: [
                MakulaTextField(
                  controller: _controller,
                  textCapitalization: TextCapitalization.sentences,
                  inputFormatters: [LengthLimitingTextInputFormatter(75)],
                ),
                SizedBox(height: context.makulaPadding.l),
                Row(
                  children: [
                    Expanded(
                      child: SecondaryButton.medium(
                        title: context.localization.cancel,
                        onTap: isLoading ? null : Navigator.of(context).pop,
                      ),
                    ),
                    SizedBox(width: context.makulaPadding.l),
                    Expanded(
                      child: PrimaryButton.medium(
                        title: context.localization.edit,
                        isLoading: isLoading,
                        onTap: _controller.text.isEmpty
                            ? null
                            : () {
                                InhertedViewModel.of<AiChatHistoryViewModel>(
                                        context)
                                    .viewModel
                                    .renameChat(
                                        widget.chatItem, _controller.text);
                              },
                      ),
                    ),
                  ],
                ),
                SizedBox(height: MediaQuery.paddingOf(context).bottom),
                SizedBox(height: MediaQuery.viewInsetsOf(context).bottom)
              ],
            ),
          );
        },
      ),
    );
  }
}

class DeleteConfimationSheet extends StatelessWidget {
  const DeleteConfimationSheet({super.key, required this.chatItem});

  final AiChatHistoryModel chatItem;

  @override
  Widget build(BuildContext context) {
    return ViewModelListener<AiChatHistoryViewModel, AiChatHistoryState>(
      listener: (context, state) {
        if (state.deleteChatHistoryItemState.isFailure) {
          Alert.showGeneralError();
        }
        if (state.deleteChatHistoryItemState.isLoaded) {
          Alert.showSuccess(message: context.localization.chatDeleteSuccess);
          Navigator.of(context).pop();
        }
      },
      child: ViewModelBuilder<AiChatHistoryViewModel, AiChatHistoryState>(
        builder: (context, state) {
          bool isLoading = state.deleteChatHistoryItemState.isLoading;
          return Padding(
            padding: EdgeInsets.all(context.makulaPadding.l),
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: SecondaryButton.medium(
                        title: context.localization.cancel,
                        onTap: isLoading ? null : Navigator.of(context).pop,
                      ),
                    ),
                    SizedBox(width: context.makulaPadding.l),
                    Expanded(
                      child: DangerButton.medium(
                        title: context.localization.delete,
                        isLoading: isLoading,
                        onTap: () {
                          InhertedViewModel.of<AiChatHistoryViewModel>(context)
                              .viewModel
                              .delete(chatItem);
                        },
                      ),
                    ),
                  ],
                ),
                SizedBox(height: MediaQuery.paddingOf(context).bottom)
              ],
            ),
          );
        },
      ),
    );
  }
}
