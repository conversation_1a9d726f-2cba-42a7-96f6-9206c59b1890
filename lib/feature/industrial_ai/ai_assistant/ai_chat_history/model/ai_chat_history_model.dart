part of 'model.dart';

class AiChatHistoryModel extends Equatable {
  final String id;
  final String name;
  final String assistantId;
  final String assistantName;
  final DateTime dateTime;

  const AiChatHistoryModel({
    required this.id,
    required this.name,
    required this.assistantId,
    required this.assistantName,
    required this.dateTime,
  });

  factory AiChatHistoryModel.fromServiceModel(AiChatHistory model) {
    return AiChatHistoryModel(
      id: model.id,
      name: model.title,
      assistantId: model.assistantId,
      assistantName: model.assistantName,
      dateTime: model.dateTime!,
    );
  }

  AiChatHistory toServiceModel() {
    return AiChatHistory(
      id: id,
      title: name,
      assistantId: assistantId,
      assistantName: assistantName,
      dateTime: dateTime,
    );
  }

  @override
  List<Object?> get props => [id, name, assistantId];
}
