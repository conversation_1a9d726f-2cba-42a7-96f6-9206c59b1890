part of 'ai_chat_history_view_model.dart';

typedef FetchChatHistoryState = PaginatedResponseState<AiChatHistoryModel>;
typedef DeleteChatHistoryItemState = GeneralResponseState<void>;
typedef RenameChatHistoryItemState = GeneralResponseState<void>;

class AiChatHistoryState extends Equatable {
  final FetchChatHistoryState fetchChatHistoryState;
  final DeleteChatHistoryItemState deleteChatHistoryItemState;
  final RenameChatHistoryItemState renameChatHistoryItemState;

  const AiChatHistoryState({
    required this.fetchChatHistoryState,
    required this.deleteChatHistoryItemState,
    required this.renameChatHistoryItemState,
  });

  factory AiChatHistoryState.initial() => AiChatHistoryState(
        fetchChatHistoryState: FetchChatHistoryState.initial(),
        deleteChatHistoryItemState: const DeleteChatHistoryItemState(),
        renameChatHistoryItemState: const RenameChatHistoryItemState(),
      );

  AiChatHistoryState copyWith({
    FetchChatHistoryState? fetchChatHistoryState,
    DeleteChatHistoryItemState? deleteChatHistoryItemState,
    RenameChatHistoryItemState? renameChatHistoryItemState,
  }) {
    return AiChatHistoryState(
      fetchChatHistoryState:
          fetchChatHistoryState ?? this.fetchChatHistoryState,
      deleteChatHistoryItemState:
          deleteChatHistoryItemState ?? this.deleteChatHistoryItemState,
      renameChatHistoryItemState:
          renameChatHistoryItemState ?? this.renameChatHistoryItemState,
    );
  }

  Map<DateTime, List<AiChatHistoryModel>> get chatsByDate {
    final chats = fetchChatHistoryState.data;
    final chatsByDate = <DateTime, List<AiChatHistoryModel>>{};

    for (final chat in chats) {
      final date = DateTime(chat.dateTime.year, chat.dateTime.month);
      if (chatsByDate.containsKey(date)) {
        chatsByDate[date]!.add(chat);
      } else {
        chatsByDate[date] = [chat];
      }
    }

    return chatsByDate;
  }

  List<DateTime> get chatDates => chatsByDate.keys.toList();

  @override
  List<Object?> get props => [
        fetchChatHistoryState,
        deleteChatHistoryItemState,
        renameChatHistoryItemState,
      ];
}
