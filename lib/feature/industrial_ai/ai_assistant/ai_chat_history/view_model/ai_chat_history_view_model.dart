import 'dart:async';

import 'package:equatable/equatable.dart';
import 'package:makula_flutter/core/core.dart';
import 'package:makula_flutter/core/utils/utils.dart';
import 'package:makula_flutter/feature/feature.dart';
import 'package:makula_flutter/service/ai_assistant/ai_assistant_service.dart';
import 'package:makula_flutter/service/analytics/analytics_service.dart';
import 'package:makula_flutter/service/service.dart';

import '../model/model.dart';

part 'ai_chat_history_state.dart';

class AiChatHistoryViewModel extends ObservableViewModel<AiChatHistoryState> {
  AiChatHistoryViewModel({
    required AiAssistantService aiAssistantService,
  })  : _aiAssistantService = aiAssistantService,
        super(AiChatHistoryState.initial());

  final AiAssistantService _aiAssistantService;

  StreamSubscription<List<AiChatHistoryModel>>? _subscription;

  @override
  void dispose() {
    _subscription?.cancel();
    super.dispose();
  }

  void initialize() {
    _subscription?.cancel();
    _subscription = _aiAssistantService.getChatHistory().map((chats) {
      return chats
          .where((chat) => chat.dateTime != null)
          .map((chat) => AiChatHistoryModel.fromServiceModel(chat))
          .toList();
    }).listen((chats) {
      updateState(
        state.copyWith(
          fetchChatHistoryState: state.fetchChatHistoryState.copyWith(
            apiState: state.fetchChatHistoryState.apiState.toLoaded(
              data: chats,
            ),
          ),
        ),
      );
    })
      ..onError((error, stackTrace) {
        updateState(
          state.copyWith(
            fetchChatHistoryState: state.fetchChatHistoryState.copyWith(
              apiState: state.fetchChatHistoryState.apiState.toFailure(
                error: ExceptionMessage.general,
              ),
            ),
          ),
        );
      });
  }

  Future<void> fetchChatHistory({required bool forceRefresh}) async {
    updateState(
      state.copyWith(
        fetchChatHistoryState: state.fetchChatHistoryState.copyWith(
          apiState: state.fetchChatHistoryState.apiState.toLoading(),
        ),
      ),
    );

    try {
      final hasMoreData = await _aiAssistantService.fetchChatHistory(
        forceRefresh: forceRefresh,
      );

      updateState(
        state.copyWith(
          fetchChatHistoryState: state.fetchChatHistoryState.copyWith(
            apiState: state.fetchChatHistoryState.apiState.toLoaded(),
            hasMoreData: hasMoreData,
          ),
        ),
      );
    } on NoInternetException {
      updateState(
        state.copyWith(
          fetchChatHistoryState: state.fetchChatHistoryState.copyWith(
            apiState: state.fetchChatHistoryState.apiState.toFailure(
              error: ExceptionMessage.noInternet,
            ),
          ),
        ),
      );
    } catch (e) {
      updateState(
        state.copyWith(
          fetchChatHistoryState: state.fetchChatHistoryState.copyWith(
            apiState: state.fetchChatHistoryState.apiState.toFailure(
              error: ExceptionMessage.general,
            ),
          ),
        ),
      );
    }
  }

  Future<void> delete(AiChatHistoryModel chatItem) async {
    final strategy = ServiceRequestHandlingStrategy(
      request: () async {
        await _aiAssistantService.deleteChatHistory(chatItem.toServiceModel());
        AnalyticsService.instance.sendEvent(
          "AI Assistant Chat Deleted",
          {
            'chat_id': chatItem.id,
            'assistant_id': chatItem.assistantId,
          },
        );
        await Future.delayed(const Duration(milliseconds: 100));
      },
      onChange: (updatedState) {
        updateState(
          state.copyWith(deleteChatHistoryItemState: updatedState),
        );
      },
    );

    await strategy.execute();

    updateState(
      state.copyWith(
        deleteChatHistoryItemState: const DeleteChatHistoryItemState(),
      ),
    );
  }

  Future<void> renameChat(AiChatHistoryModel chatItem, String text) async {
    final strategy = ServiceRequestHandlingStrategy(
      request: () async {
        await _aiAssistantService.renameChatHistory(
          chatItem.toServiceModel(),
          text,
        );
        AnalyticsService.instance.sendEvent(
          "AI Assistant Chat Renamed",
          {
            'chat_id': chatItem.id,
            'assistant_id': chatItem.assistantId,
            'old_name': chatItem.name,
            'new_name': text,
          },
        );
        await Future.delayed(const Duration(milliseconds: 100));
      },
      onChange: (updatedState) {
        updateState(
          state.copyWith(renameChatHistoryItemState: updatedState),
        );
      },
    );

    await strategy.execute();

    updateState(
      state.copyWith(
        renameChatHistoryItemState: const RenameChatHistoryItemState(),
      ),
    );
  }
}
