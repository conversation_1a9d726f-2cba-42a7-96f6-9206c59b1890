import 'package:drift/drift.dart';
import 'package:makula_flutter/service/custom_fields/repository/repository.dart';

import '../drift/database.dart';

class CustomFieldsTable extends Table {
  TextColumn get type => text()();
  TextColumn get value =>
      text().map(const CustomFieldEntityConvertor()).nullable()();

  @override
  Set<Column> get primaryKey => {type};
}

extension CustomFieldsDao on AppDatabase {
  Future<void> saveCustomFields(
    List<CustomFieldEntity> dbModel,
    String type,
  ) async {
    await into(customFieldsTable).insertOnConflictUpdate(
      CustomFieldsTableCompanion(
        type: Value(type),
        value: Value(dbModel),
      ),
    );
  }

  Future<List<CustomFieldEntity>> getCustomFields(String type) async {
    var res = await (select(customFieldsTable)
          ..where((e) => e.type.equals(type)))
        .getSingleOrNull();

    return res?.value ?? [];
  }
}
