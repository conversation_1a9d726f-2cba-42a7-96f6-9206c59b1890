import 'package:drift/drift.dart';
import 'package:makula_flutter/client/persistence/drift/database.dart';
import 'package:makula_flutter/service/ai_assistant/repository/ai_assistant_repository.dart';

class AiAssistantTable extends Table {
  TextColumn get id => text()();
  TextColumn get type => text()();
  TextColumn get assistant =>
      text().map(AiAssistantEntityConverter()).nullable()();

  @override
  Set<Column> get primaryKey => {id};
}

extension AiAssistantTableDao on AppDatabase {
  Stream<List<AiAssistantEntity>> getAiAssistants(
      AiAssistantListTypeEntity type) {
    final query = select(aiAssistantTable)
      ..where((t) => t.type.equals(type.name));
    return query.watch().map(
          (rows) => rows.map((row) => row.assistant!).toList(),
        );
  }

  Future<void> addAiAssistants(
    List<AiAssistantEntity> assistants,
    AiAssistantListTypeEntity type, {
    bool prune = false,
  }) async {
    return transaction(() async {
      if (prune) {
        await deleteAllAssistance(type);
      }

      for (var assistant in assistants) {
        await into(aiAssistantTable).insertOnConflictUpdate(
          AiAssistantTableCompanion(
            id: Value(assistant.id!),
            type: Value(type.name),
            assistant: Value(assistant),
          ),
        );
      }
    });
  }

  Future<void> addAiAssistant(
      AiAssistantEntity assistant, AiAssistantListTypeEntity type) async {
    await into(aiAssistantTable).insertOnConflictUpdate(
      AiAssistantTableCompanion(
        id: Value(assistant.id!),
        type: Value(type.name),
        assistant: Value(assistant),
      ),
    );
  }

  Future<void> deleteAllAssistance(AiAssistantListTypeEntity type) async {
    await (delete(aiAssistantTable)..where((t) => t.type.equals(type.name)))
        .go();
  }
}
