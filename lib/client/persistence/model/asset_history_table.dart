import 'package:drift/drift.dart';
import 'package:makula_flutter/client/persistence/drift/database.dart';
import 'package:makula_flutter/client/persistence/model/asset_table.dart';
import 'package:makula_flutter/service/assets/repository/respository.dart';

class AssetHistoryTable extends Table {
  TextColumn get id => text()();
  TextColumn get assetId => text()();
  DateTimeColumn get createdAt => dateTime()();
  TextColumn get assetHistory =>
      text().map(AssetHistoryItemEntityConverter()).nullable()();

  @override
  Set<Column> get primaryKey => {id, assetId};
}

extension AssetHistoryTableDao on AppDatabase {
  Future<List<AssetHistoryItemEntity>> fetchAssetHistoriesForAsset(
    String assetId,
  ) async {
    final query = select(assetHistoryTable)
      ..where((t) => t.assetId.equals(assetId))
      ..orderBy([
        (t) => OrderingTerm(expression: t.createdAt, mode: OrderingMode.desc),
      ]);
    final rows = await query.get();
    return rows
        .where((row) => row.assetHistory != null)
        .map((row) => row.assetHistory!)
        .toList();
  }

  Future<void> addAllAssetHistories(
    List<AssetHistoryItemEntity> assetHistories,
    String assetId, {
    bool prune = false,
    bool isCompletelyFetched = false,
  }) async {
    return transaction(() async {
      if (prune) {
        await deleteAllHistoryForAsset(assetId);
      }

      for (var assetHistory in assetHistories) {
        await into(assetHistoryTable).insertOnConflictUpdate(
          AssetHistoryTableCompanion(
            id: Value(assetHistory.id!),
            assetId: Value(assetId),
            createdAt: Value(DateTime.parse(assetHistory.createdAt!)),
            assetHistory: Value(assetHistory),
          ),
        );
      }

      if (isCompletelyFetched) {
        await updateAssetLastSync(
          assetId,
          AssetLastSyncEntity(historyLastSync: DateTime.now()),
        );
      }
    });
  }

  Future<void> deleteAllHistoryForAsset(String assetId) async {
    await (delete(assetHistoryTable)..where((t) => t.assetId.equals(assetId)))
        .go();
  }
}
