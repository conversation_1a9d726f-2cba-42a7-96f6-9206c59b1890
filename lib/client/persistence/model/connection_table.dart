import 'package:drift/drift.dart';
import 'package:makula_flutter/client/persistence/drift/database.dart';
import 'package:makula_flutter/service/connection/repository/repository.dart';

class ConnectionTable extends Table {
  TextColumn get id => text()();
  DateTimeColumn get lastAssetSync => dateTime().nullable()();
  TextColumn get connection =>
      text().map(ConnectionEntityConverter()).nullable()();

  @override
  Set<Column> get primaryKey => {id};
}

extension ConnectionTableDao on AppDatabase {
  Future<void> addOrUpdateConnection(
    ConnectionEntity connection,
  ) async {
    await into(connectionTable).insertOnConflictUpdate(
      ConnectionTableCompanion(
        id: Value(connection.id!),
        connection: Value(connection),
      ),
    );
  }

  Future<void> updateConnectionLastAssetSync(
    String id,
    DateTime lastAssetSync,
  ) async {
    final oldData = await getConnectionTableDataById(id);
    await into(connectionTable).insertOnConflictUpdate(
      ConnectionTableCompanion(
        id: Value(id),
        lastAssetSync: Value(lastAssetSync),
        connection: Value(oldData?.connection),
      ),
    );
  }

  Future<ConnectionEntity?> getConnectionById(String id) async {
    final query = select(connectionTable)..where((t) => t.id.equals(id));
    final result = await query.getSingleOrNull();
    return result?.connection;
  }

  Future<ConnectionTableData?> getConnectionTableDataById(String id) async {
    final query = select(connectionTable)..where((t) => t.id.equals(id));
    final result = await query.getSingleOrNull();
    return result;
  }

  Future<DateTime?> getLastAssetSync(String id) async {
    final query = select(connectionTable)
      ..where((t) => t.id.equals(id))
      ..limit(1);
    final result = await query.getSingleOrNull();
    return result?.lastAssetSync;
  }

  Future<List<ConnectionEntity>> getAllConnections() async {
    final query = select(connectionTable);
    final results = await query.get();
    return results
        .where((row) => row.connection != null)
        .map((row) => row.connection!)
        .toList();
  }

  Future<List<ConnectionEntity>> getConnectionsWithLastSync(
      {String? searchQuery}) async {
    final query = select(connectionTable)
      ..where((t) => t.lastAssetSync.isNotNull());
    final results = await query.get();
    return results
        .where((row) =>
            row.connection != null &&
            row.connection!.meetsSearchQuery(searchQuery))
        .map((row) => row.connection!)
        .toList();
  }

  Future<void> updateAllConnectionsLastAssetSync(
    DateTime lastAssetSync,
  ) async {
    final query = update(connectionTable)
      ..where((t) => t.lastAssetSync.isNotNull());
    await query.write(
      ConnectionTableCompanion(lastAssetSync: Value(lastAssetSync)),
    );
  }

  Future<List<ConnectionTableData>> getAllOfflineConnections() async {
    final query = select(connectionTable)
      ..orderBy([
        (t) =>
            OrderingTerm(expression: t.lastAssetSync, mode: OrderingMode.asc),
      ]);
    return await query.get();
  }
}
