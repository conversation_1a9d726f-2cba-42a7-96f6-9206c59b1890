import 'package:drift/drift.dart';
import 'package:makula_flutter/client/persistence/model/asset_table.dart';
import 'package:makula_flutter/service/assets/repository/model/model.dart';
import 'package:makula_flutter/service/custom_fields/repository/repository.dart';

import '../drift/database.dart';

class CustomFieldValuesTable extends Table {
  TextColumn get resourceId => text()();
  TextColumn get type => text()();
  BoolColumn get isShared => boolean()();
  TextColumn get value =>
      text().map(const CustomFieldValueEntityConvertor()).nullable()();

  @override
  Set<Column> get primaryKey => {resourceId, type, isShared};
}

extension CustomFieldValuesDao on AppDatabase {
  Future<void> saveCustomFieldValues({
    required String resourceId,
    required List<CustomFieldValueEntity> dbModel,
    required String type,
    required bool isShared,
  }) async {
    await into(customFieldValuesTable).insertOnConflictUpdate(
      CustomFieldValuesTableCompanion(
        resourceId: Value(resourceId),
        type: Value(type),
        isShared: Value(isShared),
        value: Value(dbModel),
      ),
    );

    if (type == CustomFieldTypeEntity.machines.name) {
      await updateAssetLastSync(
        resourceId,
        AssetLastSyncEntity(customFieldsLastSync: DateTime.now()),
      );
    }
  }

  Future<List<CustomFieldValueEntity>> getCustomFieldValues({
    required String resourceId,
    required String type,
    required bool isShared,
  }) async {
    final res = await (select(customFieldValuesTable)
          ..where((e) =>
              e.resourceId.equals(resourceId) &
              e.type.equals(type) &
              e.isShared.equals(isShared)))
        .getSingleOrNull();

    return res?.value ?? [];
  }
}
