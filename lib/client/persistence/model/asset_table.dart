import 'package:drift/drift.dart';
import 'package:makula_flutter/client/persistence/drift/database.dart';
import 'package:makula_flutter/client/persistence/model/connection_table.dart';
import 'package:makula_flutter/service/assets/repository/respository.dart';

class AssetTable extends Table {
  TextColumn get id => text()();
  TextColumn get connectionId => text().nullable()();
  TextColumn get parentAssetId => text().nullable()();
  BoolColumn get isSharedAsset =>
      boolean().withDefault(const Constant(false))();
  TextColumn get lastSync =>
      text().map(AssetLastSyncEntityConverter()).nullable()();
  TextColumn get asset =>
      text().map(AssetDetailsEntityConverter()).nullable()();

  @override
  Set<Column> get primaryKey => {id};
}

extension AssetTableDao on AppDatabase {
  Future<void> addOrUpdateAssetDetails(
    AssetDetailsEntity asset,
    bool isSharedAsset,
  ) async {
    final lastSyncValue = await computeLastSync(
      asset.id!,
      AssetLastSyncEntity(detailLastSync: DateTime.now()),
    );

    await into(assetTable).insertOnConflictUpdate(
      AssetTableCompanion(
        id: Value(asset.id!),
        asset: Value(asset),
        connectionId: Value(asset.customer?.id),
        parentAssetId: Value(asset.parentAssetId),
        lastSync: Value(lastSyncValue),
        isSharedAsset: Value(isSharedAsset),
      ),
    );
  }

  Future<void> addOrUpdateListAsset(
    AssetEntity asset,
    bool isSharedAsset,
  ) async {
    final lastAssetValue = await fetchAssetData(asset.id!);
    await into(assetTable).insertOnConflictUpdate(
      AssetTableCompanion(
        id: Value(asset.id!),
        asset: lastAssetValue?.asset == null
            ? Value(asset.toAssetDetailsEntity())
            : Value(
                lastAssetValue!.asset!.copyWithAssetEntity(asset),
              ),
        connectionId: Value(asset.facility?.id),
        parentAssetId: Value(asset.parentAssetId),
        isSharedAsset: Value(isSharedAsset),
        lastSync: Value(lastAssetValue?.lastSync),
      ),
    );
  }

  Future<AssetLastSyncEntity> computeLastSync(
    String id,
    AssetLastSyncEntity updatedSync,
  ) async {
    final query = select(assetTable)..where((t) => t.id.equals(id));
    final result = await query.getSingleOrNull();

    return updatedSync.fillNullsWith(result?.lastSync);
  }

  Future<void> updateAssetLastSync(
    String id,
    AssetLastSyncEntity lastSync,
  ) async {
    final lastSyncValue = await computeLastSync(
      id,
      lastSync,
    );
    final oldData = await fetchAssetData(id);

    await into(assetTable).insertOnConflictUpdate(
      AssetTableCompanion(
        id: Value(id),
        asset: Value(oldData?.asset),
        connectionId: Value(oldData?.connectionId),
        parentAssetId: Value(oldData?.parentAssetId),
        isSharedAsset: Value(oldData?.isSharedAsset ?? false),
        lastSync: Value(lastSyncValue),
      ),
    );
  }

  Future<AssetLastSyncEntity?> getAssetLastSync(String id) async {
    final query = select(assetTable)..where((t) => t.id.equals(id));
    final result = await query.getSingleOrNull();

    if (result?.lastSync?.oldestDate == null) {
      return null;
    }

    return result?.lastSync;
  }

  Future<void> deleteAsset(String id) async {
    await (delete(assetTable)..where((t) => t.id.equals(id))).go();
  }

  Future<AssetDetailsEntity?> getAssetById(String id) async {
    final query = select(assetTable)..where((t) => t.id.equals(id));
    final result = await query.getSingleOrNull();

    if (result?.lastSync == null || result?.lastSync?.hasBeenSynced == false) {
      return null;
    }

    return result?.asset;
  }

  Future<void> addAllSubAssetsForAsset(
    String parentAssetId,
    List<AssetEntity> assets, {
    bool prune = false,
    bool isCompletelyFetched = false,
    bool isSharedAsset = false,
  }) async {
    return transaction(() async {
      if (prune) {
        final allAssets = await getAssetsByParentId(parentAssetId);
        final idsToDelete = allAssets
            .where((existingAsset) =>
                !assets.any((newAsset) => newAsset.id == existingAsset.id))
            .map((asset) => asset.id!)
            .toList();
        await deleteAssetsWithId(idsToDelete);
      }
      for (var asset in assets) {
        await addOrUpdateListAsset(asset, isSharedAsset);
      }

      if (isCompletelyFetched) {
        await updateAssetLastSync(
          parentAssetId,
          AssetLastSyncEntity(hierarchyLastSync: DateTime.now().toUtc()),
        );
      }
    });
  }

  Future<void> addAllAssetsForConnection(
    String connectionId,
    List<AssetEntity> assets, {
    bool prune = false,
    bool isCompletelyFetched = false,
    bool isSharedAsset = false,
  }) async {
    return transaction(() async {
      if (prune) {
        await (delete(assetTable)
              ..where((t) => t.connectionId.equals(connectionId)))
            .go();
      }
      for (var asset in assets) {
        await addOrUpdateListAsset(asset, isSharedAsset);
      }

      if (isCompletelyFetched) {
        await updateConnectionLastAssetSync(
          connectionId,
          DateTime.now(),
        );
      }
    });
  }

  Future<void> deleteAssetHierarchy(
    String parentAssetId,
  ) async {
    await (delete(assetTable)
          ..where((t) => t.parentAssetId.equals(parentAssetId)))
        .go();
  }

  Future<List<AssetDetailsEntity>> getAssetsByConnectionId(
    String connectionId, {
    String? searchQuery,
  }) async {
    final query = select(assetTable)
      ..where((t) => t.connectionId.equals(connectionId));
    final results = await query.get();
    return results
        .where((e) => e.asset != null && e.asset!.meetsSearchQuery(searchQuery))
        .map((e) => e.asset!)
        .toList();
  }

  Future<List<AssetDetailsEntity>> getAssetsByParentId(
    String parentAssetId,
  ) async {
    final query = select(assetTable)
      ..where((t) => t.parentAssetId.equals(parentAssetId));
    final results = await query.get();
    return results.where((e) => e.asset != null).map((e) => e.asset!).toList();
  }

  Future<AssetTableData?> fetchAssetData(String id) async {
    final query = select(assetTable)..where((t) => t.id.equals(id));
    final result = await query.getSingleOrNull();

    return result;
  }

  Future<List<AssetDetailsEntity>> getAllOfflineAssetsWithDetails({
    String? searchQuery,
    bool isSharedAsset = false,
  }) async {
    final query = select(assetTable);
    final results = await query.get();

    return results
        .where(
          (e) =>
              e.asset != null &&
              e.lastSync != null &&
              e.isSharedAsset == isSharedAsset &&
              e.asset!.meetsSearchQuery(searchQuery),
        )
        .map((e) => e.asset!)
        .toList();
  }

  Future<void> deleteAssetsWithId(List<String> ids) async {
    await (delete(assetTable)..where((t) => t.id.isIn(ids))).go();
  }
}
