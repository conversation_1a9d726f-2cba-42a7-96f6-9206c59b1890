// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'database.dart';

// ignore_for_file: type=lint
class $AuthEntityTable extends AuthEntity
    with TableInfo<$AuthEntityTable, AuthEntityData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $AuthEntityTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
      'id', aliasedName, false,
      hasAutoIncrement: true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('PRIMARY KEY AUTOINCREMENT'));
  static const VerificationMeta _loginResponseMeta =
      const VerificationMeta('loginResponse');
  @override
  late final GeneratedColumnWithTypeConverter<LoginResponse?, String>
      loginResponse = GeneratedColumn<String>(
              'login_response', aliasedName, true,
              type: DriftSqlType.string, requiredDuringInsert: false)
          .withConverter<LoginResponse?>(
              $AuthEntityTable.$converterloginResponsen);
  @override
  List<GeneratedColumn> get $columns => [id, loginResponse];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'auth_entity';
  @override
  VerificationContext validateIntegrity(Insertable<AuthEntityData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    context.handle(_loginResponseMeta, const VerificationResult.success());
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  AuthEntityData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return AuthEntityData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}id'])!,
      loginResponse: $AuthEntityTable.$converterloginResponsen.fromSql(
          attachedDatabase.typeMapping.read(
              DriftSqlType.string, data['${effectivePrefix}login_response'])),
    );
  }

  @override
  $AuthEntityTable createAlias(String alias) {
    return $AuthEntityTable(attachedDatabase, alias);
  }

  static TypeConverter<LoginResponse, String> $converterloginResponse =
      const LoginResponseConvertor();
  static TypeConverter<LoginResponse?, String?> $converterloginResponsen =
      NullAwareTypeConverter.wrap($converterloginResponse);
}

class AuthEntityData extends DataClass implements Insertable<AuthEntityData> {
  final int id;
  final LoginResponse? loginResponse;
  const AuthEntityData({required this.id, this.loginResponse});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    if (!nullToAbsent || loginResponse != null) {
      map['login_response'] = Variable<String>(
          $AuthEntityTable.$converterloginResponsen.toSql(loginResponse));
    }
    return map;
  }

  AuthEntityCompanion toCompanion(bool nullToAbsent) {
    return AuthEntityCompanion(
      id: Value(id),
      loginResponse: loginResponse == null && nullToAbsent
          ? const Value.absent()
          : Value(loginResponse),
    );
  }

  factory AuthEntityData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return AuthEntityData(
      id: serializer.fromJson<int>(json['id']),
      loginResponse: serializer.fromJson<LoginResponse?>(json['loginResponse']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'loginResponse': serializer.toJson<LoginResponse?>(loginResponse),
    };
  }

  AuthEntityData copyWith(
          {int? id,
          Value<LoginResponse?> loginResponse = const Value.absent()}) =>
      AuthEntityData(
        id: id ?? this.id,
        loginResponse:
            loginResponse.present ? loginResponse.value : this.loginResponse,
      );
  AuthEntityData copyWithCompanion(AuthEntityCompanion data) {
    return AuthEntityData(
      id: data.id.present ? data.id.value : this.id,
      loginResponse: data.loginResponse.present
          ? data.loginResponse.value
          : this.loginResponse,
    );
  }

  @override
  String toString() {
    return (StringBuffer('AuthEntityData(')
          ..write('id: $id, ')
          ..write('loginResponse: $loginResponse')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, loginResponse);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is AuthEntityData &&
          other.id == this.id &&
          other.loginResponse == this.loginResponse);
}

class AuthEntityCompanion extends UpdateCompanion<AuthEntityData> {
  final Value<int> id;
  final Value<LoginResponse?> loginResponse;
  const AuthEntityCompanion({
    this.id = const Value.absent(),
    this.loginResponse = const Value.absent(),
  });
  AuthEntityCompanion.insert({
    this.id = const Value.absent(),
    this.loginResponse = const Value.absent(),
  });
  static Insertable<AuthEntityData> custom({
    Expression<int>? id,
    Expression<String>? loginResponse,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (loginResponse != null) 'login_response': loginResponse,
    });
  }

  AuthEntityCompanion copyWith(
      {Value<int>? id, Value<LoginResponse?>? loginResponse}) {
    return AuthEntityCompanion(
      id: id ?? this.id,
      loginResponse: loginResponse ?? this.loginResponse,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (loginResponse.present) {
      map['login_response'] = Variable<String>(
          $AuthEntityTable.$converterloginResponsen.toSql(loginResponse.value));
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('AuthEntityCompanion(')
          ..write('id: $id, ')
          ..write('loginResponse: $loginResponse')
          ..write(')'))
        .toString();
  }
}

class $CurrentUserEntityTable extends CurrentUserEntity
    with TableInfo<$CurrentUserEntityTable, CurrentUserEntityData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $CurrentUserEntityTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<String> id = GeneratedColumn<String>(
      'id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _itemMeta = const VerificationMeta('item');
  @override
  late final GeneratedColumnWithTypeConverter<CurrentUserDbModel?, String>
      item = GeneratedColumn<String>('item', aliasedName, true,
              type: DriftSqlType.string, requiredDuringInsert: false)
          .withConverter<CurrentUserDbModel?>(
              $CurrentUserEntityTable.$converteritemn);
  static const VerificationMeta _isFlushedMeta =
      const VerificationMeta('isFlushed');
  @override
  late final GeneratedColumn<bool> isFlushed = GeneratedColumn<bool>(
      'is_flushed', aliasedName, false,
      type: DriftSqlType.bool,
      requiredDuringInsert: true,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('CHECK ("is_flushed" IN (0, 1))'));
  @override
  List<GeneratedColumn> get $columns => [id, item, isFlushed];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'current_user_entity';
  @override
  VerificationContext validateIntegrity(
      Insertable<CurrentUserEntityData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    } else if (isInserting) {
      context.missing(_idMeta);
    }
    context.handle(_itemMeta, const VerificationResult.success());
    if (data.containsKey('is_flushed')) {
      context.handle(_isFlushedMeta,
          isFlushed.isAcceptableOrUnknown(data['is_flushed']!, _isFlushedMeta));
    } else if (isInserting) {
      context.missing(_isFlushedMeta);
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  CurrentUserEntityData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return CurrentUserEntityData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}id'])!,
      item: $CurrentUserEntityTable.$converteritemn.fromSql(attachedDatabase
          .typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}item'])),
      isFlushed: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}is_flushed'])!,
    );
  }

  @override
  $CurrentUserEntityTable createAlias(String alias) {
    return $CurrentUserEntityTable(attachedDatabase, alias);
  }

  static TypeConverter<CurrentUserDbModel, String> $converteritem =
      const CurrentUserDbModelConvertor();
  static TypeConverter<CurrentUserDbModel?, String?> $converteritemn =
      NullAwareTypeConverter.wrap($converteritem);
}

class CurrentUserEntityData extends DataClass
    implements Insertable<CurrentUserEntityData> {
  final String id;
  final CurrentUserDbModel? item;
  final bool isFlushed;
  const CurrentUserEntityData(
      {required this.id, this.item, required this.isFlushed});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<String>(id);
    if (!nullToAbsent || item != null) {
      map['item'] =
          Variable<String>($CurrentUserEntityTable.$converteritemn.toSql(item));
    }
    map['is_flushed'] = Variable<bool>(isFlushed);
    return map;
  }

  CurrentUserEntityCompanion toCompanion(bool nullToAbsent) {
    return CurrentUserEntityCompanion(
      id: Value(id),
      item: item == null && nullToAbsent ? const Value.absent() : Value(item),
      isFlushed: Value(isFlushed),
    );
  }

  factory CurrentUserEntityData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return CurrentUserEntityData(
      id: serializer.fromJson<String>(json['id']),
      item: serializer.fromJson<CurrentUserDbModel?>(json['item']),
      isFlushed: serializer.fromJson<bool>(json['isFlushed']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<String>(id),
      'item': serializer.toJson<CurrentUserDbModel?>(item),
      'isFlushed': serializer.toJson<bool>(isFlushed),
    };
  }

  CurrentUserEntityData copyWith(
          {String? id,
          Value<CurrentUserDbModel?> item = const Value.absent(),
          bool? isFlushed}) =>
      CurrentUserEntityData(
        id: id ?? this.id,
        item: item.present ? item.value : this.item,
        isFlushed: isFlushed ?? this.isFlushed,
      );
  CurrentUserEntityData copyWithCompanion(CurrentUserEntityCompanion data) {
    return CurrentUserEntityData(
      id: data.id.present ? data.id.value : this.id,
      item: data.item.present ? data.item.value : this.item,
      isFlushed: data.isFlushed.present ? data.isFlushed.value : this.isFlushed,
    );
  }

  @override
  String toString() {
    return (StringBuffer('CurrentUserEntityData(')
          ..write('id: $id, ')
          ..write('item: $item, ')
          ..write('isFlushed: $isFlushed')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, item, isFlushed);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is CurrentUserEntityData &&
          other.id == this.id &&
          other.item == this.item &&
          other.isFlushed == this.isFlushed);
}

class CurrentUserEntityCompanion
    extends UpdateCompanion<CurrentUserEntityData> {
  final Value<String> id;
  final Value<CurrentUserDbModel?> item;
  final Value<bool> isFlushed;
  final Value<int> rowid;
  const CurrentUserEntityCompanion({
    this.id = const Value.absent(),
    this.item = const Value.absent(),
    this.isFlushed = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  CurrentUserEntityCompanion.insert({
    required String id,
    this.item = const Value.absent(),
    required bool isFlushed,
    this.rowid = const Value.absent(),
  })  : id = Value(id),
        isFlushed = Value(isFlushed);
  static Insertable<CurrentUserEntityData> custom({
    Expression<String>? id,
    Expression<String>? item,
    Expression<bool>? isFlushed,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (item != null) 'item': item,
      if (isFlushed != null) 'is_flushed': isFlushed,
      if (rowid != null) 'rowid': rowid,
    });
  }

  CurrentUserEntityCompanion copyWith(
      {Value<String>? id,
      Value<CurrentUserDbModel?>? item,
      Value<bool>? isFlushed,
      Value<int>? rowid}) {
    return CurrentUserEntityCompanion(
      id: id ?? this.id,
      item: item ?? this.item,
      isFlushed: isFlushed ?? this.isFlushed,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<String>(id.value);
    }
    if (item.present) {
      map['item'] = Variable<String>(
          $CurrentUserEntityTable.$converteritemn.toSql(item.value));
    }
    if (isFlushed.present) {
      map['is_flushed'] = Variable<bool>(isFlushed.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('CurrentUserEntityCompanion(')
          ..write('id: $id, ')
          ..write('item: $item, ')
          ..write('isFlushed: $isFlushed, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $TicketEntityTable extends TicketEntity
    with TableInfo<$TicketEntityTable, TicketEntityData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $TicketEntityTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<String> id = GeneratedColumn<String>(
      'id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _itemMeta = const VerificationMeta('item');
  @override
  late final GeneratedColumnWithTypeConverter<WorkOrderDbModel?, String> item =
      GeneratedColumn<String>('item', aliasedName, true,
              type: DriftSqlType.string, requiredDuringInsert: false)
          .withConverter<WorkOrderDbModel?>($TicketEntityTable.$converteritemn);
  static const VerificationMeta _isFlushedMeta =
      const VerificationMeta('isFlushed');
  @override
  late final GeneratedColumn<bool> isFlushed = GeneratedColumn<bool>(
      'is_flushed', aliasedName, false,
      type: DriftSqlType.bool,
      requiredDuringInsert: true,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('CHECK ("is_flushed" IN (0, 1))'));
  @override
  List<GeneratedColumn> get $columns => [id, item, isFlushed];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'ticket_entity';
  @override
  VerificationContext validateIntegrity(Insertable<TicketEntityData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    } else if (isInserting) {
      context.missing(_idMeta);
    }
    context.handle(_itemMeta, const VerificationResult.success());
    if (data.containsKey('is_flushed')) {
      context.handle(_isFlushedMeta,
          isFlushed.isAcceptableOrUnknown(data['is_flushed']!, _isFlushedMeta));
    } else if (isInserting) {
      context.missing(_isFlushedMeta);
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  TicketEntityData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return TicketEntityData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}id'])!,
      item: $TicketEntityTable.$converteritemn.fromSql(attachedDatabase
          .typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}item'])),
      isFlushed: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}is_flushed'])!,
    );
  }

  @override
  $TicketEntityTable createAlias(String alias) {
    return $TicketEntityTable(attachedDatabase, alias);
  }

  static TypeConverter<WorkOrderDbModel, String> $converteritem =
      const TicketDbModelConvertor();
  static TypeConverter<WorkOrderDbModel?, String?> $converteritemn =
      NullAwareTypeConverter.wrap($converteritem);
}

class TicketEntityData extends DataClass
    implements Insertable<TicketEntityData> {
  final String id;
  final WorkOrderDbModel? item;
  final bool isFlushed;
  const TicketEntityData(
      {required this.id, this.item, required this.isFlushed});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<String>(id);
    if (!nullToAbsent || item != null) {
      map['item'] =
          Variable<String>($TicketEntityTable.$converteritemn.toSql(item));
    }
    map['is_flushed'] = Variable<bool>(isFlushed);
    return map;
  }

  TicketEntityCompanion toCompanion(bool nullToAbsent) {
    return TicketEntityCompanion(
      id: Value(id),
      item: item == null && nullToAbsent ? const Value.absent() : Value(item),
      isFlushed: Value(isFlushed),
    );
  }

  factory TicketEntityData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return TicketEntityData(
      id: serializer.fromJson<String>(json['id']),
      item: serializer.fromJson<WorkOrderDbModel?>(json['item']),
      isFlushed: serializer.fromJson<bool>(json['isFlushed']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<String>(id),
      'item': serializer.toJson<WorkOrderDbModel?>(item),
      'isFlushed': serializer.toJson<bool>(isFlushed),
    };
  }

  TicketEntityData copyWith(
          {String? id,
          Value<WorkOrderDbModel?> item = const Value.absent(),
          bool? isFlushed}) =>
      TicketEntityData(
        id: id ?? this.id,
        item: item.present ? item.value : this.item,
        isFlushed: isFlushed ?? this.isFlushed,
      );
  TicketEntityData copyWithCompanion(TicketEntityCompanion data) {
    return TicketEntityData(
      id: data.id.present ? data.id.value : this.id,
      item: data.item.present ? data.item.value : this.item,
      isFlushed: data.isFlushed.present ? data.isFlushed.value : this.isFlushed,
    );
  }

  @override
  String toString() {
    return (StringBuffer('TicketEntityData(')
          ..write('id: $id, ')
          ..write('item: $item, ')
          ..write('isFlushed: $isFlushed')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, item, isFlushed);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is TicketEntityData &&
          other.id == this.id &&
          other.item == this.item &&
          other.isFlushed == this.isFlushed);
}

class TicketEntityCompanion extends UpdateCompanion<TicketEntityData> {
  final Value<String> id;
  final Value<WorkOrderDbModel?> item;
  final Value<bool> isFlushed;
  final Value<int> rowid;
  const TicketEntityCompanion({
    this.id = const Value.absent(),
    this.item = const Value.absent(),
    this.isFlushed = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  TicketEntityCompanion.insert({
    required String id,
    this.item = const Value.absent(),
    required bool isFlushed,
    this.rowid = const Value.absent(),
  })  : id = Value(id),
        isFlushed = Value(isFlushed);
  static Insertable<TicketEntityData> custom({
    Expression<String>? id,
    Expression<String>? item,
    Expression<bool>? isFlushed,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (item != null) 'item': item,
      if (isFlushed != null) 'is_flushed': isFlushed,
      if (rowid != null) 'rowid': rowid,
    });
  }

  TicketEntityCompanion copyWith(
      {Value<String>? id,
      Value<WorkOrderDbModel?>? item,
      Value<bool>? isFlushed,
      Value<int>? rowid}) {
    return TicketEntityCompanion(
      id: id ?? this.id,
      item: item ?? this.item,
      isFlushed: isFlushed ?? this.isFlushed,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<String>(id.value);
    }
    if (item.present) {
      map['item'] = Variable<String>(
          $TicketEntityTable.$converteritemn.toSql(item.value));
    }
    if (isFlushed.present) {
      map['is_flushed'] = Variable<bool>(isFlushed.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('TicketEntityCompanion(')
          ..write('id: $id, ')
          ..write('item: $item, ')
          ..write('isFlushed: $isFlushed, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $TicketMessageEntityTable extends TicketMessageEntity
    with TableInfo<$TicketMessageEntityTable, TicketMessageEntityData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $TicketMessageEntityTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<String> id = GeneratedColumn<String>(
      'id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _ticketMeta = const VerificationMeta('ticket');
  @override
  late final GeneratedColumn<String> ticket = GeneratedColumn<String>(
      'ticket', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _channelMeta =
      const VerificationMeta('channel');
  @override
  late final GeneratedColumn<String> channel = GeneratedColumn<String>(
      'channel', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _itemMeta = const VerificationMeta('item');
  @override
  late final GeneratedColumnWithTypeConverter<MessageDbModel?, String> item =
      GeneratedColumn<String>('item', aliasedName, true,
              type: DriftSqlType.string, requiredDuringInsert: false)
          .withConverter<MessageDbModel?>(
              $TicketMessageEntityTable.$converteritemn);
  static const VerificationMeta _timeMeta = const VerificationMeta('time');
  @override
  late final GeneratedColumn<DateTime> time = GeneratedColumn<DateTime>(
      'time', aliasedName, false,
      type: DriftSqlType.dateTime, requiredDuringInsert: true);
  static const VerificationMeta _isFlushedMeta =
      const VerificationMeta('isFlushed');
  @override
  late final GeneratedColumn<bool> isFlushed = GeneratedColumn<bool>(
      'is_flushed', aliasedName, false,
      type: DriftSqlType.bool,
      requiredDuringInsert: true,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('CHECK ("is_flushed" IN (0, 1))'));
  @override
  List<GeneratedColumn> get $columns =>
      [id, ticket, channel, item, time, isFlushed];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'ticket_message_entity';
  @override
  VerificationContext validateIntegrity(
      Insertable<TicketMessageEntityData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    } else if (isInserting) {
      context.missing(_idMeta);
    }
    if (data.containsKey('ticket')) {
      context.handle(_ticketMeta,
          ticket.isAcceptableOrUnknown(data['ticket']!, _ticketMeta));
    } else if (isInserting) {
      context.missing(_ticketMeta);
    }
    if (data.containsKey('channel')) {
      context.handle(_channelMeta,
          channel.isAcceptableOrUnknown(data['channel']!, _channelMeta));
    } else if (isInserting) {
      context.missing(_channelMeta);
    }
    context.handle(_itemMeta, const VerificationResult.success());
    if (data.containsKey('time')) {
      context.handle(
          _timeMeta, time.isAcceptableOrUnknown(data['time']!, _timeMeta));
    } else if (isInserting) {
      context.missing(_timeMeta);
    }
    if (data.containsKey('is_flushed')) {
      context.handle(_isFlushedMeta,
          isFlushed.isAcceptableOrUnknown(data['is_flushed']!, _isFlushedMeta));
    } else if (isInserting) {
      context.missing(_isFlushedMeta);
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id, ticket};
  @override
  TicketMessageEntityData map(Map<String, dynamic> data,
      {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return TicketMessageEntityData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}id'])!,
      ticket: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}ticket'])!,
      channel: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}channel'])!,
      item: $TicketMessageEntityTable.$converteritemn.fromSql(attachedDatabase
          .typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}item'])),
      time: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}time'])!,
      isFlushed: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}is_flushed'])!,
    );
  }

  @override
  $TicketMessageEntityTable createAlias(String alias) {
    return $TicketMessageEntityTable(attachedDatabase, alias);
  }

  static TypeConverter<MessageDbModel, String> $converteritem =
      const MessageDbModelTypeConvertor();
  static TypeConverter<MessageDbModel?, String?> $converteritemn =
      NullAwareTypeConverter.wrap($converteritem);
}

class TicketMessageEntityData extends DataClass
    implements Insertable<TicketMessageEntityData> {
  final String id;
  final String ticket;
  final String channel;
  final MessageDbModel? item;
  final DateTime time;
  final bool isFlushed;
  const TicketMessageEntityData(
      {required this.id,
      required this.ticket,
      required this.channel,
      this.item,
      required this.time,
      required this.isFlushed});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<String>(id);
    map['ticket'] = Variable<String>(ticket);
    map['channel'] = Variable<String>(channel);
    if (!nullToAbsent || item != null) {
      map['item'] = Variable<String>(
          $TicketMessageEntityTable.$converteritemn.toSql(item));
    }
    map['time'] = Variable<DateTime>(time);
    map['is_flushed'] = Variable<bool>(isFlushed);
    return map;
  }

  TicketMessageEntityCompanion toCompanion(bool nullToAbsent) {
    return TicketMessageEntityCompanion(
      id: Value(id),
      ticket: Value(ticket),
      channel: Value(channel),
      item: item == null && nullToAbsent ? const Value.absent() : Value(item),
      time: Value(time),
      isFlushed: Value(isFlushed),
    );
  }

  factory TicketMessageEntityData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return TicketMessageEntityData(
      id: serializer.fromJson<String>(json['id']),
      ticket: serializer.fromJson<String>(json['ticket']),
      channel: serializer.fromJson<String>(json['channel']),
      item: serializer.fromJson<MessageDbModel?>(json['item']),
      time: serializer.fromJson<DateTime>(json['time']),
      isFlushed: serializer.fromJson<bool>(json['isFlushed']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<String>(id),
      'ticket': serializer.toJson<String>(ticket),
      'channel': serializer.toJson<String>(channel),
      'item': serializer.toJson<MessageDbModel?>(item),
      'time': serializer.toJson<DateTime>(time),
      'isFlushed': serializer.toJson<bool>(isFlushed),
    };
  }

  TicketMessageEntityData copyWith(
          {String? id,
          String? ticket,
          String? channel,
          Value<MessageDbModel?> item = const Value.absent(),
          DateTime? time,
          bool? isFlushed}) =>
      TicketMessageEntityData(
        id: id ?? this.id,
        ticket: ticket ?? this.ticket,
        channel: channel ?? this.channel,
        item: item.present ? item.value : this.item,
        time: time ?? this.time,
        isFlushed: isFlushed ?? this.isFlushed,
      );
  TicketMessageEntityData copyWithCompanion(TicketMessageEntityCompanion data) {
    return TicketMessageEntityData(
      id: data.id.present ? data.id.value : this.id,
      ticket: data.ticket.present ? data.ticket.value : this.ticket,
      channel: data.channel.present ? data.channel.value : this.channel,
      item: data.item.present ? data.item.value : this.item,
      time: data.time.present ? data.time.value : this.time,
      isFlushed: data.isFlushed.present ? data.isFlushed.value : this.isFlushed,
    );
  }

  @override
  String toString() {
    return (StringBuffer('TicketMessageEntityData(')
          ..write('id: $id, ')
          ..write('ticket: $ticket, ')
          ..write('channel: $channel, ')
          ..write('item: $item, ')
          ..write('time: $time, ')
          ..write('isFlushed: $isFlushed')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, ticket, channel, item, time, isFlushed);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is TicketMessageEntityData &&
          other.id == this.id &&
          other.ticket == this.ticket &&
          other.channel == this.channel &&
          other.item == this.item &&
          other.time == this.time &&
          other.isFlushed == this.isFlushed);
}

class TicketMessageEntityCompanion
    extends UpdateCompanion<TicketMessageEntityData> {
  final Value<String> id;
  final Value<String> ticket;
  final Value<String> channel;
  final Value<MessageDbModel?> item;
  final Value<DateTime> time;
  final Value<bool> isFlushed;
  final Value<int> rowid;
  const TicketMessageEntityCompanion({
    this.id = const Value.absent(),
    this.ticket = const Value.absent(),
    this.channel = const Value.absent(),
    this.item = const Value.absent(),
    this.time = const Value.absent(),
    this.isFlushed = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  TicketMessageEntityCompanion.insert({
    required String id,
    required String ticket,
    required String channel,
    this.item = const Value.absent(),
    required DateTime time,
    required bool isFlushed,
    this.rowid = const Value.absent(),
  })  : id = Value(id),
        ticket = Value(ticket),
        channel = Value(channel),
        time = Value(time),
        isFlushed = Value(isFlushed);
  static Insertable<TicketMessageEntityData> custom({
    Expression<String>? id,
    Expression<String>? ticket,
    Expression<String>? channel,
    Expression<String>? item,
    Expression<DateTime>? time,
    Expression<bool>? isFlushed,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (ticket != null) 'ticket': ticket,
      if (channel != null) 'channel': channel,
      if (item != null) 'item': item,
      if (time != null) 'time': time,
      if (isFlushed != null) 'is_flushed': isFlushed,
      if (rowid != null) 'rowid': rowid,
    });
  }

  TicketMessageEntityCompanion copyWith(
      {Value<String>? id,
      Value<String>? ticket,
      Value<String>? channel,
      Value<MessageDbModel?>? item,
      Value<DateTime>? time,
      Value<bool>? isFlushed,
      Value<int>? rowid}) {
    return TicketMessageEntityCompanion(
      id: id ?? this.id,
      ticket: ticket ?? this.ticket,
      channel: channel ?? this.channel,
      item: item ?? this.item,
      time: time ?? this.time,
      isFlushed: isFlushed ?? this.isFlushed,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<String>(id.value);
    }
    if (ticket.present) {
      map['ticket'] = Variable<String>(ticket.value);
    }
    if (channel.present) {
      map['channel'] = Variable<String>(channel.value);
    }
    if (item.present) {
      map['item'] = Variable<String>(
          $TicketMessageEntityTable.$converteritemn.toSql(item.value));
    }
    if (time.present) {
      map['time'] = Variable<DateTime>(time.value);
    }
    if (isFlushed.present) {
      map['is_flushed'] = Variable<bool>(isFlushed.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('TicketMessageEntityCompanion(')
          ..write('id: $id, ')
          ..write('ticket: $ticket, ')
          ..write('channel: $channel, ')
          ..write('item: $item, ')
          ..write('time: $time, ')
          ..write('isFlushed: $isFlushed, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $ProcedureTemplateEntityTable extends ProcedureTemplateEntity
    with TableInfo<$ProcedureTemplateEntityTable, ProcedureTemplateEntityData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $ProcedureTemplateEntityTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<String> id = GeneratedColumn<String>(
      'id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _itemMeta = const VerificationMeta('item');
  @override
  late final GeneratedColumnWithTypeConverter<ProcedureTemplateDbModel?, String>
      item = GeneratedColumn<String>('item', aliasedName, true,
              type: DriftSqlType.string, requiredDuringInsert: false)
          .withConverter<ProcedureTemplateDbModel?>(
              $ProcedureTemplateEntityTable.$converteritemn);
  @override
  List<GeneratedColumn> get $columns => [id, item];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'procedure_template_entity';
  @override
  VerificationContext validateIntegrity(
      Insertable<ProcedureTemplateEntityData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    } else if (isInserting) {
      context.missing(_idMeta);
    }
    context.handle(_itemMeta, const VerificationResult.success());
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  ProcedureTemplateEntityData map(Map<String, dynamic> data,
      {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return ProcedureTemplateEntityData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}id'])!,
      item: $ProcedureTemplateEntityTable.$converteritemn.fromSql(
          attachedDatabase.typeMapping
              .read(DriftSqlType.string, data['${effectivePrefix}item'])),
    );
  }

  @override
  $ProcedureTemplateEntityTable createAlias(String alias) {
    return $ProcedureTemplateEntityTable(attachedDatabase, alias);
  }

  static TypeConverter<ProcedureTemplateDbModel, String> $converteritem =
      const ProcedureTemplateDbModelConvertor();
  static TypeConverter<ProcedureTemplateDbModel?, String?> $converteritemn =
      NullAwareTypeConverter.wrap($converteritem);
}

class ProcedureTemplateEntityData extends DataClass
    implements Insertable<ProcedureTemplateEntityData> {
  final String id;
  final ProcedureTemplateDbModel? item;
  const ProcedureTemplateEntityData({required this.id, this.item});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<String>(id);
    if (!nullToAbsent || item != null) {
      map['item'] = Variable<String>(
          $ProcedureTemplateEntityTable.$converteritemn.toSql(item));
    }
    return map;
  }

  ProcedureTemplateEntityCompanion toCompanion(bool nullToAbsent) {
    return ProcedureTemplateEntityCompanion(
      id: Value(id),
      item: item == null && nullToAbsent ? const Value.absent() : Value(item),
    );
  }

  factory ProcedureTemplateEntityData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return ProcedureTemplateEntityData(
      id: serializer.fromJson<String>(json['id']),
      item: serializer.fromJson<ProcedureTemplateDbModel?>(json['item']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<String>(id),
      'item': serializer.toJson<ProcedureTemplateDbModel?>(item),
    };
  }

  ProcedureTemplateEntityData copyWith(
          {String? id,
          Value<ProcedureTemplateDbModel?> item = const Value.absent()}) =>
      ProcedureTemplateEntityData(
        id: id ?? this.id,
        item: item.present ? item.value : this.item,
      );
  ProcedureTemplateEntityData copyWithCompanion(
      ProcedureTemplateEntityCompanion data) {
    return ProcedureTemplateEntityData(
      id: data.id.present ? data.id.value : this.id,
      item: data.item.present ? data.item.value : this.item,
    );
  }

  @override
  String toString() {
    return (StringBuffer('ProcedureTemplateEntityData(')
          ..write('id: $id, ')
          ..write('item: $item')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, item);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is ProcedureTemplateEntityData &&
          other.id == this.id &&
          other.item == this.item);
}

class ProcedureTemplateEntityCompanion
    extends UpdateCompanion<ProcedureTemplateEntityData> {
  final Value<String> id;
  final Value<ProcedureTemplateDbModel?> item;
  final Value<int> rowid;
  const ProcedureTemplateEntityCompanion({
    this.id = const Value.absent(),
    this.item = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  ProcedureTemplateEntityCompanion.insert({
    required String id,
    this.item = const Value.absent(),
    this.rowid = const Value.absent(),
  }) : id = Value(id);
  static Insertable<ProcedureTemplateEntityData> custom({
    Expression<String>? id,
    Expression<String>? item,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (item != null) 'item': item,
      if (rowid != null) 'rowid': rowid,
    });
  }

  ProcedureTemplateEntityCompanion copyWith(
      {Value<String>? id,
      Value<ProcedureTemplateDbModel?>? item,
      Value<int>? rowid}) {
    return ProcedureTemplateEntityCompanion(
      id: id ?? this.id,
      item: item ?? this.item,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<String>(id.value);
    }
    if (item.present) {
      map['item'] = Variable<String>(
          $ProcedureTemplateEntityTable.$converteritemn.toSql(item.value));
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('ProcedureTemplateEntityCompanion(')
          ..write('id: $id, ')
          ..write('item: $item, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $TicketListEntityTable extends TicketListEntity
    with TableInfo<$TicketListEntityTable, TicketListEntityData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $TicketListEntityTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<String> id = GeneratedColumn<String>(
      'id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _typeMeta = const VerificationMeta('type');
  @override
  late final GeneratedColumn<String> type = GeneratedColumn<String>(
      'type', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  @override
  List<GeneratedColumn> get $columns => [id, type];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'ticket_list_entity';
  @override
  VerificationContext validateIntegrity(
      Insertable<TicketListEntityData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    } else if (isInserting) {
      context.missing(_idMeta);
    }
    if (data.containsKey('type')) {
      context.handle(
          _typeMeta, type.isAcceptableOrUnknown(data['type']!, _typeMeta));
    } else if (isInserting) {
      context.missing(_typeMeta);
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id, type};
  @override
  TicketListEntityData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return TicketListEntityData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}id'])!,
      type: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}type'])!,
    );
  }

  @override
  $TicketListEntityTable createAlias(String alias) {
    return $TicketListEntityTable(attachedDatabase, alias);
  }
}

class TicketListEntityData extends DataClass
    implements Insertable<TicketListEntityData> {
  final String id;
  final String type;
  const TicketListEntityData({required this.id, required this.type});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<String>(id);
    map['type'] = Variable<String>(type);
    return map;
  }

  TicketListEntityCompanion toCompanion(bool nullToAbsent) {
    return TicketListEntityCompanion(
      id: Value(id),
      type: Value(type),
    );
  }

  factory TicketListEntityData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return TicketListEntityData(
      id: serializer.fromJson<String>(json['id']),
      type: serializer.fromJson<String>(json['type']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<String>(id),
      'type': serializer.toJson<String>(type),
    };
  }

  TicketListEntityData copyWith({String? id, String? type}) =>
      TicketListEntityData(
        id: id ?? this.id,
        type: type ?? this.type,
      );
  TicketListEntityData copyWithCompanion(TicketListEntityCompanion data) {
    return TicketListEntityData(
      id: data.id.present ? data.id.value : this.id,
      type: data.type.present ? data.type.value : this.type,
    );
  }

  @override
  String toString() {
    return (StringBuffer('TicketListEntityData(')
          ..write('id: $id, ')
          ..write('type: $type')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, type);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is TicketListEntityData &&
          other.id == this.id &&
          other.type == this.type);
}

class TicketListEntityCompanion extends UpdateCompanion<TicketListEntityData> {
  final Value<String> id;
  final Value<String> type;
  final Value<int> rowid;
  const TicketListEntityCompanion({
    this.id = const Value.absent(),
    this.type = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  TicketListEntityCompanion.insert({
    required String id,
    required String type,
    this.rowid = const Value.absent(),
  })  : id = Value(id),
        type = Value(type);
  static Insertable<TicketListEntityData> custom({
    Expression<String>? id,
    Expression<String>? type,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (type != null) 'type': type,
      if (rowid != null) 'rowid': rowid,
    });
  }

  TicketListEntityCompanion copyWith(
      {Value<String>? id, Value<String>? type, Value<int>? rowid}) {
    return TicketListEntityCompanion(
      id: id ?? this.id,
      type: type ?? this.type,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<String>(id.value);
    }
    if (type.present) {
      map['type'] = Variable<String>(type.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('TicketListEntityCompanion(')
          ..write('id: $id, ')
          ..write('type: $type, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $InventoryPartEntityTable extends InventoryPartEntity
    with TableInfo<$InventoryPartEntityTable, InventoryPartEntityData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $InventoryPartEntityTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<String> id = GeneratedColumn<String>(
      'id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _itemMeta = const VerificationMeta('item');
  @override
  late final GeneratedColumnWithTypeConverter<InventoryPartDbModel?, String>
      item = GeneratedColumn<String>('item', aliasedName, true,
              type: DriftSqlType.string, requiredDuringInsert: false)
          .withConverter<InventoryPartDbModel?>(
              $InventoryPartEntityTable.$converteritemn);
  @override
  List<GeneratedColumn> get $columns => [id, item];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'inventory_part_entity';
  @override
  VerificationContext validateIntegrity(
      Insertable<InventoryPartEntityData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    } else if (isInserting) {
      context.missing(_idMeta);
    }
    context.handle(_itemMeta, const VerificationResult.success());
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  InventoryPartEntityData map(Map<String, dynamic> data,
      {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return InventoryPartEntityData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}id'])!,
      item: $InventoryPartEntityTable.$converteritemn.fromSql(attachedDatabase
          .typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}item'])),
    );
  }

  @override
  $InventoryPartEntityTable createAlias(String alias) {
    return $InventoryPartEntityTable(attachedDatabase, alias);
  }

  static TypeConverter<InventoryPartDbModel, String> $converteritem =
      const InventoryPartDbModelConvertor();
  static TypeConverter<InventoryPartDbModel?, String?> $converteritemn =
      NullAwareTypeConverter.wrap($converteritem);
}

class InventoryPartEntityData extends DataClass
    implements Insertable<InventoryPartEntityData> {
  final String id;
  final InventoryPartDbModel? item;
  const InventoryPartEntityData({required this.id, this.item});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<String>(id);
    if (!nullToAbsent || item != null) {
      map['item'] = Variable<String>(
          $InventoryPartEntityTable.$converteritemn.toSql(item));
    }
    return map;
  }

  InventoryPartEntityCompanion toCompanion(bool nullToAbsent) {
    return InventoryPartEntityCompanion(
      id: Value(id),
      item: item == null && nullToAbsent ? const Value.absent() : Value(item),
    );
  }

  factory InventoryPartEntityData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return InventoryPartEntityData(
      id: serializer.fromJson<String>(json['id']),
      item: serializer.fromJson<InventoryPartDbModel?>(json['item']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<String>(id),
      'item': serializer.toJson<InventoryPartDbModel?>(item),
    };
  }

  InventoryPartEntityData copyWith(
          {String? id,
          Value<InventoryPartDbModel?> item = const Value.absent()}) =>
      InventoryPartEntityData(
        id: id ?? this.id,
        item: item.present ? item.value : this.item,
      );
  InventoryPartEntityData copyWithCompanion(InventoryPartEntityCompanion data) {
    return InventoryPartEntityData(
      id: data.id.present ? data.id.value : this.id,
      item: data.item.present ? data.item.value : this.item,
    );
  }

  @override
  String toString() {
    return (StringBuffer('InventoryPartEntityData(')
          ..write('id: $id, ')
          ..write('item: $item')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, item);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is InventoryPartEntityData &&
          other.id == this.id &&
          other.item == this.item);
}

class InventoryPartEntityCompanion
    extends UpdateCompanion<InventoryPartEntityData> {
  final Value<String> id;
  final Value<InventoryPartDbModel?> item;
  final Value<int> rowid;
  const InventoryPartEntityCompanion({
    this.id = const Value.absent(),
    this.item = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  InventoryPartEntityCompanion.insert({
    required String id,
    this.item = const Value.absent(),
    this.rowid = const Value.absent(),
  }) : id = Value(id);
  static Insertable<InventoryPartEntityData> custom({
    Expression<String>? id,
    Expression<String>? item,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (item != null) 'item': item,
      if (rowid != null) 'rowid': rowid,
    });
  }

  InventoryPartEntityCompanion copyWith(
      {Value<String>? id,
      Value<InventoryPartDbModel?>? item,
      Value<int>? rowid}) {
    return InventoryPartEntityCompanion(
      id: id ?? this.id,
      item: item ?? this.item,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<String>(id.value);
    }
    if (item.present) {
      map['item'] = Variable<String>(
          $InventoryPartEntityTable.$converteritemn.toSql(item.value));
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('InventoryPartEntityCompanion(')
          ..write('id: $id, ')
          ..write('item: $item, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $ProcedureEntityTable extends ProcedureEntity
    with TableInfo<$ProcedureEntityTable, ProcedureEntityData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $ProcedureEntityTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<String> id = GeneratedColumn<String>(
      'id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _ticketIDMeta =
      const VerificationMeta('ticketID');
  @override
  late final GeneratedColumn<String> ticketID = GeneratedColumn<String>(
      'ticket_i_d', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _templateIDMeta =
      const VerificationMeta('templateID');
  @override
  late final GeneratedColumn<String> templateID = GeneratedColumn<String>(
      'template_i_d', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _itemMeta = const VerificationMeta('item');
  @override
  late final GeneratedColumnWithTypeConverter<ProcedureDbModel?, String> item =
      GeneratedColumn<String>('item', aliasedName, true,
              type: DriftSqlType.string, requiredDuringInsert: false)
          .withConverter<ProcedureDbModel?>(
              $ProcedureEntityTable.$converteritemn);
  static const VerificationMeta _isFlushedMeta =
      const VerificationMeta('isFlushed');
  @override
  late final GeneratedColumn<bool> isFlushed = GeneratedColumn<bool>(
      'is_flushed', aliasedName, false,
      type: DriftSqlType.bool,
      requiredDuringInsert: true,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('CHECK ("is_flushed" IN (0, 1))'));
  static const VerificationMeta _isDeletedMeta =
      const VerificationMeta('isDeleted');
  @override
  late final GeneratedColumn<bool> isDeleted = GeneratedColumn<bool>(
      'is_deleted', aliasedName, false,
      type: DriftSqlType.bool,
      requiredDuringInsert: true,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('CHECK ("is_deleted" IN (0, 1))'));
  static const VerificationMeta _isDownloadedMeta =
      const VerificationMeta('isDownloaded');
  @override
  late final GeneratedColumn<bool> isDownloaded = GeneratedColumn<bool>(
      'is_downloaded', aliasedName, false,
      type: DriftSqlType.bool,
      requiredDuringInsert: true,
      defaultConstraints: GeneratedColumn.constraintIsAlways(
          'CHECK ("is_downloaded" IN (0, 1))'));
  static const VerificationMeta _statusMeta = const VerificationMeta('status');
  @override
  late final GeneratedColumnWithTypeConverter<ProcedureEntityStatus, String>
      status = GeneratedColumn<String>('status', aliasedName, false,
              type: DriftSqlType.string, requiredDuringInsert: true)
          .withConverter<ProcedureEntityStatus>(
              $ProcedureEntityTable.$converterstatus);
  @override
  List<GeneratedColumn> get $columns => [
        id,
        ticketID,
        templateID,
        item,
        isFlushed,
        isDeleted,
        isDownloaded,
        status
      ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'procedure_entity';
  @override
  VerificationContext validateIntegrity(
      Insertable<ProcedureEntityData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    } else if (isInserting) {
      context.missing(_idMeta);
    }
    if (data.containsKey('ticket_i_d')) {
      context.handle(_ticketIDMeta,
          ticketID.isAcceptableOrUnknown(data['ticket_i_d']!, _ticketIDMeta));
    } else if (isInserting) {
      context.missing(_ticketIDMeta);
    }
    if (data.containsKey('template_i_d')) {
      context.handle(
          _templateIDMeta,
          templateID.isAcceptableOrUnknown(
              data['template_i_d']!, _templateIDMeta));
    }
    context.handle(_itemMeta, const VerificationResult.success());
    if (data.containsKey('is_flushed')) {
      context.handle(_isFlushedMeta,
          isFlushed.isAcceptableOrUnknown(data['is_flushed']!, _isFlushedMeta));
    } else if (isInserting) {
      context.missing(_isFlushedMeta);
    }
    if (data.containsKey('is_deleted')) {
      context.handle(_isDeletedMeta,
          isDeleted.isAcceptableOrUnknown(data['is_deleted']!, _isDeletedMeta));
    } else if (isInserting) {
      context.missing(_isDeletedMeta);
    }
    if (data.containsKey('is_downloaded')) {
      context.handle(
          _isDownloadedMeta,
          isDownloaded.isAcceptableOrUnknown(
              data['is_downloaded']!, _isDownloadedMeta));
    } else if (isInserting) {
      context.missing(_isDownloadedMeta);
    }
    context.handle(_statusMeta, const VerificationResult.success());
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  ProcedureEntityData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return ProcedureEntityData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}id'])!,
      ticketID: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}ticket_i_d'])!,
      templateID: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}template_i_d']),
      item: $ProcedureEntityTable.$converteritemn.fromSql(attachedDatabase
          .typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}item'])),
      isFlushed: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}is_flushed'])!,
      isDeleted: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}is_deleted'])!,
      isDownloaded: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}is_downloaded'])!,
      status: $ProcedureEntityTable.$converterstatus.fromSql(attachedDatabase
          .typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}status'])!),
    );
  }

  @override
  $ProcedureEntityTable createAlias(String alias) {
    return $ProcedureEntityTable(attachedDatabase, alias);
  }

  static TypeConverter<ProcedureDbModel, String> $converteritem =
      const ProcedureDbModelConvertor();
  static TypeConverter<ProcedureDbModel?, String?> $converteritemn =
      NullAwareTypeConverter.wrap($converteritem);
  static JsonTypeConverter2<ProcedureEntityStatus, String, String>
      $converterstatus = const EnumNameConverter<ProcedureEntityStatus>(
          ProcedureEntityStatus.values);
}

class ProcedureEntityData extends DataClass
    implements Insertable<ProcedureEntityData> {
  final String id;
  final String ticketID;
  final String? templateID;
  final ProcedureDbModel? item;
  final bool isFlushed;
  final bool isDeleted;
  final bool isDownloaded;
  final ProcedureEntityStatus status;
  const ProcedureEntityData(
      {required this.id,
      required this.ticketID,
      this.templateID,
      this.item,
      required this.isFlushed,
      required this.isDeleted,
      required this.isDownloaded,
      required this.status});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<String>(id);
    map['ticket_i_d'] = Variable<String>(ticketID);
    if (!nullToAbsent || templateID != null) {
      map['template_i_d'] = Variable<String>(templateID);
    }
    if (!nullToAbsent || item != null) {
      map['item'] =
          Variable<String>($ProcedureEntityTable.$converteritemn.toSql(item));
    }
    map['is_flushed'] = Variable<bool>(isFlushed);
    map['is_deleted'] = Variable<bool>(isDeleted);
    map['is_downloaded'] = Variable<bool>(isDownloaded);
    {
      map['status'] = Variable<String>(
          $ProcedureEntityTable.$converterstatus.toSql(status));
    }
    return map;
  }

  ProcedureEntityCompanion toCompanion(bool nullToAbsent) {
    return ProcedureEntityCompanion(
      id: Value(id),
      ticketID: Value(ticketID),
      templateID: templateID == null && nullToAbsent
          ? const Value.absent()
          : Value(templateID),
      item: item == null && nullToAbsent ? const Value.absent() : Value(item),
      isFlushed: Value(isFlushed),
      isDeleted: Value(isDeleted),
      isDownloaded: Value(isDownloaded),
      status: Value(status),
    );
  }

  factory ProcedureEntityData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return ProcedureEntityData(
      id: serializer.fromJson<String>(json['id']),
      ticketID: serializer.fromJson<String>(json['ticketID']),
      templateID: serializer.fromJson<String?>(json['templateID']),
      item: serializer.fromJson<ProcedureDbModel?>(json['item']),
      isFlushed: serializer.fromJson<bool>(json['isFlushed']),
      isDeleted: serializer.fromJson<bool>(json['isDeleted']),
      isDownloaded: serializer.fromJson<bool>(json['isDownloaded']),
      status: $ProcedureEntityTable.$converterstatus
          .fromJson(serializer.fromJson<String>(json['status'])),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<String>(id),
      'ticketID': serializer.toJson<String>(ticketID),
      'templateID': serializer.toJson<String?>(templateID),
      'item': serializer.toJson<ProcedureDbModel?>(item),
      'isFlushed': serializer.toJson<bool>(isFlushed),
      'isDeleted': serializer.toJson<bool>(isDeleted),
      'isDownloaded': serializer.toJson<bool>(isDownloaded),
      'status': serializer.toJson<String>(
          $ProcedureEntityTable.$converterstatus.toJson(status)),
    };
  }

  ProcedureEntityData copyWith(
          {String? id,
          String? ticketID,
          Value<String?> templateID = const Value.absent(),
          Value<ProcedureDbModel?> item = const Value.absent(),
          bool? isFlushed,
          bool? isDeleted,
          bool? isDownloaded,
          ProcedureEntityStatus? status}) =>
      ProcedureEntityData(
        id: id ?? this.id,
        ticketID: ticketID ?? this.ticketID,
        templateID: templateID.present ? templateID.value : this.templateID,
        item: item.present ? item.value : this.item,
        isFlushed: isFlushed ?? this.isFlushed,
        isDeleted: isDeleted ?? this.isDeleted,
        isDownloaded: isDownloaded ?? this.isDownloaded,
        status: status ?? this.status,
      );
  ProcedureEntityData copyWithCompanion(ProcedureEntityCompanion data) {
    return ProcedureEntityData(
      id: data.id.present ? data.id.value : this.id,
      ticketID: data.ticketID.present ? data.ticketID.value : this.ticketID,
      templateID:
          data.templateID.present ? data.templateID.value : this.templateID,
      item: data.item.present ? data.item.value : this.item,
      isFlushed: data.isFlushed.present ? data.isFlushed.value : this.isFlushed,
      isDeleted: data.isDeleted.present ? data.isDeleted.value : this.isDeleted,
      isDownloaded: data.isDownloaded.present
          ? data.isDownloaded.value
          : this.isDownloaded,
      status: data.status.present ? data.status.value : this.status,
    );
  }

  @override
  String toString() {
    return (StringBuffer('ProcedureEntityData(')
          ..write('id: $id, ')
          ..write('ticketID: $ticketID, ')
          ..write('templateID: $templateID, ')
          ..write('item: $item, ')
          ..write('isFlushed: $isFlushed, ')
          ..write('isDeleted: $isDeleted, ')
          ..write('isDownloaded: $isDownloaded, ')
          ..write('status: $status')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, ticketID, templateID, item, isFlushed,
      isDeleted, isDownloaded, status);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is ProcedureEntityData &&
          other.id == this.id &&
          other.ticketID == this.ticketID &&
          other.templateID == this.templateID &&
          other.item == this.item &&
          other.isFlushed == this.isFlushed &&
          other.isDeleted == this.isDeleted &&
          other.isDownloaded == this.isDownloaded &&
          other.status == this.status);
}

class ProcedureEntityCompanion extends UpdateCompanion<ProcedureEntityData> {
  final Value<String> id;
  final Value<String> ticketID;
  final Value<String?> templateID;
  final Value<ProcedureDbModel?> item;
  final Value<bool> isFlushed;
  final Value<bool> isDeleted;
  final Value<bool> isDownloaded;
  final Value<ProcedureEntityStatus> status;
  final Value<int> rowid;
  const ProcedureEntityCompanion({
    this.id = const Value.absent(),
    this.ticketID = const Value.absent(),
    this.templateID = const Value.absent(),
    this.item = const Value.absent(),
    this.isFlushed = const Value.absent(),
    this.isDeleted = const Value.absent(),
    this.isDownloaded = const Value.absent(),
    this.status = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  ProcedureEntityCompanion.insert({
    required String id,
    required String ticketID,
    this.templateID = const Value.absent(),
    this.item = const Value.absent(),
    required bool isFlushed,
    required bool isDeleted,
    required bool isDownloaded,
    required ProcedureEntityStatus status,
    this.rowid = const Value.absent(),
  })  : id = Value(id),
        ticketID = Value(ticketID),
        isFlushed = Value(isFlushed),
        isDeleted = Value(isDeleted),
        isDownloaded = Value(isDownloaded),
        status = Value(status);
  static Insertable<ProcedureEntityData> custom({
    Expression<String>? id,
    Expression<String>? ticketID,
    Expression<String>? templateID,
    Expression<String>? item,
    Expression<bool>? isFlushed,
    Expression<bool>? isDeleted,
    Expression<bool>? isDownloaded,
    Expression<String>? status,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (ticketID != null) 'ticket_i_d': ticketID,
      if (templateID != null) 'template_i_d': templateID,
      if (item != null) 'item': item,
      if (isFlushed != null) 'is_flushed': isFlushed,
      if (isDeleted != null) 'is_deleted': isDeleted,
      if (isDownloaded != null) 'is_downloaded': isDownloaded,
      if (status != null) 'status': status,
      if (rowid != null) 'rowid': rowid,
    });
  }

  ProcedureEntityCompanion copyWith(
      {Value<String>? id,
      Value<String>? ticketID,
      Value<String?>? templateID,
      Value<ProcedureDbModel?>? item,
      Value<bool>? isFlushed,
      Value<bool>? isDeleted,
      Value<bool>? isDownloaded,
      Value<ProcedureEntityStatus>? status,
      Value<int>? rowid}) {
    return ProcedureEntityCompanion(
      id: id ?? this.id,
      ticketID: ticketID ?? this.ticketID,
      templateID: templateID ?? this.templateID,
      item: item ?? this.item,
      isFlushed: isFlushed ?? this.isFlushed,
      isDeleted: isDeleted ?? this.isDeleted,
      isDownloaded: isDownloaded ?? this.isDownloaded,
      status: status ?? this.status,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<String>(id.value);
    }
    if (ticketID.present) {
      map['ticket_i_d'] = Variable<String>(ticketID.value);
    }
    if (templateID.present) {
      map['template_i_d'] = Variable<String>(templateID.value);
    }
    if (item.present) {
      map['item'] = Variable<String>(
          $ProcedureEntityTable.$converteritemn.toSql(item.value));
    }
    if (isFlushed.present) {
      map['is_flushed'] = Variable<bool>(isFlushed.value);
    }
    if (isDeleted.present) {
      map['is_deleted'] = Variable<bool>(isDeleted.value);
    }
    if (isDownloaded.present) {
      map['is_downloaded'] = Variable<bool>(isDownloaded.value);
    }
    if (status.present) {
      map['status'] = Variable<String>(
          $ProcedureEntityTable.$converterstatus.toSql(status.value));
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('ProcedureEntityCompanion(')
          ..write('id: $id, ')
          ..write('ticketID: $ticketID, ')
          ..write('templateID: $templateID, ')
          ..write('item: $item, ')
          ..write('isFlushed: $isFlushed, ')
          ..write('isDeleted: $isDeleted, ')
          ..write('isDownloaded: $isDownloaded, ')
          ..write('status: $status, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $SupportAccountEntityTable extends SupportAccountEntity
    with TableInfo<$SupportAccountEntityTable, SupportAccountEntityData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $SupportAccountEntityTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<String> id = GeneratedColumn<String>(
      'id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _itemMeta = const VerificationMeta('item');
  @override
  late final GeneratedColumnWithTypeConverter<SupportAccountDbModel?, String>
      item = GeneratedColumn<String>('item', aliasedName, true,
              type: DriftSqlType.string, requiredDuringInsert: false)
          .withConverter<SupportAccountDbModel?>(
              $SupportAccountEntityTable.$converteritemn);
  @override
  List<GeneratedColumn> get $columns => [id, item];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'support_account_entity';
  @override
  VerificationContext validateIntegrity(
      Insertable<SupportAccountEntityData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    } else if (isInserting) {
      context.missing(_idMeta);
    }
    context.handle(_itemMeta, const VerificationResult.success());
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  SupportAccountEntityData map(Map<String, dynamic> data,
      {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return SupportAccountEntityData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}id'])!,
      item: $SupportAccountEntityTable.$converteritemn.fromSql(attachedDatabase
          .typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}item'])),
    );
  }

  @override
  $SupportAccountEntityTable createAlias(String alias) {
    return $SupportAccountEntityTable(attachedDatabase, alias);
  }

  static TypeConverter<SupportAccountDbModel, String> $converteritem =
      const SupportAccountDbModelConvertor();
  static TypeConverter<SupportAccountDbModel?, String?> $converteritemn =
      NullAwareTypeConverter.wrap($converteritem);
}

class SupportAccountEntityData extends DataClass
    implements Insertable<SupportAccountEntityData> {
  final String id;
  final SupportAccountDbModel? item;
  const SupportAccountEntityData({required this.id, this.item});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<String>(id);
    if (!nullToAbsent || item != null) {
      map['item'] = Variable<String>(
          $SupportAccountEntityTable.$converteritemn.toSql(item));
    }
    return map;
  }

  SupportAccountEntityCompanion toCompanion(bool nullToAbsent) {
    return SupportAccountEntityCompanion(
      id: Value(id),
      item: item == null && nullToAbsent ? const Value.absent() : Value(item),
    );
  }

  factory SupportAccountEntityData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return SupportAccountEntityData(
      id: serializer.fromJson<String>(json['id']),
      item: serializer.fromJson<SupportAccountDbModel?>(json['item']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<String>(id),
      'item': serializer.toJson<SupportAccountDbModel?>(item),
    };
  }

  SupportAccountEntityData copyWith(
          {String? id,
          Value<SupportAccountDbModel?> item = const Value.absent()}) =>
      SupportAccountEntityData(
        id: id ?? this.id,
        item: item.present ? item.value : this.item,
      );
  SupportAccountEntityData copyWithCompanion(
      SupportAccountEntityCompanion data) {
    return SupportAccountEntityData(
      id: data.id.present ? data.id.value : this.id,
      item: data.item.present ? data.item.value : this.item,
    );
  }

  @override
  String toString() {
    return (StringBuffer('SupportAccountEntityData(')
          ..write('id: $id, ')
          ..write('item: $item')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, item);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is SupportAccountEntityData &&
          other.id == this.id &&
          other.item == this.item);
}

class SupportAccountEntityCompanion
    extends UpdateCompanion<SupportAccountEntityData> {
  final Value<String> id;
  final Value<SupportAccountDbModel?> item;
  final Value<int> rowid;
  const SupportAccountEntityCompanion({
    this.id = const Value.absent(),
    this.item = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  SupportAccountEntityCompanion.insert({
    required String id,
    this.item = const Value.absent(),
    this.rowid = const Value.absent(),
  }) : id = Value(id);
  static Insertable<SupportAccountEntityData> custom({
    Expression<String>? id,
    Expression<String>? item,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (item != null) 'item': item,
      if (rowid != null) 'rowid': rowid,
    });
  }

  SupportAccountEntityCompanion copyWith(
      {Value<String>? id,
      Value<SupportAccountDbModel?>? item,
      Value<int>? rowid}) {
    return SupportAccountEntityCompanion(
      id: id ?? this.id,
      item: item ?? this.item,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<String>(id.value);
    }
    if (item.present) {
      map['item'] = Variable<String>(
          $SupportAccountEntityTable.$converteritemn.toSql(item.value));
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('SupportAccountEntityCompanion(')
          ..write('id: $id, ')
          ..write('item: $item, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $OemDbEntityTable extends OemDbEntity
    with TableInfo<$OemDbEntityTable, OemDbEntityData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $OemDbEntityTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<String> id = GeneratedColumn<String>(
      'id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _itemMeta = const VerificationMeta('item');
  @override
  late final GeneratedColumnWithTypeConverter<OemEntity?, String> item =
      GeneratedColumn<String>('item', aliasedName, true,
              type: DriftSqlType.string, requiredDuringInsert: false)
          .withConverter<OemEntity?>($OemDbEntityTable.$converteritemn);
  static const VerificationMeta _workOrderTypesMeta =
      const VerificationMeta('workOrderTypes');
  @override
  late final GeneratedColumnWithTypeConverter<List<WorkOrderTypeEntity>?,
      String> workOrderTypes = GeneratedColumn<String>(
          'work_order_types', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false)
      .withConverter<List<WorkOrderTypeEntity>?>(
          $OemDbEntityTable.$converterworkOrderTypesn);
  static const VerificationMeta _statusesMeta =
      const VerificationMeta('statuses');
  @override
  late final GeneratedColumnWithTypeConverter<List<StatusEntity>?, String>
      statuses = GeneratedColumn<String>('statuses', aliasedName, true,
              type: DriftSqlType.string, requiredDuringInsert: false)
          .withConverter<List<StatusEntity>?>(
              $OemDbEntityTable.$converterstatusesn);
  @override
  List<GeneratedColumn> get $columns => [id, item, workOrderTypes, statuses];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'oem_db_entity';
  @override
  VerificationContext validateIntegrity(Insertable<OemDbEntityData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    } else if (isInserting) {
      context.missing(_idMeta);
    }
    context.handle(_itemMeta, const VerificationResult.success());
    context.handle(_workOrderTypesMeta, const VerificationResult.success());
    context.handle(_statusesMeta, const VerificationResult.success());
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  OemDbEntityData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return OemDbEntityData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}id'])!,
      item: $OemDbEntityTable.$converteritemn.fromSql(attachedDatabase
          .typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}item'])),
      workOrderTypes: $OemDbEntityTable.$converterworkOrderTypesn.fromSql(
          attachedDatabase.typeMapping.read(
              DriftSqlType.string, data['${effectivePrefix}work_order_types'])),
      statuses: $OemDbEntityTable.$converterstatusesn.fromSql(attachedDatabase
          .typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}statuses'])),
    );
  }

  @override
  $OemDbEntityTable createAlias(String alias) {
    return $OemDbEntityTable(attachedDatabase, alias);
  }

  static TypeConverter<OemEntity, String> $converteritem =
      const OemEntityDbModelConvertor();
  static TypeConverter<OemEntity?, String?> $converteritemn =
      NullAwareTypeConverter.wrap($converteritem);
  static TypeConverter<List<WorkOrderTypeEntity>, String>
      $converterworkOrderTypes = const WorkOrderTypeDBConvertor();
  static TypeConverter<List<WorkOrderTypeEntity>?, String?>
      $converterworkOrderTypesn =
      NullAwareTypeConverter.wrap($converterworkOrderTypes);
  static TypeConverter<List<StatusEntity>, String> $converterstatuses =
      const StatusDbModelConvertor();
  static TypeConverter<List<StatusEntity>?, String?> $converterstatusesn =
      NullAwareTypeConverter.wrap($converterstatuses);
}

class OemDbEntityData extends DataClass implements Insertable<OemDbEntityData> {
  final String id;
  final OemEntity? item;
  final List<WorkOrderTypeEntity>? workOrderTypes;
  final List<StatusEntity>? statuses;
  const OemDbEntityData(
      {required this.id, this.item, this.workOrderTypes, this.statuses});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<String>(id);
    if (!nullToAbsent || item != null) {
      map['item'] =
          Variable<String>($OemDbEntityTable.$converteritemn.toSql(item));
    }
    if (!nullToAbsent || workOrderTypes != null) {
      map['work_order_types'] = Variable<String>(
          $OemDbEntityTable.$converterworkOrderTypesn.toSql(workOrderTypes));
    }
    if (!nullToAbsent || statuses != null) {
      map['statuses'] = Variable<String>(
          $OemDbEntityTable.$converterstatusesn.toSql(statuses));
    }
    return map;
  }

  OemDbEntityCompanion toCompanion(bool nullToAbsent) {
    return OemDbEntityCompanion(
      id: Value(id),
      item: item == null && nullToAbsent ? const Value.absent() : Value(item),
      workOrderTypes: workOrderTypes == null && nullToAbsent
          ? const Value.absent()
          : Value(workOrderTypes),
      statuses: statuses == null && nullToAbsent
          ? const Value.absent()
          : Value(statuses),
    );
  }

  factory OemDbEntityData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return OemDbEntityData(
      id: serializer.fromJson<String>(json['id']),
      item: serializer.fromJson<OemEntity?>(json['item']),
      workOrderTypes: serializer
          .fromJson<List<WorkOrderTypeEntity>?>(json['workOrderTypes']),
      statuses: serializer.fromJson<List<StatusEntity>?>(json['statuses']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<String>(id),
      'item': serializer.toJson<OemEntity?>(item),
      'workOrderTypes':
          serializer.toJson<List<WorkOrderTypeEntity>?>(workOrderTypes),
      'statuses': serializer.toJson<List<StatusEntity>?>(statuses),
    };
  }

  OemDbEntityData copyWith(
          {String? id,
          Value<OemEntity?> item = const Value.absent(),
          Value<List<WorkOrderTypeEntity>?> workOrderTypes =
              const Value.absent(),
          Value<List<StatusEntity>?> statuses = const Value.absent()}) =>
      OemDbEntityData(
        id: id ?? this.id,
        item: item.present ? item.value : this.item,
        workOrderTypes:
            workOrderTypes.present ? workOrderTypes.value : this.workOrderTypes,
        statuses: statuses.present ? statuses.value : this.statuses,
      );
  OemDbEntityData copyWithCompanion(OemDbEntityCompanion data) {
    return OemDbEntityData(
      id: data.id.present ? data.id.value : this.id,
      item: data.item.present ? data.item.value : this.item,
      workOrderTypes: data.workOrderTypes.present
          ? data.workOrderTypes.value
          : this.workOrderTypes,
      statuses: data.statuses.present ? data.statuses.value : this.statuses,
    );
  }

  @override
  String toString() {
    return (StringBuffer('OemDbEntityData(')
          ..write('id: $id, ')
          ..write('item: $item, ')
          ..write('workOrderTypes: $workOrderTypes, ')
          ..write('statuses: $statuses')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, item, workOrderTypes, statuses);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is OemDbEntityData &&
          other.id == this.id &&
          other.item == this.item &&
          other.workOrderTypes == this.workOrderTypes &&
          other.statuses == this.statuses);
}

class OemDbEntityCompanion extends UpdateCompanion<OemDbEntityData> {
  final Value<String> id;
  final Value<OemEntity?> item;
  final Value<List<WorkOrderTypeEntity>?> workOrderTypes;
  final Value<List<StatusEntity>?> statuses;
  final Value<int> rowid;
  const OemDbEntityCompanion({
    this.id = const Value.absent(),
    this.item = const Value.absent(),
    this.workOrderTypes = const Value.absent(),
    this.statuses = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  OemDbEntityCompanion.insert({
    required String id,
    this.item = const Value.absent(),
    this.workOrderTypes = const Value.absent(),
    this.statuses = const Value.absent(),
    this.rowid = const Value.absent(),
  }) : id = Value(id);
  static Insertable<OemDbEntityData> custom({
    Expression<String>? id,
    Expression<String>? item,
    Expression<String>? workOrderTypes,
    Expression<String>? statuses,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (item != null) 'item': item,
      if (workOrderTypes != null) 'work_order_types': workOrderTypes,
      if (statuses != null) 'statuses': statuses,
      if (rowid != null) 'rowid': rowid,
    });
  }

  OemDbEntityCompanion copyWith(
      {Value<String>? id,
      Value<OemEntity?>? item,
      Value<List<WorkOrderTypeEntity>?>? workOrderTypes,
      Value<List<StatusEntity>?>? statuses,
      Value<int>? rowid}) {
    return OemDbEntityCompanion(
      id: id ?? this.id,
      item: item ?? this.item,
      workOrderTypes: workOrderTypes ?? this.workOrderTypes,
      statuses: statuses ?? this.statuses,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<String>(id.value);
    }
    if (item.present) {
      map['item'] =
          Variable<String>($OemDbEntityTable.$converteritemn.toSql(item.value));
    }
    if (workOrderTypes.present) {
      map['work_order_types'] = Variable<String>($OemDbEntityTable
          .$converterworkOrderTypesn
          .toSql(workOrderTypes.value));
    }
    if (statuses.present) {
      map['statuses'] = Variable<String>(
          $OemDbEntityTable.$converterstatusesn.toSql(statuses.value));
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('OemDbEntityCompanion(')
          ..write('id: $id, ')
          ..write('item: $item, ')
          ..write('workOrderTypes: $workOrderTypes, ')
          ..write('statuses: $statuses, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $TicketCountEntityTable extends TicketCountEntity
    with TableInfo<$TicketCountEntityTable, TicketCountEntityData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $TicketCountEntityTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _typeMeta = const VerificationMeta('type');
  @override
  late final GeneratedColumn<String> type = GeneratedColumn<String>(
      'type', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _countMeta = const VerificationMeta('count');
  @override
  late final GeneratedColumn<int> count = GeneratedColumn<int>(
      'count', aliasedName, false,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      defaultValue: const Constant(0));
  @override
  List<GeneratedColumn> get $columns => [type, count];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'ticket_count_entity';
  @override
  VerificationContext validateIntegrity(
      Insertable<TicketCountEntityData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('type')) {
      context.handle(
          _typeMeta, type.isAcceptableOrUnknown(data['type']!, _typeMeta));
    } else if (isInserting) {
      context.missing(_typeMeta);
    }
    if (data.containsKey('count')) {
      context.handle(
          _countMeta, count.isAcceptableOrUnknown(data['count']!, _countMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {type};
  @override
  TicketCountEntityData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return TicketCountEntityData(
      type: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}type'])!,
      count: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}count'])!,
    );
  }

  @override
  $TicketCountEntityTable createAlias(String alias) {
    return $TicketCountEntityTable(attachedDatabase, alias);
  }
}

class TicketCountEntityData extends DataClass
    implements Insertable<TicketCountEntityData> {
  final String type;
  final int count;
  const TicketCountEntityData({required this.type, required this.count});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['type'] = Variable<String>(type);
    map['count'] = Variable<int>(count);
    return map;
  }

  TicketCountEntityCompanion toCompanion(bool nullToAbsent) {
    return TicketCountEntityCompanion(
      type: Value(type),
      count: Value(count),
    );
  }

  factory TicketCountEntityData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return TicketCountEntityData(
      type: serializer.fromJson<String>(json['type']),
      count: serializer.fromJson<int>(json['count']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'type': serializer.toJson<String>(type),
      'count': serializer.toJson<int>(count),
    };
  }

  TicketCountEntityData copyWith({String? type, int? count}) =>
      TicketCountEntityData(
        type: type ?? this.type,
        count: count ?? this.count,
      );
  TicketCountEntityData copyWithCompanion(TicketCountEntityCompanion data) {
    return TicketCountEntityData(
      type: data.type.present ? data.type.value : this.type,
      count: data.count.present ? data.count.value : this.count,
    );
  }

  @override
  String toString() {
    return (StringBuffer('TicketCountEntityData(')
          ..write('type: $type, ')
          ..write('count: $count')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(type, count);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is TicketCountEntityData &&
          other.type == this.type &&
          other.count == this.count);
}

class TicketCountEntityCompanion
    extends UpdateCompanion<TicketCountEntityData> {
  final Value<String> type;
  final Value<int> count;
  final Value<int> rowid;
  const TicketCountEntityCompanion({
    this.type = const Value.absent(),
    this.count = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  TicketCountEntityCompanion.insert({
    required String type,
    this.count = const Value.absent(),
    this.rowid = const Value.absent(),
  }) : type = Value(type);
  static Insertable<TicketCountEntityData> custom({
    Expression<String>? type,
    Expression<int>? count,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (type != null) 'type': type,
      if (count != null) 'count': count,
      if (rowid != null) 'rowid': rowid,
    });
  }

  TicketCountEntityCompanion copyWith(
      {Value<String>? type, Value<int>? count, Value<int>? rowid}) {
    return TicketCountEntityCompanion(
      type: type ?? this.type,
      count: count ?? this.count,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (type.present) {
      map['type'] = Variable<String>(type.value);
    }
    if (count.present) {
      map['count'] = Variable<int>(count.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('TicketCountEntityCompanion(')
          ..write('type: $type, ')
          ..write('count: $count, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $WorkOrderPartsEntityTable extends WorkOrderPartsEntity
    with TableInfo<$WorkOrderPartsEntityTable, WorkOrderPartsEntityData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $WorkOrderPartsEntityTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _workOrderIdMeta =
      const VerificationMeta('workOrderId');
  @override
  late final GeneratedColumn<String> workOrderId = GeneratedColumn<String>(
      'work_order_id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _partIdMeta = const VerificationMeta('partId');
  @override
  late final GeneratedColumn<String> partId = GeneratedColumn<String>(
      'part_id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _valueMeta = const VerificationMeta('value');
  @override
  late final GeneratedColumnWithTypeConverter<WorkOrderPartDbModel?, String>
      value = GeneratedColumn<String>('value', aliasedName, true,
              type: DriftSqlType.string, requiredDuringInsert: false)
          .withConverter<WorkOrderPartDbModel?>(
              $WorkOrderPartsEntityTable.$convertervaluen);
  @override
  List<GeneratedColumn> get $columns => [workOrderId, partId, value];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'work_order_parts_entity';
  @override
  VerificationContext validateIntegrity(
      Insertable<WorkOrderPartsEntityData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('work_order_id')) {
      context.handle(
          _workOrderIdMeta,
          workOrderId.isAcceptableOrUnknown(
              data['work_order_id']!, _workOrderIdMeta));
    } else if (isInserting) {
      context.missing(_workOrderIdMeta);
    }
    if (data.containsKey('part_id')) {
      context.handle(_partIdMeta,
          partId.isAcceptableOrUnknown(data['part_id']!, _partIdMeta));
    } else if (isInserting) {
      context.missing(_partIdMeta);
    }
    context.handle(_valueMeta, const VerificationResult.success());
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {workOrderId, partId};
  @override
  WorkOrderPartsEntityData map(Map<String, dynamic> data,
      {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return WorkOrderPartsEntityData(
      workOrderId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}work_order_id'])!,
      partId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}part_id'])!,
      value: $WorkOrderPartsEntityTable.$convertervaluen.fromSql(
          attachedDatabase.typeMapping
              .read(DriftSqlType.string, data['${effectivePrefix}value'])),
    );
  }

  @override
  $WorkOrderPartsEntityTable createAlias(String alias) {
    return $WorkOrderPartsEntityTable(attachedDatabase, alias);
  }

  static TypeConverter<WorkOrderPartDbModel, String> $convertervalue =
      const WorkOrderPartsDbModelConvertor();
  static TypeConverter<WorkOrderPartDbModel?, String?> $convertervaluen =
      NullAwareTypeConverter.wrap($convertervalue);
}

class WorkOrderPartsEntityData extends DataClass
    implements Insertable<WorkOrderPartsEntityData> {
  final String workOrderId;
  final String partId;
  final WorkOrderPartDbModel? value;
  const WorkOrderPartsEntityData(
      {required this.workOrderId, required this.partId, this.value});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['work_order_id'] = Variable<String>(workOrderId);
    map['part_id'] = Variable<String>(partId);
    if (!nullToAbsent || value != null) {
      map['value'] = Variable<String>(
          $WorkOrderPartsEntityTable.$convertervaluen.toSql(value));
    }
    return map;
  }

  WorkOrderPartsEntityCompanion toCompanion(bool nullToAbsent) {
    return WorkOrderPartsEntityCompanion(
      workOrderId: Value(workOrderId),
      partId: Value(partId),
      value:
          value == null && nullToAbsent ? const Value.absent() : Value(value),
    );
  }

  factory WorkOrderPartsEntityData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return WorkOrderPartsEntityData(
      workOrderId: serializer.fromJson<String>(json['workOrderId']),
      partId: serializer.fromJson<String>(json['partId']),
      value: serializer.fromJson<WorkOrderPartDbModel?>(json['value']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'workOrderId': serializer.toJson<String>(workOrderId),
      'partId': serializer.toJson<String>(partId),
      'value': serializer.toJson<WorkOrderPartDbModel?>(value),
    };
  }

  WorkOrderPartsEntityData copyWith(
          {String? workOrderId,
          String? partId,
          Value<WorkOrderPartDbModel?> value = const Value.absent()}) =>
      WorkOrderPartsEntityData(
        workOrderId: workOrderId ?? this.workOrderId,
        partId: partId ?? this.partId,
        value: value.present ? value.value : this.value,
      );
  WorkOrderPartsEntityData copyWithCompanion(
      WorkOrderPartsEntityCompanion data) {
    return WorkOrderPartsEntityData(
      workOrderId:
          data.workOrderId.present ? data.workOrderId.value : this.workOrderId,
      partId: data.partId.present ? data.partId.value : this.partId,
      value: data.value.present ? data.value.value : this.value,
    );
  }

  @override
  String toString() {
    return (StringBuffer('WorkOrderPartsEntityData(')
          ..write('workOrderId: $workOrderId, ')
          ..write('partId: $partId, ')
          ..write('value: $value')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(workOrderId, partId, value);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is WorkOrderPartsEntityData &&
          other.workOrderId == this.workOrderId &&
          other.partId == this.partId &&
          other.value == this.value);
}

class WorkOrderPartsEntityCompanion
    extends UpdateCompanion<WorkOrderPartsEntityData> {
  final Value<String> workOrderId;
  final Value<String> partId;
  final Value<WorkOrderPartDbModel?> value;
  final Value<int> rowid;
  const WorkOrderPartsEntityCompanion({
    this.workOrderId = const Value.absent(),
    this.partId = const Value.absent(),
    this.value = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  WorkOrderPartsEntityCompanion.insert({
    required String workOrderId,
    required String partId,
    this.value = const Value.absent(),
    this.rowid = const Value.absent(),
  })  : workOrderId = Value(workOrderId),
        partId = Value(partId);
  static Insertable<WorkOrderPartsEntityData> custom({
    Expression<String>? workOrderId,
    Expression<String>? partId,
    Expression<String>? value,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (workOrderId != null) 'work_order_id': workOrderId,
      if (partId != null) 'part_id': partId,
      if (value != null) 'value': value,
      if (rowid != null) 'rowid': rowid,
    });
  }

  WorkOrderPartsEntityCompanion copyWith(
      {Value<String>? workOrderId,
      Value<String>? partId,
      Value<WorkOrderPartDbModel?>? value,
      Value<int>? rowid}) {
    return WorkOrderPartsEntityCompanion(
      workOrderId: workOrderId ?? this.workOrderId,
      partId: partId ?? this.partId,
      value: value ?? this.value,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (workOrderId.present) {
      map['work_order_id'] = Variable<String>(workOrderId.value);
    }
    if (partId.present) {
      map['part_id'] = Variable<String>(partId.value);
    }
    if (value.present) {
      map['value'] = Variable<String>(
          $WorkOrderPartsEntityTable.$convertervaluen.toSql(value.value));
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('WorkOrderPartsEntityCompanion(')
          ..write('workOrderId: $workOrderId, ')
          ..write('partId: $partId, ')
          ..write('value: $value, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $TimeLogEntityTable extends TimeLogEntity
    with TableInfo<$TimeLogEntityTable, TimeLogEntityData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $TimeLogEntityTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<String> id = GeneratedColumn<String>(
      'id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _workOrderIdMeta =
      const VerificationMeta('workOrderId');
  @override
  late final GeneratedColumn<String> workOrderId = GeneratedColumn<String>(
      'work_order_id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _itemMeta = const VerificationMeta('item');
  @override
  late final GeneratedColumnWithTypeConverter<TimeLogDbModel?, String> item =
      GeneratedColumn<String>('item', aliasedName, true,
              type: DriftSqlType.string, requiredDuringInsert: false)
          .withConverter<TimeLogDbModel?>($TimeLogEntityTable.$converteritemn);
  @override
  List<GeneratedColumn> get $columns => [id, workOrderId, item];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'time_log_entity';
  @override
  VerificationContext validateIntegrity(Insertable<TimeLogEntityData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    } else if (isInserting) {
      context.missing(_idMeta);
    }
    if (data.containsKey('work_order_id')) {
      context.handle(
          _workOrderIdMeta,
          workOrderId.isAcceptableOrUnknown(
              data['work_order_id']!, _workOrderIdMeta));
    } else if (isInserting) {
      context.missing(_workOrderIdMeta);
    }
    context.handle(_itemMeta, const VerificationResult.success());
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  TimeLogEntityData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return TimeLogEntityData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}id'])!,
      workOrderId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}work_order_id'])!,
      item: $TimeLogEntityTable.$converteritemn.fromSql(attachedDatabase
          .typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}item'])),
    );
  }

  @override
  $TimeLogEntityTable createAlias(String alias) {
    return $TimeLogEntityTable(attachedDatabase, alias);
  }

  static TypeConverter<TimeLogDbModel, String> $converteritem =
      const TimeLogDbModelConvertor();
  static TypeConverter<TimeLogDbModel?, String?> $converteritemn =
      NullAwareTypeConverter.wrap($converteritem);
}

class TimeLogEntityData extends DataClass
    implements Insertable<TimeLogEntityData> {
  final String id;
  final String workOrderId;
  final TimeLogDbModel? item;
  const TimeLogEntityData(
      {required this.id, required this.workOrderId, this.item});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<String>(id);
    map['work_order_id'] = Variable<String>(workOrderId);
    if (!nullToAbsent || item != null) {
      map['item'] =
          Variable<String>($TimeLogEntityTable.$converteritemn.toSql(item));
    }
    return map;
  }

  TimeLogEntityCompanion toCompanion(bool nullToAbsent) {
    return TimeLogEntityCompanion(
      id: Value(id),
      workOrderId: Value(workOrderId),
      item: item == null && nullToAbsent ? const Value.absent() : Value(item),
    );
  }

  factory TimeLogEntityData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return TimeLogEntityData(
      id: serializer.fromJson<String>(json['id']),
      workOrderId: serializer.fromJson<String>(json['workOrderId']),
      item: serializer.fromJson<TimeLogDbModel?>(json['item']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<String>(id),
      'workOrderId': serializer.toJson<String>(workOrderId),
      'item': serializer.toJson<TimeLogDbModel?>(item),
    };
  }

  TimeLogEntityData copyWith(
          {String? id,
          String? workOrderId,
          Value<TimeLogDbModel?> item = const Value.absent()}) =>
      TimeLogEntityData(
        id: id ?? this.id,
        workOrderId: workOrderId ?? this.workOrderId,
        item: item.present ? item.value : this.item,
      );
  TimeLogEntityData copyWithCompanion(TimeLogEntityCompanion data) {
    return TimeLogEntityData(
      id: data.id.present ? data.id.value : this.id,
      workOrderId:
          data.workOrderId.present ? data.workOrderId.value : this.workOrderId,
      item: data.item.present ? data.item.value : this.item,
    );
  }

  @override
  String toString() {
    return (StringBuffer('TimeLogEntityData(')
          ..write('id: $id, ')
          ..write('workOrderId: $workOrderId, ')
          ..write('item: $item')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, workOrderId, item);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is TimeLogEntityData &&
          other.id == this.id &&
          other.workOrderId == this.workOrderId &&
          other.item == this.item);
}

class TimeLogEntityCompanion extends UpdateCompanion<TimeLogEntityData> {
  final Value<String> id;
  final Value<String> workOrderId;
  final Value<TimeLogDbModel?> item;
  final Value<int> rowid;
  const TimeLogEntityCompanion({
    this.id = const Value.absent(),
    this.workOrderId = const Value.absent(),
    this.item = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  TimeLogEntityCompanion.insert({
    required String id,
    required String workOrderId,
    this.item = const Value.absent(),
    this.rowid = const Value.absent(),
  })  : id = Value(id),
        workOrderId = Value(workOrderId);
  static Insertable<TimeLogEntityData> custom({
    Expression<String>? id,
    Expression<String>? workOrderId,
    Expression<String>? item,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (workOrderId != null) 'work_order_id': workOrderId,
      if (item != null) 'item': item,
      if (rowid != null) 'rowid': rowid,
    });
  }

  TimeLogEntityCompanion copyWith(
      {Value<String>? id,
      Value<String>? workOrderId,
      Value<TimeLogDbModel?>? item,
      Value<int>? rowid}) {
    return TimeLogEntityCompanion(
      id: id ?? this.id,
      workOrderId: workOrderId ?? this.workOrderId,
      item: item ?? this.item,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<String>(id.value);
    }
    if (workOrderId.present) {
      map['work_order_id'] = Variable<String>(workOrderId.value);
    }
    if (item.present) {
      map['item'] = Variable<String>(
          $TimeLogEntityTable.$converteritemn.toSql(item.value));
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('TimeLogEntityCompanion(')
          ..write('id: $id, ')
          ..write('workOrderId: $workOrderId, ')
          ..write('item: $item, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $AiNoteTableTable extends AiNoteTable
    with TableInfo<$AiNoteTableTable, AiNoteTableData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $AiNoteTableTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<String> id = GeneratedColumn<String>(
      'id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _noteMeta = const VerificationMeta('note');
  @override
  late final GeneratedColumnWithTypeConverter<AiNoteEntity?, String> note =
      GeneratedColumn<String>('note', aliasedName, true,
              type: DriftSqlType.string, requiredDuringInsert: false)
          .withConverter<AiNoteEntity?>($AiNoteTableTable.$converternoten);
  @override
  List<GeneratedColumn> get $columns => [id, note];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'ai_note_table';
  @override
  VerificationContext validateIntegrity(Insertable<AiNoteTableData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    } else if (isInserting) {
      context.missing(_idMeta);
    }
    context.handle(_noteMeta, const VerificationResult.success());
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  AiNoteTableData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return AiNoteTableData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}id'])!,
      note: $AiNoteTableTable.$converternoten.fromSql(attachedDatabase
          .typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}note'])),
    );
  }

  @override
  $AiNoteTableTable createAlias(String alias) {
    return $AiNoteTableTable(attachedDatabase, alias);
  }

  static TypeConverter<AiNoteEntity, String> $converternote =
      AiNoteEntityConverter();
  static TypeConverter<AiNoteEntity?, String?> $converternoten =
      NullAwareTypeConverter.wrap($converternote);
}

class AiNoteTableData extends DataClass implements Insertable<AiNoteTableData> {
  final String id;
  final AiNoteEntity? note;
  const AiNoteTableData({required this.id, this.note});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<String>(id);
    if (!nullToAbsent || note != null) {
      map['note'] =
          Variable<String>($AiNoteTableTable.$converternoten.toSql(note));
    }
    return map;
  }

  AiNoteTableCompanion toCompanion(bool nullToAbsent) {
    return AiNoteTableCompanion(
      id: Value(id),
      note: note == null && nullToAbsent ? const Value.absent() : Value(note),
    );
  }

  factory AiNoteTableData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return AiNoteTableData(
      id: serializer.fromJson<String>(json['id']),
      note: serializer.fromJson<AiNoteEntity?>(json['note']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<String>(id),
      'note': serializer.toJson<AiNoteEntity?>(note),
    };
  }

  AiNoteTableData copyWith(
          {String? id, Value<AiNoteEntity?> note = const Value.absent()}) =>
      AiNoteTableData(
        id: id ?? this.id,
        note: note.present ? note.value : this.note,
      );
  AiNoteTableData copyWithCompanion(AiNoteTableCompanion data) {
    return AiNoteTableData(
      id: data.id.present ? data.id.value : this.id,
      note: data.note.present ? data.note.value : this.note,
    );
  }

  @override
  String toString() {
    return (StringBuffer('AiNoteTableData(')
          ..write('id: $id, ')
          ..write('note: $note')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, note);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is AiNoteTableData &&
          other.id == this.id &&
          other.note == this.note);
}

class AiNoteTableCompanion extends UpdateCompanion<AiNoteTableData> {
  final Value<String> id;
  final Value<AiNoteEntity?> note;
  final Value<int> rowid;
  const AiNoteTableCompanion({
    this.id = const Value.absent(),
    this.note = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  AiNoteTableCompanion.insert({
    required String id,
    this.note = const Value.absent(),
    this.rowid = const Value.absent(),
  }) : id = Value(id);
  static Insertable<AiNoteTableData> custom({
    Expression<String>? id,
    Expression<String>? note,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (note != null) 'note': note,
      if (rowid != null) 'rowid': rowid,
    });
  }

  AiNoteTableCompanion copyWith(
      {Value<String>? id, Value<AiNoteEntity?>? note, Value<int>? rowid}) {
    return AiNoteTableCompanion(
      id: id ?? this.id,
      note: note ?? this.note,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<String>(id.value);
    }
    if (note.present) {
      map['note'] =
          Variable<String>($AiNoteTableTable.$converternoten.toSql(note.value));
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('AiNoteTableCompanion(')
          ..write('id: $id, ')
          ..write('note: $note, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $ProcedureSubAssetEntityTable extends ProcedureSubAssetEntity
    with TableInfo<$ProcedureSubAssetEntityTable, ProcedureSubAssetEntityData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $ProcedureSubAssetEntityTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<String> id = GeneratedColumn<String>(
      'id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _parentAssetIdMeta =
      const VerificationMeta('parentAssetId');
  @override
  late final GeneratedColumn<String> parentAssetId = GeneratedColumn<String>(
      'parent_asset_id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _itemMeta = const VerificationMeta('item');
  @override
  late final GeneratedColumnWithTypeConverter<ProcedureSubAssetDbModel?, String>
      item = GeneratedColumn<String>('item', aliasedName, true,
              type: DriftSqlType.string, requiredDuringInsert: false)
          .withConverter<ProcedureSubAssetDbModel?>(
              $ProcedureSubAssetEntityTable.$converteritemn);
  @override
  List<GeneratedColumn> get $columns => [id, parentAssetId, item];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'procedure_sub_asset_entity';
  @override
  VerificationContext validateIntegrity(
      Insertable<ProcedureSubAssetEntityData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    } else if (isInserting) {
      context.missing(_idMeta);
    }
    if (data.containsKey('parent_asset_id')) {
      context.handle(
          _parentAssetIdMeta,
          parentAssetId.isAcceptableOrUnknown(
              data['parent_asset_id']!, _parentAssetIdMeta));
    } else if (isInserting) {
      context.missing(_parentAssetIdMeta);
    }
    context.handle(_itemMeta, const VerificationResult.success());
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id, parentAssetId};
  @override
  ProcedureSubAssetEntityData map(Map<String, dynamic> data,
      {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return ProcedureSubAssetEntityData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}id'])!,
      parentAssetId: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}parent_asset_id'])!,
      item: $ProcedureSubAssetEntityTable.$converteritemn.fromSql(
          attachedDatabase.typeMapping
              .read(DriftSqlType.string, data['${effectivePrefix}item'])),
    );
  }

  @override
  $ProcedureSubAssetEntityTable createAlias(String alias) {
    return $ProcedureSubAssetEntityTable(attachedDatabase, alias);
  }

  static TypeConverter<ProcedureSubAssetDbModel, String> $converteritem =
      const ProcedureSubAssetDbModelConvertor();
  static TypeConverter<ProcedureSubAssetDbModel?, String?> $converteritemn =
      NullAwareTypeConverter.wrap($converteritem);
}

class ProcedureSubAssetEntityData extends DataClass
    implements Insertable<ProcedureSubAssetEntityData> {
  final String id;
  final String parentAssetId;
  final ProcedureSubAssetDbModel? item;
  const ProcedureSubAssetEntityData(
      {required this.id, required this.parentAssetId, this.item});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<String>(id);
    map['parent_asset_id'] = Variable<String>(parentAssetId);
    if (!nullToAbsent || item != null) {
      map['item'] = Variable<String>(
          $ProcedureSubAssetEntityTable.$converteritemn.toSql(item));
    }
    return map;
  }

  ProcedureSubAssetEntityCompanion toCompanion(bool nullToAbsent) {
    return ProcedureSubAssetEntityCompanion(
      id: Value(id),
      parentAssetId: Value(parentAssetId),
      item: item == null && nullToAbsent ? const Value.absent() : Value(item),
    );
  }

  factory ProcedureSubAssetEntityData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return ProcedureSubAssetEntityData(
      id: serializer.fromJson<String>(json['id']),
      parentAssetId: serializer.fromJson<String>(json['parentAssetId']),
      item: serializer.fromJson<ProcedureSubAssetDbModel?>(json['item']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<String>(id),
      'parentAssetId': serializer.toJson<String>(parentAssetId),
      'item': serializer.toJson<ProcedureSubAssetDbModel?>(item),
    };
  }

  ProcedureSubAssetEntityData copyWith(
          {String? id,
          String? parentAssetId,
          Value<ProcedureSubAssetDbModel?> item = const Value.absent()}) =>
      ProcedureSubAssetEntityData(
        id: id ?? this.id,
        parentAssetId: parentAssetId ?? this.parentAssetId,
        item: item.present ? item.value : this.item,
      );
  ProcedureSubAssetEntityData copyWithCompanion(
      ProcedureSubAssetEntityCompanion data) {
    return ProcedureSubAssetEntityData(
      id: data.id.present ? data.id.value : this.id,
      parentAssetId: data.parentAssetId.present
          ? data.parentAssetId.value
          : this.parentAssetId,
      item: data.item.present ? data.item.value : this.item,
    );
  }

  @override
  String toString() {
    return (StringBuffer('ProcedureSubAssetEntityData(')
          ..write('id: $id, ')
          ..write('parentAssetId: $parentAssetId, ')
          ..write('item: $item')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, parentAssetId, item);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is ProcedureSubAssetEntityData &&
          other.id == this.id &&
          other.parentAssetId == this.parentAssetId &&
          other.item == this.item);
}

class ProcedureSubAssetEntityCompanion
    extends UpdateCompanion<ProcedureSubAssetEntityData> {
  final Value<String> id;
  final Value<String> parentAssetId;
  final Value<ProcedureSubAssetDbModel?> item;
  final Value<int> rowid;
  const ProcedureSubAssetEntityCompanion({
    this.id = const Value.absent(),
    this.parentAssetId = const Value.absent(),
    this.item = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  ProcedureSubAssetEntityCompanion.insert({
    required String id,
    required String parentAssetId,
    this.item = const Value.absent(),
    this.rowid = const Value.absent(),
  })  : id = Value(id),
        parentAssetId = Value(parentAssetId);
  static Insertable<ProcedureSubAssetEntityData> custom({
    Expression<String>? id,
    Expression<String>? parentAssetId,
    Expression<String>? item,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (parentAssetId != null) 'parent_asset_id': parentAssetId,
      if (item != null) 'item': item,
      if (rowid != null) 'rowid': rowid,
    });
  }

  ProcedureSubAssetEntityCompanion copyWith(
      {Value<String>? id,
      Value<String>? parentAssetId,
      Value<ProcedureSubAssetDbModel?>? item,
      Value<int>? rowid}) {
    return ProcedureSubAssetEntityCompanion(
      id: id ?? this.id,
      parentAssetId: parentAssetId ?? this.parentAssetId,
      item: item ?? this.item,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<String>(id.value);
    }
    if (parentAssetId.present) {
      map['parent_asset_id'] = Variable<String>(parentAssetId.value);
    }
    if (item.present) {
      map['item'] = Variable<String>(
          $ProcedureSubAssetEntityTable.$converteritemn.toSql(item.value));
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('ProcedureSubAssetEntityCompanion(')
          ..write('id: $id, ')
          ..write('parentAssetId: $parentAssetId, ')
          ..write('item: $item, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $CustomerRequestTableTable extends CustomerRequestTable
    with TableInfo<$CustomerRequestTableTable, CustomerRequestTableData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $CustomerRequestTableTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<String> id = GeneratedColumn<String>(
      'id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _itemMeta = const VerificationMeta('item');
  @override
  late final GeneratedColumnWithTypeConverter<CustomerRequestEntity?, String>
      item = GeneratedColumn<String>('item', aliasedName, true,
              type: DriftSqlType.string, requiredDuringInsert: false)
          .withConverter<CustomerRequestEntity?>(
              $CustomerRequestTableTable.$converteritemn);
  @override
  List<GeneratedColumn> get $columns => [id, item];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'customer_request_table';
  @override
  VerificationContext validateIntegrity(
      Insertable<CustomerRequestTableData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    } else if (isInserting) {
      context.missing(_idMeta);
    }
    context.handle(_itemMeta, const VerificationResult.success());
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  CustomerRequestTableData map(Map<String, dynamic> data,
      {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return CustomerRequestTableData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}id'])!,
      item: $CustomerRequestTableTable.$converteritemn.fromSql(attachedDatabase
          .typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}item'])),
    );
  }

  @override
  $CustomerRequestTableTable createAlias(String alias) {
    return $CustomerRequestTableTable(attachedDatabase, alias);
  }

  static TypeConverter<CustomerRequestEntity, String> $converteritem =
      const CustomerRequestEntityConvertor();
  static TypeConverter<CustomerRequestEntity?, String?> $converteritemn =
      NullAwareTypeConverter.wrap($converteritem);
}

class CustomerRequestTableData extends DataClass
    implements Insertable<CustomerRequestTableData> {
  final String id;
  final CustomerRequestEntity? item;
  const CustomerRequestTableData({required this.id, this.item});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<String>(id);
    if (!nullToAbsent || item != null) {
      map['item'] = Variable<String>(
          $CustomerRequestTableTable.$converteritemn.toSql(item));
    }
    return map;
  }

  CustomerRequestTableCompanion toCompanion(bool nullToAbsent) {
    return CustomerRequestTableCompanion(
      id: Value(id),
      item: item == null && nullToAbsent ? const Value.absent() : Value(item),
    );
  }

  factory CustomerRequestTableData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return CustomerRequestTableData(
      id: serializer.fromJson<String>(json['id']),
      item: serializer.fromJson<CustomerRequestEntity?>(json['item']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<String>(id),
      'item': serializer.toJson<CustomerRequestEntity?>(item),
    };
  }

  CustomerRequestTableData copyWith(
          {String? id,
          Value<CustomerRequestEntity?> item = const Value.absent()}) =>
      CustomerRequestTableData(
        id: id ?? this.id,
        item: item.present ? item.value : this.item,
      );
  CustomerRequestTableData copyWithCompanion(
      CustomerRequestTableCompanion data) {
    return CustomerRequestTableData(
      id: data.id.present ? data.id.value : this.id,
      item: data.item.present ? data.item.value : this.item,
    );
  }

  @override
  String toString() {
    return (StringBuffer('CustomerRequestTableData(')
          ..write('id: $id, ')
          ..write('item: $item')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, item);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is CustomerRequestTableData &&
          other.id == this.id &&
          other.item == this.item);
}

class CustomerRequestTableCompanion
    extends UpdateCompanion<CustomerRequestTableData> {
  final Value<String> id;
  final Value<CustomerRequestEntity?> item;
  final Value<int> rowid;
  const CustomerRequestTableCompanion({
    this.id = const Value.absent(),
    this.item = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  CustomerRequestTableCompanion.insert({
    required String id,
    this.item = const Value.absent(),
    this.rowid = const Value.absent(),
  }) : id = Value(id);
  static Insertable<CustomerRequestTableData> custom({
    Expression<String>? id,
    Expression<String>? item,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (item != null) 'item': item,
      if (rowid != null) 'rowid': rowid,
    });
  }

  CustomerRequestTableCompanion copyWith(
      {Value<String>? id,
      Value<CustomerRequestEntity?>? item,
      Value<int>? rowid}) {
    return CustomerRequestTableCompanion(
      id: id ?? this.id,
      item: item ?? this.item,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<String>(id.value);
    }
    if (item.present) {
      map['item'] = Variable<String>(
          $CustomerRequestTableTable.$converteritemn.toSql(item.value));
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('CustomerRequestTableCompanion(')
          ..write('id: $id, ')
          ..write('item: $item, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $CustomerRequestListTableTable extends CustomerRequestListTable
    with
        TableInfo<$CustomerRequestListTableTable,
            CustomerRequestListTableData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $CustomerRequestListTableTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<String> id = GeneratedColumn<String>(
      'id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _typeMeta = const VerificationMeta('type');
  @override
  late final GeneratedColumn<String> type = GeneratedColumn<String>(
      'type', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  @override
  List<GeneratedColumn> get $columns => [id, type];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'customer_request_list_table';
  @override
  VerificationContext validateIntegrity(
      Insertable<CustomerRequestListTableData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    } else if (isInserting) {
      context.missing(_idMeta);
    }
    if (data.containsKey('type')) {
      context.handle(
          _typeMeta, type.isAcceptableOrUnknown(data['type']!, _typeMeta));
    } else if (isInserting) {
      context.missing(_typeMeta);
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id, type};
  @override
  CustomerRequestListTableData map(Map<String, dynamic> data,
      {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return CustomerRequestListTableData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}id'])!,
      type: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}type'])!,
    );
  }

  @override
  $CustomerRequestListTableTable createAlias(String alias) {
    return $CustomerRequestListTableTable(attachedDatabase, alias);
  }
}

class CustomerRequestListTableData extends DataClass
    implements Insertable<CustomerRequestListTableData> {
  final String id;
  final String type;
  const CustomerRequestListTableData({required this.id, required this.type});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<String>(id);
    map['type'] = Variable<String>(type);
    return map;
  }

  CustomerRequestListTableCompanion toCompanion(bool nullToAbsent) {
    return CustomerRequestListTableCompanion(
      id: Value(id),
      type: Value(type),
    );
  }

  factory CustomerRequestListTableData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return CustomerRequestListTableData(
      id: serializer.fromJson<String>(json['id']),
      type: serializer.fromJson<String>(json['type']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<String>(id),
      'type': serializer.toJson<String>(type),
    };
  }

  CustomerRequestListTableData copyWith({String? id, String? type}) =>
      CustomerRequestListTableData(
        id: id ?? this.id,
        type: type ?? this.type,
      );
  CustomerRequestListTableData copyWithCompanion(
      CustomerRequestListTableCompanion data) {
    return CustomerRequestListTableData(
      id: data.id.present ? data.id.value : this.id,
      type: data.type.present ? data.type.value : this.type,
    );
  }

  @override
  String toString() {
    return (StringBuffer('CustomerRequestListTableData(')
          ..write('id: $id, ')
          ..write('type: $type')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, type);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is CustomerRequestListTableData &&
          other.id == this.id &&
          other.type == this.type);
}

class CustomerRequestListTableCompanion
    extends UpdateCompanion<CustomerRequestListTableData> {
  final Value<String> id;
  final Value<String> type;
  final Value<int> rowid;
  const CustomerRequestListTableCompanion({
    this.id = const Value.absent(),
    this.type = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  CustomerRequestListTableCompanion.insert({
    required String id,
    required String type,
    this.rowid = const Value.absent(),
  })  : id = Value(id),
        type = Value(type);
  static Insertable<CustomerRequestListTableData> custom({
    Expression<String>? id,
    Expression<String>? type,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (type != null) 'type': type,
      if (rowid != null) 'rowid': rowid,
    });
  }

  CustomerRequestListTableCompanion copyWith(
      {Value<String>? id, Value<String>? type, Value<int>? rowid}) {
    return CustomerRequestListTableCompanion(
      id: id ?? this.id,
      type: type ?? this.type,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<String>(id.value);
    }
    if (type.present) {
      map['type'] = Variable<String>(type.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('CustomerRequestListTableCompanion(')
          ..write('id: $id, ')
          ..write('type: $type, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $CustomerRequestCountTableTable extends CustomerRequestCountTable
    with
        TableInfo<$CustomerRequestCountTableTable,
            CustomerRequestCountTableData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $CustomerRequestCountTableTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _typeMeta = const VerificationMeta('type');
  @override
  late final GeneratedColumn<String> type = GeneratedColumn<String>(
      'type', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _countMeta = const VerificationMeta('count');
  @override
  late final GeneratedColumn<int> count = GeneratedColumn<int>(
      'count', aliasedName, false,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      defaultValue: const Constant(0));
  @override
  List<GeneratedColumn> get $columns => [type, count];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'customer_request_count_table';
  @override
  VerificationContext validateIntegrity(
      Insertable<CustomerRequestCountTableData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('type')) {
      context.handle(
          _typeMeta, type.isAcceptableOrUnknown(data['type']!, _typeMeta));
    } else if (isInserting) {
      context.missing(_typeMeta);
    }
    if (data.containsKey('count')) {
      context.handle(
          _countMeta, count.isAcceptableOrUnknown(data['count']!, _countMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {type};
  @override
  CustomerRequestCountTableData map(Map<String, dynamic> data,
      {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return CustomerRequestCountTableData(
      type: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}type'])!,
      count: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}count'])!,
    );
  }

  @override
  $CustomerRequestCountTableTable createAlias(String alias) {
    return $CustomerRequestCountTableTable(attachedDatabase, alias);
  }
}

class CustomerRequestCountTableData extends DataClass
    implements Insertable<CustomerRequestCountTableData> {
  final String type;
  final int count;
  const CustomerRequestCountTableData(
      {required this.type, required this.count});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['type'] = Variable<String>(type);
    map['count'] = Variable<int>(count);
    return map;
  }

  CustomerRequestCountTableCompanion toCompanion(bool nullToAbsent) {
    return CustomerRequestCountTableCompanion(
      type: Value(type),
      count: Value(count),
    );
  }

  factory CustomerRequestCountTableData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return CustomerRequestCountTableData(
      type: serializer.fromJson<String>(json['type']),
      count: serializer.fromJson<int>(json['count']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'type': serializer.toJson<String>(type),
      'count': serializer.toJson<int>(count),
    };
  }

  CustomerRequestCountTableData copyWith({String? type, int? count}) =>
      CustomerRequestCountTableData(
        type: type ?? this.type,
        count: count ?? this.count,
      );
  CustomerRequestCountTableData copyWithCompanion(
      CustomerRequestCountTableCompanion data) {
    return CustomerRequestCountTableData(
      type: data.type.present ? data.type.value : this.type,
      count: data.count.present ? data.count.value : this.count,
    );
  }

  @override
  String toString() {
    return (StringBuffer('CustomerRequestCountTableData(')
          ..write('type: $type, ')
          ..write('count: $count')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(type, count);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is CustomerRequestCountTableData &&
          other.type == this.type &&
          other.count == this.count);
}

class CustomerRequestCountTableCompanion
    extends UpdateCompanion<CustomerRequestCountTableData> {
  final Value<String> type;
  final Value<int> count;
  final Value<int> rowid;
  const CustomerRequestCountTableCompanion({
    this.type = const Value.absent(),
    this.count = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  CustomerRequestCountTableCompanion.insert({
    required String type,
    this.count = const Value.absent(),
    this.rowid = const Value.absent(),
  }) : type = Value(type);
  static Insertable<CustomerRequestCountTableData> custom({
    Expression<String>? type,
    Expression<int>? count,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (type != null) 'type': type,
      if (count != null) 'count': count,
      if (rowid != null) 'rowid': rowid,
    });
  }

  CustomerRequestCountTableCompanion copyWith(
      {Value<String>? type, Value<int>? count, Value<int>? rowid}) {
    return CustomerRequestCountTableCompanion(
      type: type ?? this.type,
      count: count ?? this.count,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (type.present) {
      map['type'] = Variable<String>(type.value);
    }
    if (count.present) {
      map['count'] = Variable<int>(count.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('CustomerRequestCountTableCompanion(')
          ..write('type: $type, ')
          ..write('count: $count, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $AiAssistantTableTable extends AiAssistantTable
    with TableInfo<$AiAssistantTableTable, AiAssistantTableData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $AiAssistantTableTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<String> id = GeneratedColumn<String>(
      'id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _typeMeta = const VerificationMeta('type');
  @override
  late final GeneratedColumn<String> type = GeneratedColumn<String>(
      'type', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _assistantMeta =
      const VerificationMeta('assistant');
  @override
  late final GeneratedColumnWithTypeConverter<AiAssistantEntity?, String>
      assistant = GeneratedColumn<String>('assistant', aliasedName, true,
              type: DriftSqlType.string, requiredDuringInsert: false)
          .withConverter<AiAssistantEntity?>(
              $AiAssistantTableTable.$converterassistantn);
  @override
  List<GeneratedColumn> get $columns => [id, type, assistant];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'ai_assistant_table';
  @override
  VerificationContext validateIntegrity(
      Insertable<AiAssistantTableData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    } else if (isInserting) {
      context.missing(_idMeta);
    }
    if (data.containsKey('type')) {
      context.handle(
          _typeMeta, type.isAcceptableOrUnknown(data['type']!, _typeMeta));
    } else if (isInserting) {
      context.missing(_typeMeta);
    }
    context.handle(_assistantMeta, const VerificationResult.success());
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  AiAssistantTableData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return AiAssistantTableData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}id'])!,
      type: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}type'])!,
      assistant: $AiAssistantTableTable.$converterassistantn.fromSql(
          attachedDatabase.typeMapping
              .read(DriftSqlType.string, data['${effectivePrefix}assistant'])),
    );
  }

  @override
  $AiAssistantTableTable createAlias(String alias) {
    return $AiAssistantTableTable(attachedDatabase, alias);
  }

  static TypeConverter<AiAssistantEntity, String> $converterassistant =
      AiAssistantEntityConverter();
  static TypeConverter<AiAssistantEntity?, String?> $converterassistantn =
      NullAwareTypeConverter.wrap($converterassistant);
}

class AiAssistantTableData extends DataClass
    implements Insertable<AiAssistantTableData> {
  final String id;
  final String type;
  final AiAssistantEntity? assistant;
  const AiAssistantTableData(
      {required this.id, required this.type, this.assistant});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<String>(id);
    map['type'] = Variable<String>(type);
    if (!nullToAbsent || assistant != null) {
      map['assistant'] = Variable<String>(
          $AiAssistantTableTable.$converterassistantn.toSql(assistant));
    }
    return map;
  }

  AiAssistantTableCompanion toCompanion(bool nullToAbsent) {
    return AiAssistantTableCompanion(
      id: Value(id),
      type: Value(type),
      assistant: assistant == null && nullToAbsent
          ? const Value.absent()
          : Value(assistant),
    );
  }

  factory AiAssistantTableData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return AiAssistantTableData(
      id: serializer.fromJson<String>(json['id']),
      type: serializer.fromJson<String>(json['type']),
      assistant: serializer.fromJson<AiAssistantEntity?>(json['assistant']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<String>(id),
      'type': serializer.toJson<String>(type),
      'assistant': serializer.toJson<AiAssistantEntity?>(assistant),
    };
  }

  AiAssistantTableData copyWith(
          {String? id,
          String? type,
          Value<AiAssistantEntity?> assistant = const Value.absent()}) =>
      AiAssistantTableData(
        id: id ?? this.id,
        type: type ?? this.type,
        assistant: assistant.present ? assistant.value : this.assistant,
      );
  AiAssistantTableData copyWithCompanion(AiAssistantTableCompanion data) {
    return AiAssistantTableData(
      id: data.id.present ? data.id.value : this.id,
      type: data.type.present ? data.type.value : this.type,
      assistant: data.assistant.present ? data.assistant.value : this.assistant,
    );
  }

  @override
  String toString() {
    return (StringBuffer('AiAssistantTableData(')
          ..write('id: $id, ')
          ..write('type: $type, ')
          ..write('assistant: $assistant')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, type, assistant);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is AiAssistantTableData &&
          other.id == this.id &&
          other.type == this.type &&
          other.assistant == this.assistant);
}

class AiAssistantTableCompanion extends UpdateCompanion<AiAssistantTableData> {
  final Value<String> id;
  final Value<String> type;
  final Value<AiAssistantEntity?> assistant;
  final Value<int> rowid;
  const AiAssistantTableCompanion({
    this.id = const Value.absent(),
    this.type = const Value.absent(),
    this.assistant = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  AiAssistantTableCompanion.insert({
    required String id,
    required String type,
    this.assistant = const Value.absent(),
    this.rowid = const Value.absent(),
  })  : id = Value(id),
        type = Value(type);
  static Insertable<AiAssistantTableData> custom({
    Expression<String>? id,
    Expression<String>? type,
    Expression<String>? assistant,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (type != null) 'type': type,
      if (assistant != null) 'assistant': assistant,
      if (rowid != null) 'rowid': rowid,
    });
  }

  AiAssistantTableCompanion copyWith(
      {Value<String>? id,
      Value<String>? type,
      Value<AiAssistantEntity?>? assistant,
      Value<int>? rowid}) {
    return AiAssistantTableCompanion(
      id: id ?? this.id,
      type: type ?? this.type,
      assistant: assistant ?? this.assistant,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<String>(id.value);
    }
    if (type.present) {
      map['type'] = Variable<String>(type.value);
    }
    if (assistant.present) {
      map['assistant'] = Variable<String>(
          $AiAssistantTableTable.$converterassistantn.toSql(assistant.value));
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('AiAssistantTableCompanion(')
          ..write('id: $id, ')
          ..write('type: $type, ')
          ..write('assistant: $assistant, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $ConnectionTableTable extends ConnectionTable
    with TableInfo<$ConnectionTableTable, ConnectionTableData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $ConnectionTableTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<String> id = GeneratedColumn<String>(
      'id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _lastAssetSyncMeta =
      const VerificationMeta('lastAssetSync');
  @override
  late final GeneratedColumn<DateTime> lastAssetSync =
      GeneratedColumn<DateTime>('last_asset_sync', aliasedName, true,
          type: DriftSqlType.dateTime, requiredDuringInsert: false);
  static const VerificationMeta _connectionMeta =
      const VerificationMeta('connection');
  @override
  late final GeneratedColumnWithTypeConverter<ConnectionEntity?, String>
      connection = GeneratedColumn<String>('connection', aliasedName, true,
              type: DriftSqlType.string, requiredDuringInsert: false)
          .withConverter<ConnectionEntity?>(
              $ConnectionTableTable.$converterconnectionn);
  @override
  List<GeneratedColumn> get $columns => [id, lastAssetSync, connection];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'connection_table';
  @override
  VerificationContext validateIntegrity(
      Insertable<ConnectionTableData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    } else if (isInserting) {
      context.missing(_idMeta);
    }
    if (data.containsKey('last_asset_sync')) {
      context.handle(
          _lastAssetSyncMeta,
          lastAssetSync.isAcceptableOrUnknown(
              data['last_asset_sync']!, _lastAssetSyncMeta));
    }
    context.handle(_connectionMeta, const VerificationResult.success());
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  ConnectionTableData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return ConnectionTableData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}id'])!,
      lastAssetSync: attachedDatabase.typeMapping.read(
          DriftSqlType.dateTime, data['${effectivePrefix}last_asset_sync']),
      connection: $ConnectionTableTable.$converterconnectionn.fromSql(
          attachedDatabase.typeMapping
              .read(DriftSqlType.string, data['${effectivePrefix}connection'])),
    );
  }

  @override
  $ConnectionTableTable createAlias(String alias) {
    return $ConnectionTableTable(attachedDatabase, alias);
  }

  static TypeConverter<ConnectionEntity, String> $converterconnection =
      ConnectionEntityConverter();
  static TypeConverter<ConnectionEntity?, String?> $converterconnectionn =
      NullAwareTypeConverter.wrap($converterconnection);
}

class ConnectionTableData extends DataClass
    implements Insertable<ConnectionTableData> {
  final String id;
  final DateTime? lastAssetSync;
  final ConnectionEntity? connection;
  const ConnectionTableData(
      {required this.id, this.lastAssetSync, this.connection});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<String>(id);
    if (!nullToAbsent || lastAssetSync != null) {
      map['last_asset_sync'] = Variable<DateTime>(lastAssetSync);
    }
    if (!nullToAbsent || connection != null) {
      map['connection'] = Variable<String>(
          $ConnectionTableTable.$converterconnectionn.toSql(connection));
    }
    return map;
  }

  ConnectionTableCompanion toCompanion(bool nullToAbsent) {
    return ConnectionTableCompanion(
      id: Value(id),
      lastAssetSync: lastAssetSync == null && nullToAbsent
          ? const Value.absent()
          : Value(lastAssetSync),
      connection: connection == null && nullToAbsent
          ? const Value.absent()
          : Value(connection),
    );
  }

  factory ConnectionTableData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return ConnectionTableData(
      id: serializer.fromJson<String>(json['id']),
      lastAssetSync: serializer.fromJson<DateTime?>(json['lastAssetSync']),
      connection: serializer.fromJson<ConnectionEntity?>(json['connection']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<String>(id),
      'lastAssetSync': serializer.toJson<DateTime?>(lastAssetSync),
      'connection': serializer.toJson<ConnectionEntity?>(connection),
    };
  }

  ConnectionTableData copyWith(
          {String? id,
          Value<DateTime?> lastAssetSync = const Value.absent(),
          Value<ConnectionEntity?> connection = const Value.absent()}) =>
      ConnectionTableData(
        id: id ?? this.id,
        lastAssetSync:
            lastAssetSync.present ? lastAssetSync.value : this.lastAssetSync,
        connection: connection.present ? connection.value : this.connection,
      );
  ConnectionTableData copyWithCompanion(ConnectionTableCompanion data) {
    return ConnectionTableData(
      id: data.id.present ? data.id.value : this.id,
      lastAssetSync: data.lastAssetSync.present
          ? data.lastAssetSync.value
          : this.lastAssetSync,
      connection:
          data.connection.present ? data.connection.value : this.connection,
    );
  }

  @override
  String toString() {
    return (StringBuffer('ConnectionTableData(')
          ..write('id: $id, ')
          ..write('lastAssetSync: $lastAssetSync, ')
          ..write('connection: $connection')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, lastAssetSync, connection);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is ConnectionTableData &&
          other.id == this.id &&
          other.lastAssetSync == this.lastAssetSync &&
          other.connection == this.connection);
}

class ConnectionTableCompanion extends UpdateCompanion<ConnectionTableData> {
  final Value<String> id;
  final Value<DateTime?> lastAssetSync;
  final Value<ConnectionEntity?> connection;
  final Value<int> rowid;
  const ConnectionTableCompanion({
    this.id = const Value.absent(),
    this.lastAssetSync = const Value.absent(),
    this.connection = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  ConnectionTableCompanion.insert({
    required String id,
    this.lastAssetSync = const Value.absent(),
    this.connection = const Value.absent(),
    this.rowid = const Value.absent(),
  }) : id = Value(id);
  static Insertable<ConnectionTableData> custom({
    Expression<String>? id,
    Expression<DateTime>? lastAssetSync,
    Expression<String>? connection,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (lastAssetSync != null) 'last_asset_sync': lastAssetSync,
      if (connection != null) 'connection': connection,
      if (rowid != null) 'rowid': rowid,
    });
  }

  ConnectionTableCompanion copyWith(
      {Value<String>? id,
      Value<DateTime?>? lastAssetSync,
      Value<ConnectionEntity?>? connection,
      Value<int>? rowid}) {
    return ConnectionTableCompanion(
      id: id ?? this.id,
      lastAssetSync: lastAssetSync ?? this.lastAssetSync,
      connection: connection ?? this.connection,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<String>(id.value);
    }
    if (lastAssetSync.present) {
      map['last_asset_sync'] = Variable<DateTime>(lastAssetSync.value);
    }
    if (connection.present) {
      map['connection'] = Variable<String>(
          $ConnectionTableTable.$converterconnectionn.toSql(connection.value));
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('ConnectionTableCompanion(')
          ..write('id: $id, ')
          ..write('lastAssetSync: $lastAssetSync, ')
          ..write('connection: $connection, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $CustomFieldsTableTable extends CustomFieldsTable
    with TableInfo<$CustomFieldsTableTable, CustomFieldsTableData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $CustomFieldsTableTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _typeMeta = const VerificationMeta('type');
  @override
  late final GeneratedColumn<String> type = GeneratedColumn<String>(
      'type', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _valueMeta = const VerificationMeta('value');
  @override
  late final GeneratedColumnWithTypeConverter<List<CustomFieldEntity>?, String>
      value = GeneratedColumn<String>('value', aliasedName, true,
              type: DriftSqlType.string, requiredDuringInsert: false)
          .withConverter<List<CustomFieldEntity>?>(
              $CustomFieldsTableTable.$convertervaluen);
  @override
  List<GeneratedColumn> get $columns => [type, value];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'custom_fields_table';
  @override
  VerificationContext validateIntegrity(
      Insertable<CustomFieldsTableData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('type')) {
      context.handle(
          _typeMeta, type.isAcceptableOrUnknown(data['type']!, _typeMeta));
    } else if (isInserting) {
      context.missing(_typeMeta);
    }
    context.handle(_valueMeta, const VerificationResult.success());
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {type};
  @override
  CustomFieldsTableData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return CustomFieldsTableData(
      type: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}type'])!,
      value: $CustomFieldsTableTable.$convertervaluen.fromSql(attachedDatabase
          .typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}value'])),
    );
  }

  @override
  $CustomFieldsTableTable createAlias(String alias) {
    return $CustomFieldsTableTable(attachedDatabase, alias);
  }

  static TypeConverter<List<CustomFieldEntity>, String> $convertervalue =
      const CustomFieldEntityConvertor();
  static TypeConverter<List<CustomFieldEntity>?, String?> $convertervaluen =
      NullAwareTypeConverter.wrap($convertervalue);
}

class CustomFieldsTableData extends DataClass
    implements Insertable<CustomFieldsTableData> {
  final String type;
  final List<CustomFieldEntity>? value;
  const CustomFieldsTableData({required this.type, this.value});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['type'] = Variable<String>(type);
    if (!nullToAbsent || value != null) {
      map['value'] = Variable<String>(
          $CustomFieldsTableTable.$convertervaluen.toSql(value));
    }
    return map;
  }

  CustomFieldsTableCompanion toCompanion(bool nullToAbsent) {
    return CustomFieldsTableCompanion(
      type: Value(type),
      value:
          value == null && nullToAbsent ? const Value.absent() : Value(value),
    );
  }

  factory CustomFieldsTableData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return CustomFieldsTableData(
      type: serializer.fromJson<String>(json['type']),
      value: serializer.fromJson<List<CustomFieldEntity>?>(json['value']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'type': serializer.toJson<String>(type),
      'value': serializer.toJson<List<CustomFieldEntity>?>(value),
    };
  }

  CustomFieldsTableData copyWith(
          {String? type,
          Value<List<CustomFieldEntity>?> value = const Value.absent()}) =>
      CustomFieldsTableData(
        type: type ?? this.type,
        value: value.present ? value.value : this.value,
      );
  CustomFieldsTableData copyWithCompanion(CustomFieldsTableCompanion data) {
    return CustomFieldsTableData(
      type: data.type.present ? data.type.value : this.type,
      value: data.value.present ? data.value.value : this.value,
    );
  }

  @override
  String toString() {
    return (StringBuffer('CustomFieldsTableData(')
          ..write('type: $type, ')
          ..write('value: $value')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(type, value);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is CustomFieldsTableData &&
          other.type == this.type &&
          other.value == this.value);
}

class CustomFieldsTableCompanion
    extends UpdateCompanion<CustomFieldsTableData> {
  final Value<String> type;
  final Value<List<CustomFieldEntity>?> value;
  final Value<int> rowid;
  const CustomFieldsTableCompanion({
    this.type = const Value.absent(),
    this.value = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  CustomFieldsTableCompanion.insert({
    required String type,
    this.value = const Value.absent(),
    this.rowid = const Value.absent(),
  }) : type = Value(type);
  static Insertable<CustomFieldsTableData> custom({
    Expression<String>? type,
    Expression<String>? value,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (type != null) 'type': type,
      if (value != null) 'value': value,
      if (rowid != null) 'rowid': rowid,
    });
  }

  CustomFieldsTableCompanion copyWith(
      {Value<String>? type,
      Value<List<CustomFieldEntity>?>? value,
      Value<int>? rowid}) {
    return CustomFieldsTableCompanion(
      type: type ?? this.type,
      value: value ?? this.value,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (type.present) {
      map['type'] = Variable<String>(type.value);
    }
    if (value.present) {
      map['value'] = Variable<String>(
          $CustomFieldsTableTable.$convertervaluen.toSql(value.value));
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('CustomFieldsTableCompanion(')
          ..write('type: $type, ')
          ..write('value: $value, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $CustomFieldValuesTableTable extends CustomFieldValuesTable
    with TableInfo<$CustomFieldValuesTableTable, CustomFieldValuesTableData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $CustomFieldValuesTableTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _resourceIdMeta =
      const VerificationMeta('resourceId');
  @override
  late final GeneratedColumn<String> resourceId = GeneratedColumn<String>(
      'resource_id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _typeMeta = const VerificationMeta('type');
  @override
  late final GeneratedColumn<String> type = GeneratedColumn<String>(
      'type', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _isSharedMeta =
      const VerificationMeta('isShared');
  @override
  late final GeneratedColumn<bool> isShared = GeneratedColumn<bool>(
      'is_shared', aliasedName, false,
      type: DriftSqlType.bool,
      requiredDuringInsert: true,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('CHECK ("is_shared" IN (0, 1))'));
  static const VerificationMeta _valueMeta = const VerificationMeta('value');
  @override
  late final GeneratedColumnWithTypeConverter<List<CustomFieldValueEntity>?,
      String> value = GeneratedColumn<String>('value', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false)
      .withConverter<List<CustomFieldValueEntity>?>(
          $CustomFieldValuesTableTable.$convertervaluen);
  @override
  List<GeneratedColumn> get $columns => [resourceId, type, isShared, value];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'custom_field_values_table';
  @override
  VerificationContext validateIntegrity(
      Insertable<CustomFieldValuesTableData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('resource_id')) {
      context.handle(
          _resourceIdMeta,
          resourceId.isAcceptableOrUnknown(
              data['resource_id']!, _resourceIdMeta));
    } else if (isInserting) {
      context.missing(_resourceIdMeta);
    }
    if (data.containsKey('type')) {
      context.handle(
          _typeMeta, type.isAcceptableOrUnknown(data['type']!, _typeMeta));
    } else if (isInserting) {
      context.missing(_typeMeta);
    }
    if (data.containsKey('is_shared')) {
      context.handle(_isSharedMeta,
          isShared.isAcceptableOrUnknown(data['is_shared']!, _isSharedMeta));
    } else if (isInserting) {
      context.missing(_isSharedMeta);
    }
    context.handle(_valueMeta, const VerificationResult.success());
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {resourceId, type, isShared};
  @override
  CustomFieldValuesTableData map(Map<String, dynamic> data,
      {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return CustomFieldValuesTableData(
      resourceId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}resource_id'])!,
      type: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}type'])!,
      isShared: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}is_shared'])!,
      value: $CustomFieldValuesTableTable.$convertervaluen.fromSql(
          attachedDatabase.typeMapping
              .read(DriftSqlType.string, data['${effectivePrefix}value'])),
    );
  }

  @override
  $CustomFieldValuesTableTable createAlias(String alias) {
    return $CustomFieldValuesTableTable(attachedDatabase, alias);
  }

  static TypeConverter<List<CustomFieldValueEntity>, String> $convertervalue =
      const CustomFieldValueEntityConvertor();
  static TypeConverter<List<CustomFieldValueEntity>?, String?>
      $convertervaluen = NullAwareTypeConverter.wrap($convertervalue);
}

class CustomFieldValuesTableData extends DataClass
    implements Insertable<CustomFieldValuesTableData> {
  final String resourceId;
  final String type;
  final bool isShared;
  final List<CustomFieldValueEntity>? value;
  const CustomFieldValuesTableData(
      {required this.resourceId,
      required this.type,
      required this.isShared,
      this.value});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['resource_id'] = Variable<String>(resourceId);
    map['type'] = Variable<String>(type);
    map['is_shared'] = Variable<bool>(isShared);
    if (!nullToAbsent || value != null) {
      map['value'] = Variable<String>(
          $CustomFieldValuesTableTable.$convertervaluen.toSql(value));
    }
    return map;
  }

  CustomFieldValuesTableCompanion toCompanion(bool nullToAbsent) {
    return CustomFieldValuesTableCompanion(
      resourceId: Value(resourceId),
      type: Value(type),
      isShared: Value(isShared),
      value:
          value == null && nullToAbsent ? const Value.absent() : Value(value),
    );
  }

  factory CustomFieldValuesTableData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return CustomFieldValuesTableData(
      resourceId: serializer.fromJson<String>(json['resourceId']),
      type: serializer.fromJson<String>(json['type']),
      isShared: serializer.fromJson<bool>(json['isShared']),
      value: serializer.fromJson<List<CustomFieldValueEntity>?>(json['value']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'resourceId': serializer.toJson<String>(resourceId),
      'type': serializer.toJson<String>(type),
      'isShared': serializer.toJson<bool>(isShared),
      'value': serializer.toJson<List<CustomFieldValueEntity>?>(value),
    };
  }

  CustomFieldValuesTableData copyWith(
          {String? resourceId,
          String? type,
          bool? isShared,
          Value<List<CustomFieldValueEntity>?> value = const Value.absent()}) =>
      CustomFieldValuesTableData(
        resourceId: resourceId ?? this.resourceId,
        type: type ?? this.type,
        isShared: isShared ?? this.isShared,
        value: value.present ? value.value : this.value,
      );
  CustomFieldValuesTableData copyWithCompanion(
      CustomFieldValuesTableCompanion data) {
    return CustomFieldValuesTableData(
      resourceId:
          data.resourceId.present ? data.resourceId.value : this.resourceId,
      type: data.type.present ? data.type.value : this.type,
      isShared: data.isShared.present ? data.isShared.value : this.isShared,
      value: data.value.present ? data.value.value : this.value,
    );
  }

  @override
  String toString() {
    return (StringBuffer('CustomFieldValuesTableData(')
          ..write('resourceId: $resourceId, ')
          ..write('type: $type, ')
          ..write('isShared: $isShared, ')
          ..write('value: $value')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(resourceId, type, isShared, value);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is CustomFieldValuesTableData &&
          other.resourceId == this.resourceId &&
          other.type == this.type &&
          other.isShared == this.isShared &&
          other.value == this.value);
}

class CustomFieldValuesTableCompanion
    extends UpdateCompanion<CustomFieldValuesTableData> {
  final Value<String> resourceId;
  final Value<String> type;
  final Value<bool> isShared;
  final Value<List<CustomFieldValueEntity>?> value;
  final Value<int> rowid;
  const CustomFieldValuesTableCompanion({
    this.resourceId = const Value.absent(),
    this.type = const Value.absent(),
    this.isShared = const Value.absent(),
    this.value = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  CustomFieldValuesTableCompanion.insert({
    required String resourceId,
    required String type,
    required bool isShared,
    this.value = const Value.absent(),
    this.rowid = const Value.absent(),
  })  : resourceId = Value(resourceId),
        type = Value(type),
        isShared = Value(isShared);
  static Insertable<CustomFieldValuesTableData> custom({
    Expression<String>? resourceId,
    Expression<String>? type,
    Expression<bool>? isShared,
    Expression<String>? value,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (resourceId != null) 'resource_id': resourceId,
      if (type != null) 'type': type,
      if (isShared != null) 'is_shared': isShared,
      if (value != null) 'value': value,
      if (rowid != null) 'rowid': rowid,
    });
  }

  CustomFieldValuesTableCompanion copyWith(
      {Value<String>? resourceId,
      Value<String>? type,
      Value<bool>? isShared,
      Value<List<CustomFieldValueEntity>?>? value,
      Value<int>? rowid}) {
    return CustomFieldValuesTableCompanion(
      resourceId: resourceId ?? this.resourceId,
      type: type ?? this.type,
      isShared: isShared ?? this.isShared,
      value: value ?? this.value,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (resourceId.present) {
      map['resource_id'] = Variable<String>(resourceId.value);
    }
    if (type.present) {
      map['type'] = Variable<String>(type.value);
    }
    if (isShared.present) {
      map['is_shared'] = Variable<bool>(isShared.value);
    }
    if (value.present) {
      map['value'] = Variable<String>(
          $CustomFieldValuesTableTable.$convertervaluen.toSql(value.value));
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('CustomFieldValuesTableCompanion(')
          ..write('resourceId: $resourceId, ')
          ..write('type: $type, ')
          ..write('isShared: $isShared, ')
          ..write('value: $value, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $AssetTableTable extends AssetTable
    with TableInfo<$AssetTableTable, AssetTableData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $AssetTableTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<String> id = GeneratedColumn<String>(
      'id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _connectionIdMeta =
      const VerificationMeta('connectionId');
  @override
  late final GeneratedColumn<String> connectionId = GeneratedColumn<String>(
      'connection_id', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _parentAssetIdMeta =
      const VerificationMeta('parentAssetId');
  @override
  late final GeneratedColumn<String> parentAssetId = GeneratedColumn<String>(
      'parent_asset_id', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _isSharedAssetMeta =
      const VerificationMeta('isSharedAsset');
  @override
  late final GeneratedColumn<bool> isSharedAsset = GeneratedColumn<bool>(
      'is_shared_asset', aliasedName, false,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      defaultConstraints: GeneratedColumn.constraintIsAlways(
          'CHECK ("is_shared_asset" IN (0, 1))'),
      defaultValue: const Constant(false));
  static const VerificationMeta _lastSyncMeta =
      const VerificationMeta('lastSync');
  @override
  late final GeneratedColumnWithTypeConverter<AssetLastSyncEntity?, String>
      lastSync = GeneratedColumn<String>('last_sync', aliasedName, true,
              type: DriftSqlType.string, requiredDuringInsert: false)
          .withConverter<AssetLastSyncEntity?>(
              $AssetTableTable.$converterlastSyncn);
  static const VerificationMeta _assetMeta = const VerificationMeta('asset');
  @override
  late final GeneratedColumnWithTypeConverter<AssetDetailsEntity?, String>
      asset = GeneratedColumn<String>('asset', aliasedName, true,
              type: DriftSqlType.string, requiredDuringInsert: false)
          .withConverter<AssetDetailsEntity?>(
              $AssetTableTable.$converterassetn);
  @override
  List<GeneratedColumn> get $columns =>
      [id, connectionId, parentAssetId, isSharedAsset, lastSync, asset];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'asset_table';
  @override
  VerificationContext validateIntegrity(Insertable<AssetTableData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    } else if (isInserting) {
      context.missing(_idMeta);
    }
    if (data.containsKey('connection_id')) {
      context.handle(
          _connectionIdMeta,
          connectionId.isAcceptableOrUnknown(
              data['connection_id']!, _connectionIdMeta));
    }
    if (data.containsKey('parent_asset_id')) {
      context.handle(
          _parentAssetIdMeta,
          parentAssetId.isAcceptableOrUnknown(
              data['parent_asset_id']!, _parentAssetIdMeta));
    }
    if (data.containsKey('is_shared_asset')) {
      context.handle(
          _isSharedAssetMeta,
          isSharedAsset.isAcceptableOrUnknown(
              data['is_shared_asset']!, _isSharedAssetMeta));
    }
    context.handle(_lastSyncMeta, const VerificationResult.success());
    context.handle(_assetMeta, const VerificationResult.success());
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  AssetTableData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return AssetTableData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}id'])!,
      connectionId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}connection_id']),
      parentAssetId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}parent_asset_id']),
      isSharedAsset: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}is_shared_asset'])!,
      lastSync: $AssetTableTable.$converterlastSyncn.fromSql(attachedDatabase
          .typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}last_sync'])),
      asset: $AssetTableTable.$converterassetn.fromSql(attachedDatabase
          .typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}asset'])),
    );
  }

  @override
  $AssetTableTable createAlias(String alias) {
    return $AssetTableTable(attachedDatabase, alias);
  }

  static TypeConverter<AssetLastSyncEntity, String> $converterlastSync =
      AssetLastSyncEntityConverter();
  static TypeConverter<AssetLastSyncEntity?, String?> $converterlastSyncn =
      NullAwareTypeConverter.wrap($converterlastSync);
  static TypeConverter<AssetDetailsEntity, String> $converterasset =
      AssetDetailsEntityConverter();
  static TypeConverter<AssetDetailsEntity?, String?> $converterassetn =
      NullAwareTypeConverter.wrap($converterasset);
}

class AssetTableData extends DataClass implements Insertable<AssetTableData> {
  final String id;
  final String? connectionId;
  final String? parentAssetId;
  final bool isSharedAsset;
  final AssetLastSyncEntity? lastSync;
  final AssetDetailsEntity? asset;
  const AssetTableData(
      {required this.id,
      this.connectionId,
      this.parentAssetId,
      required this.isSharedAsset,
      this.lastSync,
      this.asset});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<String>(id);
    if (!nullToAbsent || connectionId != null) {
      map['connection_id'] = Variable<String>(connectionId);
    }
    if (!nullToAbsent || parentAssetId != null) {
      map['parent_asset_id'] = Variable<String>(parentAssetId);
    }
    map['is_shared_asset'] = Variable<bool>(isSharedAsset);
    if (!nullToAbsent || lastSync != null) {
      map['last_sync'] = Variable<String>(
          $AssetTableTable.$converterlastSyncn.toSql(lastSync));
    }
    if (!nullToAbsent || asset != null) {
      map['asset'] =
          Variable<String>($AssetTableTable.$converterassetn.toSql(asset));
    }
    return map;
  }

  AssetTableCompanion toCompanion(bool nullToAbsent) {
    return AssetTableCompanion(
      id: Value(id),
      connectionId: connectionId == null && nullToAbsent
          ? const Value.absent()
          : Value(connectionId),
      parentAssetId: parentAssetId == null && nullToAbsent
          ? const Value.absent()
          : Value(parentAssetId),
      isSharedAsset: Value(isSharedAsset),
      lastSync: lastSync == null && nullToAbsent
          ? const Value.absent()
          : Value(lastSync),
      asset:
          asset == null && nullToAbsent ? const Value.absent() : Value(asset),
    );
  }

  factory AssetTableData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return AssetTableData(
      id: serializer.fromJson<String>(json['id']),
      connectionId: serializer.fromJson<String?>(json['connectionId']),
      parentAssetId: serializer.fromJson<String?>(json['parentAssetId']),
      isSharedAsset: serializer.fromJson<bool>(json['isSharedAsset']),
      lastSync: serializer.fromJson<AssetLastSyncEntity?>(json['lastSync']),
      asset: serializer.fromJson<AssetDetailsEntity?>(json['asset']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<String>(id),
      'connectionId': serializer.toJson<String?>(connectionId),
      'parentAssetId': serializer.toJson<String?>(parentAssetId),
      'isSharedAsset': serializer.toJson<bool>(isSharedAsset),
      'lastSync': serializer.toJson<AssetLastSyncEntity?>(lastSync),
      'asset': serializer.toJson<AssetDetailsEntity?>(asset),
    };
  }

  AssetTableData copyWith(
          {String? id,
          Value<String?> connectionId = const Value.absent(),
          Value<String?> parentAssetId = const Value.absent(),
          bool? isSharedAsset,
          Value<AssetLastSyncEntity?> lastSync = const Value.absent(),
          Value<AssetDetailsEntity?> asset = const Value.absent()}) =>
      AssetTableData(
        id: id ?? this.id,
        connectionId:
            connectionId.present ? connectionId.value : this.connectionId,
        parentAssetId:
            parentAssetId.present ? parentAssetId.value : this.parentAssetId,
        isSharedAsset: isSharedAsset ?? this.isSharedAsset,
        lastSync: lastSync.present ? lastSync.value : this.lastSync,
        asset: asset.present ? asset.value : this.asset,
      );
  AssetTableData copyWithCompanion(AssetTableCompanion data) {
    return AssetTableData(
      id: data.id.present ? data.id.value : this.id,
      connectionId: data.connectionId.present
          ? data.connectionId.value
          : this.connectionId,
      parentAssetId: data.parentAssetId.present
          ? data.parentAssetId.value
          : this.parentAssetId,
      isSharedAsset: data.isSharedAsset.present
          ? data.isSharedAsset.value
          : this.isSharedAsset,
      lastSync: data.lastSync.present ? data.lastSync.value : this.lastSync,
      asset: data.asset.present ? data.asset.value : this.asset,
    );
  }

  @override
  String toString() {
    return (StringBuffer('AssetTableData(')
          ..write('id: $id, ')
          ..write('connectionId: $connectionId, ')
          ..write('parentAssetId: $parentAssetId, ')
          ..write('isSharedAsset: $isSharedAsset, ')
          ..write('lastSync: $lastSync, ')
          ..write('asset: $asset')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(
      id, connectionId, parentAssetId, isSharedAsset, lastSync, asset);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is AssetTableData &&
          other.id == this.id &&
          other.connectionId == this.connectionId &&
          other.parentAssetId == this.parentAssetId &&
          other.isSharedAsset == this.isSharedAsset &&
          other.lastSync == this.lastSync &&
          other.asset == this.asset);
}

class AssetTableCompanion extends UpdateCompanion<AssetTableData> {
  final Value<String> id;
  final Value<String?> connectionId;
  final Value<String?> parentAssetId;
  final Value<bool> isSharedAsset;
  final Value<AssetLastSyncEntity?> lastSync;
  final Value<AssetDetailsEntity?> asset;
  final Value<int> rowid;
  const AssetTableCompanion({
    this.id = const Value.absent(),
    this.connectionId = const Value.absent(),
    this.parentAssetId = const Value.absent(),
    this.isSharedAsset = const Value.absent(),
    this.lastSync = const Value.absent(),
    this.asset = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  AssetTableCompanion.insert({
    required String id,
    this.connectionId = const Value.absent(),
    this.parentAssetId = const Value.absent(),
    this.isSharedAsset = const Value.absent(),
    this.lastSync = const Value.absent(),
    this.asset = const Value.absent(),
    this.rowid = const Value.absent(),
  }) : id = Value(id);
  static Insertable<AssetTableData> custom({
    Expression<String>? id,
    Expression<String>? connectionId,
    Expression<String>? parentAssetId,
    Expression<bool>? isSharedAsset,
    Expression<String>? lastSync,
    Expression<String>? asset,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (connectionId != null) 'connection_id': connectionId,
      if (parentAssetId != null) 'parent_asset_id': parentAssetId,
      if (isSharedAsset != null) 'is_shared_asset': isSharedAsset,
      if (lastSync != null) 'last_sync': lastSync,
      if (asset != null) 'asset': asset,
      if (rowid != null) 'rowid': rowid,
    });
  }

  AssetTableCompanion copyWith(
      {Value<String>? id,
      Value<String?>? connectionId,
      Value<String?>? parentAssetId,
      Value<bool>? isSharedAsset,
      Value<AssetLastSyncEntity?>? lastSync,
      Value<AssetDetailsEntity?>? asset,
      Value<int>? rowid}) {
    return AssetTableCompanion(
      id: id ?? this.id,
      connectionId: connectionId ?? this.connectionId,
      parentAssetId: parentAssetId ?? this.parentAssetId,
      isSharedAsset: isSharedAsset ?? this.isSharedAsset,
      lastSync: lastSync ?? this.lastSync,
      asset: asset ?? this.asset,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<String>(id.value);
    }
    if (connectionId.present) {
      map['connection_id'] = Variable<String>(connectionId.value);
    }
    if (parentAssetId.present) {
      map['parent_asset_id'] = Variable<String>(parentAssetId.value);
    }
    if (isSharedAsset.present) {
      map['is_shared_asset'] = Variable<bool>(isSharedAsset.value);
    }
    if (lastSync.present) {
      map['last_sync'] = Variable<String>(
          $AssetTableTable.$converterlastSyncn.toSql(lastSync.value));
    }
    if (asset.present) {
      map['asset'] = Variable<String>(
          $AssetTableTable.$converterassetn.toSql(asset.value));
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('AssetTableCompanion(')
          ..write('id: $id, ')
          ..write('connectionId: $connectionId, ')
          ..write('parentAssetId: $parentAssetId, ')
          ..write('isSharedAsset: $isSharedAsset, ')
          ..write('lastSync: $lastSync, ')
          ..write('asset: $asset, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $AssetHistoryTableTable extends AssetHistoryTable
    with TableInfo<$AssetHistoryTableTable, AssetHistoryTableData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $AssetHistoryTableTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<String> id = GeneratedColumn<String>(
      'id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _assetIdMeta =
      const VerificationMeta('assetId');
  @override
  late final GeneratedColumn<String> assetId = GeneratedColumn<String>(
      'asset_id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _createdAtMeta =
      const VerificationMeta('createdAt');
  @override
  late final GeneratedColumn<DateTime> createdAt = GeneratedColumn<DateTime>(
      'created_at', aliasedName, false,
      type: DriftSqlType.dateTime, requiredDuringInsert: true);
  static const VerificationMeta _assetHistoryMeta =
      const VerificationMeta('assetHistory');
  @override
  late final GeneratedColumnWithTypeConverter<AssetHistoryItemEntity?, String>
      assetHistory = GeneratedColumn<String>('asset_history', aliasedName, true,
              type: DriftSqlType.string, requiredDuringInsert: false)
          .withConverter<AssetHistoryItemEntity?>(
              $AssetHistoryTableTable.$converterassetHistoryn);
  @override
  List<GeneratedColumn> get $columns => [id, assetId, createdAt, assetHistory];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'asset_history_table';
  @override
  VerificationContext validateIntegrity(
      Insertable<AssetHistoryTableData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    } else if (isInserting) {
      context.missing(_idMeta);
    }
    if (data.containsKey('asset_id')) {
      context.handle(_assetIdMeta,
          assetId.isAcceptableOrUnknown(data['asset_id']!, _assetIdMeta));
    } else if (isInserting) {
      context.missing(_assetIdMeta);
    }
    if (data.containsKey('created_at')) {
      context.handle(_createdAtMeta,
          createdAt.isAcceptableOrUnknown(data['created_at']!, _createdAtMeta));
    } else if (isInserting) {
      context.missing(_createdAtMeta);
    }
    context.handle(_assetHistoryMeta, const VerificationResult.success());
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id, assetId};
  @override
  AssetHistoryTableData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return AssetHistoryTableData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}id'])!,
      assetId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}asset_id'])!,
      createdAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}created_at'])!,
      assetHistory: $AssetHistoryTableTable.$converterassetHistoryn.fromSql(
          attachedDatabase.typeMapping.read(
              DriftSqlType.string, data['${effectivePrefix}asset_history'])),
    );
  }

  @override
  $AssetHistoryTableTable createAlias(String alias) {
    return $AssetHistoryTableTable(attachedDatabase, alias);
  }

  static TypeConverter<AssetHistoryItemEntity, String> $converterassetHistory =
      AssetHistoryItemEntityConverter();
  static TypeConverter<AssetHistoryItemEntity?, String?>
      $converterassetHistoryn =
      NullAwareTypeConverter.wrap($converterassetHistory);
}

class AssetHistoryTableData extends DataClass
    implements Insertable<AssetHistoryTableData> {
  final String id;
  final String assetId;
  final DateTime createdAt;
  final AssetHistoryItemEntity? assetHistory;
  const AssetHistoryTableData(
      {required this.id,
      required this.assetId,
      required this.createdAt,
      this.assetHistory});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<String>(id);
    map['asset_id'] = Variable<String>(assetId);
    map['created_at'] = Variable<DateTime>(createdAt);
    if (!nullToAbsent || assetHistory != null) {
      map['asset_history'] = Variable<String>(
          $AssetHistoryTableTable.$converterassetHistoryn.toSql(assetHistory));
    }
    return map;
  }

  AssetHistoryTableCompanion toCompanion(bool nullToAbsent) {
    return AssetHistoryTableCompanion(
      id: Value(id),
      assetId: Value(assetId),
      createdAt: Value(createdAt),
      assetHistory: assetHistory == null && nullToAbsent
          ? const Value.absent()
          : Value(assetHistory),
    );
  }

  factory AssetHistoryTableData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return AssetHistoryTableData(
      id: serializer.fromJson<String>(json['id']),
      assetId: serializer.fromJson<String>(json['assetId']),
      createdAt: serializer.fromJson<DateTime>(json['createdAt']),
      assetHistory:
          serializer.fromJson<AssetHistoryItemEntity?>(json['assetHistory']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<String>(id),
      'assetId': serializer.toJson<String>(assetId),
      'createdAt': serializer.toJson<DateTime>(createdAt),
      'assetHistory': serializer.toJson<AssetHistoryItemEntity?>(assetHistory),
    };
  }

  AssetHistoryTableData copyWith(
          {String? id,
          String? assetId,
          DateTime? createdAt,
          Value<AssetHistoryItemEntity?> assetHistory =
              const Value.absent()}) =>
      AssetHistoryTableData(
        id: id ?? this.id,
        assetId: assetId ?? this.assetId,
        createdAt: createdAt ?? this.createdAt,
        assetHistory:
            assetHistory.present ? assetHistory.value : this.assetHistory,
      );
  AssetHistoryTableData copyWithCompanion(AssetHistoryTableCompanion data) {
    return AssetHistoryTableData(
      id: data.id.present ? data.id.value : this.id,
      assetId: data.assetId.present ? data.assetId.value : this.assetId,
      createdAt: data.createdAt.present ? data.createdAt.value : this.createdAt,
      assetHistory: data.assetHistory.present
          ? data.assetHistory.value
          : this.assetHistory,
    );
  }

  @override
  String toString() {
    return (StringBuffer('AssetHistoryTableData(')
          ..write('id: $id, ')
          ..write('assetId: $assetId, ')
          ..write('createdAt: $createdAt, ')
          ..write('assetHistory: $assetHistory')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, assetId, createdAt, assetHistory);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is AssetHistoryTableData &&
          other.id == this.id &&
          other.assetId == this.assetId &&
          other.createdAt == this.createdAt &&
          other.assetHistory == this.assetHistory);
}

class AssetHistoryTableCompanion
    extends UpdateCompanion<AssetHistoryTableData> {
  final Value<String> id;
  final Value<String> assetId;
  final Value<DateTime> createdAt;
  final Value<AssetHistoryItemEntity?> assetHistory;
  final Value<int> rowid;
  const AssetHistoryTableCompanion({
    this.id = const Value.absent(),
    this.assetId = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.assetHistory = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  AssetHistoryTableCompanion.insert({
    required String id,
    required String assetId,
    required DateTime createdAt,
    this.assetHistory = const Value.absent(),
    this.rowid = const Value.absent(),
  })  : id = Value(id),
        assetId = Value(assetId),
        createdAt = Value(createdAt);
  static Insertable<AssetHistoryTableData> custom({
    Expression<String>? id,
    Expression<String>? assetId,
    Expression<DateTime>? createdAt,
    Expression<String>? assetHistory,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (assetId != null) 'asset_id': assetId,
      if (createdAt != null) 'created_at': createdAt,
      if (assetHistory != null) 'asset_history': assetHistory,
      if (rowid != null) 'rowid': rowid,
    });
  }

  AssetHistoryTableCompanion copyWith(
      {Value<String>? id,
      Value<String>? assetId,
      Value<DateTime>? createdAt,
      Value<AssetHistoryItemEntity?>? assetHistory,
      Value<int>? rowid}) {
    return AssetHistoryTableCompanion(
      id: id ?? this.id,
      assetId: assetId ?? this.assetId,
      createdAt: createdAt ?? this.createdAt,
      assetHistory: assetHistory ?? this.assetHistory,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<String>(id.value);
    }
    if (assetId.present) {
      map['asset_id'] = Variable<String>(assetId.value);
    }
    if (createdAt.present) {
      map['created_at'] = Variable<DateTime>(createdAt.value);
    }
    if (assetHistory.present) {
      map['asset_history'] = Variable<String>($AssetHistoryTableTable
          .$converterassetHistoryn
          .toSql(assetHistory.value));
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('AssetHistoryTableCompanion(')
          ..write('id: $id, ')
          ..write('assetId: $assetId, ')
          ..write('createdAt: $createdAt, ')
          ..write('assetHistory: $assetHistory, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

abstract class _$AppDatabase extends GeneratedDatabase {
  _$AppDatabase(QueryExecutor e) : super(e);
  $AppDatabaseManager get managers => $AppDatabaseManager(this);
  late final $AuthEntityTable authEntity = $AuthEntityTable(this);
  late final $CurrentUserEntityTable currentUserEntity =
      $CurrentUserEntityTable(this);
  late final $TicketEntityTable ticketEntity = $TicketEntityTable(this);
  late final $TicketMessageEntityTable ticketMessageEntity =
      $TicketMessageEntityTable(this);
  late final $ProcedureTemplateEntityTable procedureTemplateEntity =
      $ProcedureTemplateEntityTable(this);
  late final $TicketListEntityTable ticketListEntity =
      $TicketListEntityTable(this);
  late final $InventoryPartEntityTable inventoryPartEntity =
      $InventoryPartEntityTable(this);
  late final $ProcedureEntityTable procedureEntity =
      $ProcedureEntityTable(this);
  late final $SupportAccountEntityTable supportAccountEntity =
      $SupportAccountEntityTable(this);
  late final $OemDbEntityTable oemDbEntity = $OemDbEntityTable(this);
  late final $TicketCountEntityTable ticketCountEntity =
      $TicketCountEntityTable(this);
  late final $WorkOrderPartsEntityTable workOrderPartsEntity =
      $WorkOrderPartsEntityTable(this);
  late final $TimeLogEntityTable timeLogEntity = $TimeLogEntityTable(this);
  late final $AiNoteTableTable aiNoteTable = $AiNoteTableTable(this);
  late final $ProcedureSubAssetEntityTable procedureSubAssetEntity =
      $ProcedureSubAssetEntityTable(this);
  late final $CustomerRequestTableTable customerRequestTable =
      $CustomerRequestTableTable(this);
  late final $CustomerRequestListTableTable customerRequestListTable =
      $CustomerRequestListTableTable(this);
  late final $CustomerRequestCountTableTable customerRequestCountTable =
      $CustomerRequestCountTableTable(this);
  late final $AiAssistantTableTable aiAssistantTable =
      $AiAssistantTableTable(this);
  late final $ConnectionTableTable connectionTable =
      $ConnectionTableTable(this);
  late final $CustomFieldsTableTable customFieldsTable =
      $CustomFieldsTableTable(this);
  late final $CustomFieldValuesTableTable customFieldValuesTable =
      $CustomFieldValuesTableTable(this);
  late final $AssetTableTable assetTable = $AssetTableTable(this);
  late final $AssetHistoryTableTable assetHistoryTable =
      $AssetHistoryTableTable(this);
  @override
  Iterable<TableInfo<Table, Object?>> get allTables =>
      allSchemaEntities.whereType<TableInfo<Table, Object?>>();
  @override
  List<DatabaseSchemaEntity> get allSchemaEntities => [
        authEntity,
        currentUserEntity,
        ticketEntity,
        ticketMessageEntity,
        procedureTemplateEntity,
        ticketListEntity,
        inventoryPartEntity,
        procedureEntity,
        supportAccountEntity,
        oemDbEntity,
        ticketCountEntity,
        workOrderPartsEntity,
        timeLogEntity,
        aiNoteTable,
        procedureSubAssetEntity,
        customerRequestTable,
        customerRequestListTable,
        customerRequestCountTable,
        aiAssistantTable,
        connectionTable,
        customFieldsTable,
        customFieldValuesTable,
        assetTable,
        assetHistoryTable
      ];
}

typedef $$AuthEntityTableCreateCompanionBuilder = AuthEntityCompanion Function({
  Value<int> id,
  Value<LoginResponse?> loginResponse,
});
typedef $$AuthEntityTableUpdateCompanionBuilder = AuthEntityCompanion Function({
  Value<int> id,
  Value<LoginResponse?> loginResponse,
});

class $$AuthEntityTableFilterComposer
    extends FilterComposer<_$AppDatabase, $AuthEntityTable> {
  $$AuthEntityTableFilterComposer(super.$state);
  ColumnFilters<int> get id => $state.composableBuilder(
      column: $state.table.id,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnWithTypeConverterFilters<LoginResponse?, LoginResponse, String>
      get loginResponse => $state.composableBuilder(
          column: $state.table.loginResponse,
          builder: (column, joinBuilders) => ColumnWithTypeConverterFilters(
              column,
              joinBuilders: joinBuilders));
}

class $$AuthEntityTableOrderingComposer
    extends OrderingComposer<_$AppDatabase, $AuthEntityTable> {
  $$AuthEntityTableOrderingComposer(super.$state);
  ColumnOrderings<int> get id => $state.composableBuilder(
      column: $state.table.id,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get loginResponse => $state.composableBuilder(
      column: $state.table.loginResponse,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));
}

class $$AuthEntityTableTableManager extends RootTableManager<
    _$AppDatabase,
    $AuthEntityTable,
    AuthEntityData,
    $$AuthEntityTableFilterComposer,
    $$AuthEntityTableOrderingComposer,
    $$AuthEntityTableCreateCompanionBuilder,
    $$AuthEntityTableUpdateCompanionBuilder,
    (
      AuthEntityData,
      BaseReferences<_$AppDatabase, $AuthEntityTable, AuthEntityData>
    ),
    AuthEntityData,
    PrefetchHooks Function()> {
  $$AuthEntityTableTableManager(_$AppDatabase db, $AuthEntityTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          filteringComposer:
              $$AuthEntityTableFilterComposer(ComposerState(db, table)),
          orderingComposer:
              $$AuthEntityTableOrderingComposer(ComposerState(db, table)),
          updateCompanionCallback: ({
            Value<int> id = const Value.absent(),
            Value<LoginResponse?> loginResponse = const Value.absent(),
          }) =>
              AuthEntityCompanion(
            id: id,
            loginResponse: loginResponse,
          ),
          createCompanionCallback: ({
            Value<int> id = const Value.absent(),
            Value<LoginResponse?> loginResponse = const Value.absent(),
          }) =>
              AuthEntityCompanion.insert(
            id: id,
            loginResponse: loginResponse,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$AuthEntityTableProcessedTableManager = ProcessedTableManager<
    _$AppDatabase,
    $AuthEntityTable,
    AuthEntityData,
    $$AuthEntityTableFilterComposer,
    $$AuthEntityTableOrderingComposer,
    $$AuthEntityTableCreateCompanionBuilder,
    $$AuthEntityTableUpdateCompanionBuilder,
    (
      AuthEntityData,
      BaseReferences<_$AppDatabase, $AuthEntityTable, AuthEntityData>
    ),
    AuthEntityData,
    PrefetchHooks Function()>;
typedef $$CurrentUserEntityTableCreateCompanionBuilder
    = CurrentUserEntityCompanion Function({
  required String id,
  Value<CurrentUserDbModel?> item,
  required bool isFlushed,
  Value<int> rowid,
});
typedef $$CurrentUserEntityTableUpdateCompanionBuilder
    = CurrentUserEntityCompanion Function({
  Value<String> id,
  Value<CurrentUserDbModel?> item,
  Value<bool> isFlushed,
  Value<int> rowid,
});

class $$CurrentUserEntityTableFilterComposer
    extends FilterComposer<_$AppDatabase, $CurrentUserEntityTable> {
  $$CurrentUserEntityTableFilterComposer(super.$state);
  ColumnFilters<String> get id => $state.composableBuilder(
      column: $state.table.id,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnWithTypeConverterFilters<CurrentUserDbModel?, CurrentUserDbModel,
          String>
      get item => $state.composableBuilder(
          column: $state.table.item,
          builder: (column, joinBuilders) => ColumnWithTypeConverterFilters(
              column,
              joinBuilders: joinBuilders));

  ColumnFilters<bool> get isFlushed => $state.composableBuilder(
      column: $state.table.isFlushed,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));
}

class $$CurrentUserEntityTableOrderingComposer
    extends OrderingComposer<_$AppDatabase, $CurrentUserEntityTable> {
  $$CurrentUserEntityTableOrderingComposer(super.$state);
  ColumnOrderings<String> get id => $state.composableBuilder(
      column: $state.table.id,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get item => $state.composableBuilder(
      column: $state.table.item,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<bool> get isFlushed => $state.composableBuilder(
      column: $state.table.isFlushed,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));
}

class $$CurrentUserEntityTableTableManager extends RootTableManager<
    _$AppDatabase,
    $CurrentUserEntityTable,
    CurrentUserEntityData,
    $$CurrentUserEntityTableFilterComposer,
    $$CurrentUserEntityTableOrderingComposer,
    $$CurrentUserEntityTableCreateCompanionBuilder,
    $$CurrentUserEntityTableUpdateCompanionBuilder,
    (
      CurrentUserEntityData,
      BaseReferences<_$AppDatabase, $CurrentUserEntityTable,
          CurrentUserEntityData>
    ),
    CurrentUserEntityData,
    PrefetchHooks Function()> {
  $$CurrentUserEntityTableTableManager(
      _$AppDatabase db, $CurrentUserEntityTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          filteringComposer:
              $$CurrentUserEntityTableFilterComposer(ComposerState(db, table)),
          orderingComposer: $$CurrentUserEntityTableOrderingComposer(
              ComposerState(db, table)),
          updateCompanionCallback: ({
            Value<String> id = const Value.absent(),
            Value<CurrentUserDbModel?> item = const Value.absent(),
            Value<bool> isFlushed = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              CurrentUserEntityCompanion(
            id: id,
            item: item,
            isFlushed: isFlushed,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String id,
            Value<CurrentUserDbModel?> item = const Value.absent(),
            required bool isFlushed,
            Value<int> rowid = const Value.absent(),
          }) =>
              CurrentUserEntityCompanion.insert(
            id: id,
            item: item,
            isFlushed: isFlushed,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$CurrentUserEntityTableProcessedTableManager = ProcessedTableManager<
    _$AppDatabase,
    $CurrentUserEntityTable,
    CurrentUserEntityData,
    $$CurrentUserEntityTableFilterComposer,
    $$CurrentUserEntityTableOrderingComposer,
    $$CurrentUserEntityTableCreateCompanionBuilder,
    $$CurrentUserEntityTableUpdateCompanionBuilder,
    (
      CurrentUserEntityData,
      BaseReferences<_$AppDatabase, $CurrentUserEntityTable,
          CurrentUserEntityData>
    ),
    CurrentUserEntityData,
    PrefetchHooks Function()>;
typedef $$TicketEntityTableCreateCompanionBuilder = TicketEntityCompanion
    Function({
  required String id,
  Value<WorkOrderDbModel?> item,
  required bool isFlushed,
  Value<int> rowid,
});
typedef $$TicketEntityTableUpdateCompanionBuilder = TicketEntityCompanion
    Function({
  Value<String> id,
  Value<WorkOrderDbModel?> item,
  Value<bool> isFlushed,
  Value<int> rowid,
});

class $$TicketEntityTableFilterComposer
    extends FilterComposer<_$AppDatabase, $TicketEntityTable> {
  $$TicketEntityTableFilterComposer(super.$state);
  ColumnFilters<String> get id => $state.composableBuilder(
      column: $state.table.id,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnWithTypeConverterFilters<WorkOrderDbModel?, WorkOrderDbModel, String>
      get item => $state.composableBuilder(
          column: $state.table.item,
          builder: (column, joinBuilders) => ColumnWithTypeConverterFilters(
              column,
              joinBuilders: joinBuilders));

  ColumnFilters<bool> get isFlushed => $state.composableBuilder(
      column: $state.table.isFlushed,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));
}

class $$TicketEntityTableOrderingComposer
    extends OrderingComposer<_$AppDatabase, $TicketEntityTable> {
  $$TicketEntityTableOrderingComposer(super.$state);
  ColumnOrderings<String> get id => $state.composableBuilder(
      column: $state.table.id,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get item => $state.composableBuilder(
      column: $state.table.item,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<bool> get isFlushed => $state.composableBuilder(
      column: $state.table.isFlushed,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));
}

class $$TicketEntityTableTableManager extends RootTableManager<
    _$AppDatabase,
    $TicketEntityTable,
    TicketEntityData,
    $$TicketEntityTableFilterComposer,
    $$TicketEntityTableOrderingComposer,
    $$TicketEntityTableCreateCompanionBuilder,
    $$TicketEntityTableUpdateCompanionBuilder,
    (
      TicketEntityData,
      BaseReferences<_$AppDatabase, $TicketEntityTable, TicketEntityData>
    ),
    TicketEntityData,
    PrefetchHooks Function()> {
  $$TicketEntityTableTableManager(_$AppDatabase db, $TicketEntityTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          filteringComposer:
              $$TicketEntityTableFilterComposer(ComposerState(db, table)),
          orderingComposer:
              $$TicketEntityTableOrderingComposer(ComposerState(db, table)),
          updateCompanionCallback: ({
            Value<String> id = const Value.absent(),
            Value<WorkOrderDbModel?> item = const Value.absent(),
            Value<bool> isFlushed = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              TicketEntityCompanion(
            id: id,
            item: item,
            isFlushed: isFlushed,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String id,
            Value<WorkOrderDbModel?> item = const Value.absent(),
            required bool isFlushed,
            Value<int> rowid = const Value.absent(),
          }) =>
              TicketEntityCompanion.insert(
            id: id,
            item: item,
            isFlushed: isFlushed,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$TicketEntityTableProcessedTableManager = ProcessedTableManager<
    _$AppDatabase,
    $TicketEntityTable,
    TicketEntityData,
    $$TicketEntityTableFilterComposer,
    $$TicketEntityTableOrderingComposer,
    $$TicketEntityTableCreateCompanionBuilder,
    $$TicketEntityTableUpdateCompanionBuilder,
    (
      TicketEntityData,
      BaseReferences<_$AppDatabase, $TicketEntityTable, TicketEntityData>
    ),
    TicketEntityData,
    PrefetchHooks Function()>;
typedef $$TicketMessageEntityTableCreateCompanionBuilder
    = TicketMessageEntityCompanion Function({
  required String id,
  required String ticket,
  required String channel,
  Value<MessageDbModel?> item,
  required DateTime time,
  required bool isFlushed,
  Value<int> rowid,
});
typedef $$TicketMessageEntityTableUpdateCompanionBuilder
    = TicketMessageEntityCompanion Function({
  Value<String> id,
  Value<String> ticket,
  Value<String> channel,
  Value<MessageDbModel?> item,
  Value<DateTime> time,
  Value<bool> isFlushed,
  Value<int> rowid,
});

class $$TicketMessageEntityTableFilterComposer
    extends FilterComposer<_$AppDatabase, $TicketMessageEntityTable> {
  $$TicketMessageEntityTableFilterComposer(super.$state);
  ColumnFilters<String> get id => $state.composableBuilder(
      column: $state.table.id,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get ticket => $state.composableBuilder(
      column: $state.table.ticket,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get channel => $state.composableBuilder(
      column: $state.table.channel,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnWithTypeConverterFilters<MessageDbModel?, MessageDbModel, String>
      get item => $state.composableBuilder(
          column: $state.table.item,
          builder: (column, joinBuilders) => ColumnWithTypeConverterFilters(
              column,
              joinBuilders: joinBuilders));

  ColumnFilters<DateTime> get time => $state.composableBuilder(
      column: $state.table.time,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<bool> get isFlushed => $state.composableBuilder(
      column: $state.table.isFlushed,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));
}

class $$TicketMessageEntityTableOrderingComposer
    extends OrderingComposer<_$AppDatabase, $TicketMessageEntityTable> {
  $$TicketMessageEntityTableOrderingComposer(super.$state);
  ColumnOrderings<String> get id => $state.composableBuilder(
      column: $state.table.id,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get ticket => $state.composableBuilder(
      column: $state.table.ticket,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get channel => $state.composableBuilder(
      column: $state.table.channel,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get item => $state.composableBuilder(
      column: $state.table.item,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<DateTime> get time => $state.composableBuilder(
      column: $state.table.time,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<bool> get isFlushed => $state.composableBuilder(
      column: $state.table.isFlushed,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));
}

class $$TicketMessageEntityTableTableManager extends RootTableManager<
    _$AppDatabase,
    $TicketMessageEntityTable,
    TicketMessageEntityData,
    $$TicketMessageEntityTableFilterComposer,
    $$TicketMessageEntityTableOrderingComposer,
    $$TicketMessageEntityTableCreateCompanionBuilder,
    $$TicketMessageEntityTableUpdateCompanionBuilder,
    (
      TicketMessageEntityData,
      BaseReferences<_$AppDatabase, $TicketMessageEntityTable,
          TicketMessageEntityData>
    ),
    TicketMessageEntityData,
    PrefetchHooks Function()> {
  $$TicketMessageEntityTableTableManager(
      _$AppDatabase db, $TicketMessageEntityTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          filteringComposer: $$TicketMessageEntityTableFilterComposer(
              ComposerState(db, table)),
          orderingComposer: $$TicketMessageEntityTableOrderingComposer(
              ComposerState(db, table)),
          updateCompanionCallback: ({
            Value<String> id = const Value.absent(),
            Value<String> ticket = const Value.absent(),
            Value<String> channel = const Value.absent(),
            Value<MessageDbModel?> item = const Value.absent(),
            Value<DateTime> time = const Value.absent(),
            Value<bool> isFlushed = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              TicketMessageEntityCompanion(
            id: id,
            ticket: ticket,
            channel: channel,
            item: item,
            time: time,
            isFlushed: isFlushed,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String id,
            required String ticket,
            required String channel,
            Value<MessageDbModel?> item = const Value.absent(),
            required DateTime time,
            required bool isFlushed,
            Value<int> rowid = const Value.absent(),
          }) =>
              TicketMessageEntityCompanion.insert(
            id: id,
            ticket: ticket,
            channel: channel,
            item: item,
            time: time,
            isFlushed: isFlushed,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$TicketMessageEntityTableProcessedTableManager = ProcessedTableManager<
    _$AppDatabase,
    $TicketMessageEntityTable,
    TicketMessageEntityData,
    $$TicketMessageEntityTableFilterComposer,
    $$TicketMessageEntityTableOrderingComposer,
    $$TicketMessageEntityTableCreateCompanionBuilder,
    $$TicketMessageEntityTableUpdateCompanionBuilder,
    (
      TicketMessageEntityData,
      BaseReferences<_$AppDatabase, $TicketMessageEntityTable,
          TicketMessageEntityData>
    ),
    TicketMessageEntityData,
    PrefetchHooks Function()>;
typedef $$ProcedureTemplateEntityTableCreateCompanionBuilder
    = ProcedureTemplateEntityCompanion Function({
  required String id,
  Value<ProcedureTemplateDbModel?> item,
  Value<int> rowid,
});
typedef $$ProcedureTemplateEntityTableUpdateCompanionBuilder
    = ProcedureTemplateEntityCompanion Function({
  Value<String> id,
  Value<ProcedureTemplateDbModel?> item,
  Value<int> rowid,
});

class $$ProcedureTemplateEntityTableFilterComposer
    extends FilterComposer<_$AppDatabase, $ProcedureTemplateEntityTable> {
  $$ProcedureTemplateEntityTableFilterComposer(super.$state);
  ColumnFilters<String> get id => $state.composableBuilder(
      column: $state.table.id,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnWithTypeConverterFilters<ProcedureTemplateDbModel?,
          ProcedureTemplateDbModel, String>
      get item => $state.composableBuilder(
          column: $state.table.item,
          builder: (column, joinBuilders) => ColumnWithTypeConverterFilters(
              column,
              joinBuilders: joinBuilders));
}

class $$ProcedureTemplateEntityTableOrderingComposer
    extends OrderingComposer<_$AppDatabase, $ProcedureTemplateEntityTable> {
  $$ProcedureTemplateEntityTableOrderingComposer(super.$state);
  ColumnOrderings<String> get id => $state.composableBuilder(
      column: $state.table.id,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get item => $state.composableBuilder(
      column: $state.table.item,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));
}

class $$ProcedureTemplateEntityTableTableManager extends RootTableManager<
    _$AppDatabase,
    $ProcedureTemplateEntityTable,
    ProcedureTemplateEntityData,
    $$ProcedureTemplateEntityTableFilterComposer,
    $$ProcedureTemplateEntityTableOrderingComposer,
    $$ProcedureTemplateEntityTableCreateCompanionBuilder,
    $$ProcedureTemplateEntityTableUpdateCompanionBuilder,
    (
      ProcedureTemplateEntityData,
      BaseReferences<_$AppDatabase, $ProcedureTemplateEntityTable,
          ProcedureTemplateEntityData>
    ),
    ProcedureTemplateEntityData,
    PrefetchHooks Function()> {
  $$ProcedureTemplateEntityTableTableManager(
      _$AppDatabase db, $ProcedureTemplateEntityTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          filteringComposer: $$ProcedureTemplateEntityTableFilterComposer(
              ComposerState(db, table)),
          orderingComposer: $$ProcedureTemplateEntityTableOrderingComposer(
              ComposerState(db, table)),
          updateCompanionCallback: ({
            Value<String> id = const Value.absent(),
            Value<ProcedureTemplateDbModel?> item = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              ProcedureTemplateEntityCompanion(
            id: id,
            item: item,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String id,
            Value<ProcedureTemplateDbModel?> item = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              ProcedureTemplateEntityCompanion.insert(
            id: id,
            item: item,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$ProcedureTemplateEntityTableProcessedTableManager
    = ProcessedTableManager<
        _$AppDatabase,
        $ProcedureTemplateEntityTable,
        ProcedureTemplateEntityData,
        $$ProcedureTemplateEntityTableFilterComposer,
        $$ProcedureTemplateEntityTableOrderingComposer,
        $$ProcedureTemplateEntityTableCreateCompanionBuilder,
        $$ProcedureTemplateEntityTableUpdateCompanionBuilder,
        (
          ProcedureTemplateEntityData,
          BaseReferences<_$AppDatabase, $ProcedureTemplateEntityTable,
              ProcedureTemplateEntityData>
        ),
        ProcedureTemplateEntityData,
        PrefetchHooks Function()>;
typedef $$TicketListEntityTableCreateCompanionBuilder
    = TicketListEntityCompanion Function({
  required String id,
  required String type,
  Value<int> rowid,
});
typedef $$TicketListEntityTableUpdateCompanionBuilder
    = TicketListEntityCompanion Function({
  Value<String> id,
  Value<String> type,
  Value<int> rowid,
});

class $$TicketListEntityTableFilterComposer
    extends FilterComposer<_$AppDatabase, $TicketListEntityTable> {
  $$TicketListEntityTableFilterComposer(super.$state);
  ColumnFilters<String> get id => $state.composableBuilder(
      column: $state.table.id,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get type => $state.composableBuilder(
      column: $state.table.type,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));
}

class $$TicketListEntityTableOrderingComposer
    extends OrderingComposer<_$AppDatabase, $TicketListEntityTable> {
  $$TicketListEntityTableOrderingComposer(super.$state);
  ColumnOrderings<String> get id => $state.composableBuilder(
      column: $state.table.id,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get type => $state.composableBuilder(
      column: $state.table.type,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));
}

class $$TicketListEntityTableTableManager extends RootTableManager<
    _$AppDatabase,
    $TicketListEntityTable,
    TicketListEntityData,
    $$TicketListEntityTableFilterComposer,
    $$TicketListEntityTableOrderingComposer,
    $$TicketListEntityTableCreateCompanionBuilder,
    $$TicketListEntityTableUpdateCompanionBuilder,
    (
      TicketListEntityData,
      BaseReferences<_$AppDatabase, $TicketListEntityTable,
          TicketListEntityData>
    ),
    TicketListEntityData,
    PrefetchHooks Function()> {
  $$TicketListEntityTableTableManager(
      _$AppDatabase db, $TicketListEntityTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          filteringComposer:
              $$TicketListEntityTableFilterComposer(ComposerState(db, table)),
          orderingComposer:
              $$TicketListEntityTableOrderingComposer(ComposerState(db, table)),
          updateCompanionCallback: ({
            Value<String> id = const Value.absent(),
            Value<String> type = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              TicketListEntityCompanion(
            id: id,
            type: type,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String id,
            required String type,
            Value<int> rowid = const Value.absent(),
          }) =>
              TicketListEntityCompanion.insert(
            id: id,
            type: type,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$TicketListEntityTableProcessedTableManager = ProcessedTableManager<
    _$AppDatabase,
    $TicketListEntityTable,
    TicketListEntityData,
    $$TicketListEntityTableFilterComposer,
    $$TicketListEntityTableOrderingComposer,
    $$TicketListEntityTableCreateCompanionBuilder,
    $$TicketListEntityTableUpdateCompanionBuilder,
    (
      TicketListEntityData,
      BaseReferences<_$AppDatabase, $TicketListEntityTable,
          TicketListEntityData>
    ),
    TicketListEntityData,
    PrefetchHooks Function()>;
typedef $$InventoryPartEntityTableCreateCompanionBuilder
    = InventoryPartEntityCompanion Function({
  required String id,
  Value<InventoryPartDbModel?> item,
  Value<int> rowid,
});
typedef $$InventoryPartEntityTableUpdateCompanionBuilder
    = InventoryPartEntityCompanion Function({
  Value<String> id,
  Value<InventoryPartDbModel?> item,
  Value<int> rowid,
});

class $$InventoryPartEntityTableFilterComposer
    extends FilterComposer<_$AppDatabase, $InventoryPartEntityTable> {
  $$InventoryPartEntityTableFilterComposer(super.$state);
  ColumnFilters<String> get id => $state.composableBuilder(
      column: $state.table.id,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnWithTypeConverterFilters<InventoryPartDbModel?, InventoryPartDbModel,
          String>
      get item => $state.composableBuilder(
          column: $state.table.item,
          builder: (column, joinBuilders) => ColumnWithTypeConverterFilters(
              column,
              joinBuilders: joinBuilders));
}

class $$InventoryPartEntityTableOrderingComposer
    extends OrderingComposer<_$AppDatabase, $InventoryPartEntityTable> {
  $$InventoryPartEntityTableOrderingComposer(super.$state);
  ColumnOrderings<String> get id => $state.composableBuilder(
      column: $state.table.id,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get item => $state.composableBuilder(
      column: $state.table.item,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));
}

class $$InventoryPartEntityTableTableManager extends RootTableManager<
    _$AppDatabase,
    $InventoryPartEntityTable,
    InventoryPartEntityData,
    $$InventoryPartEntityTableFilterComposer,
    $$InventoryPartEntityTableOrderingComposer,
    $$InventoryPartEntityTableCreateCompanionBuilder,
    $$InventoryPartEntityTableUpdateCompanionBuilder,
    (
      InventoryPartEntityData,
      BaseReferences<_$AppDatabase, $InventoryPartEntityTable,
          InventoryPartEntityData>
    ),
    InventoryPartEntityData,
    PrefetchHooks Function()> {
  $$InventoryPartEntityTableTableManager(
      _$AppDatabase db, $InventoryPartEntityTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          filteringComposer: $$InventoryPartEntityTableFilterComposer(
              ComposerState(db, table)),
          orderingComposer: $$InventoryPartEntityTableOrderingComposer(
              ComposerState(db, table)),
          updateCompanionCallback: ({
            Value<String> id = const Value.absent(),
            Value<InventoryPartDbModel?> item = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              InventoryPartEntityCompanion(
            id: id,
            item: item,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String id,
            Value<InventoryPartDbModel?> item = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              InventoryPartEntityCompanion.insert(
            id: id,
            item: item,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$InventoryPartEntityTableProcessedTableManager = ProcessedTableManager<
    _$AppDatabase,
    $InventoryPartEntityTable,
    InventoryPartEntityData,
    $$InventoryPartEntityTableFilterComposer,
    $$InventoryPartEntityTableOrderingComposer,
    $$InventoryPartEntityTableCreateCompanionBuilder,
    $$InventoryPartEntityTableUpdateCompanionBuilder,
    (
      InventoryPartEntityData,
      BaseReferences<_$AppDatabase, $InventoryPartEntityTable,
          InventoryPartEntityData>
    ),
    InventoryPartEntityData,
    PrefetchHooks Function()>;
typedef $$ProcedureEntityTableCreateCompanionBuilder = ProcedureEntityCompanion
    Function({
  required String id,
  required String ticketID,
  Value<String?> templateID,
  Value<ProcedureDbModel?> item,
  required bool isFlushed,
  required bool isDeleted,
  required bool isDownloaded,
  required ProcedureEntityStatus status,
  Value<int> rowid,
});
typedef $$ProcedureEntityTableUpdateCompanionBuilder = ProcedureEntityCompanion
    Function({
  Value<String> id,
  Value<String> ticketID,
  Value<String?> templateID,
  Value<ProcedureDbModel?> item,
  Value<bool> isFlushed,
  Value<bool> isDeleted,
  Value<bool> isDownloaded,
  Value<ProcedureEntityStatus> status,
  Value<int> rowid,
});

class $$ProcedureEntityTableFilterComposer
    extends FilterComposer<_$AppDatabase, $ProcedureEntityTable> {
  $$ProcedureEntityTableFilterComposer(super.$state);
  ColumnFilters<String> get id => $state.composableBuilder(
      column: $state.table.id,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get ticketID => $state.composableBuilder(
      column: $state.table.ticketID,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get templateID => $state.composableBuilder(
      column: $state.table.templateID,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnWithTypeConverterFilters<ProcedureDbModel?, ProcedureDbModel, String>
      get item => $state.composableBuilder(
          column: $state.table.item,
          builder: (column, joinBuilders) => ColumnWithTypeConverterFilters(
              column,
              joinBuilders: joinBuilders));

  ColumnFilters<bool> get isFlushed => $state.composableBuilder(
      column: $state.table.isFlushed,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<bool> get isDeleted => $state.composableBuilder(
      column: $state.table.isDeleted,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<bool> get isDownloaded => $state.composableBuilder(
      column: $state.table.isDownloaded,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnWithTypeConverterFilters<ProcedureEntityStatus, ProcedureEntityStatus,
          String>
      get status => $state.composableBuilder(
          column: $state.table.status,
          builder: (column, joinBuilders) => ColumnWithTypeConverterFilters(
              column,
              joinBuilders: joinBuilders));
}

class $$ProcedureEntityTableOrderingComposer
    extends OrderingComposer<_$AppDatabase, $ProcedureEntityTable> {
  $$ProcedureEntityTableOrderingComposer(super.$state);
  ColumnOrderings<String> get id => $state.composableBuilder(
      column: $state.table.id,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get ticketID => $state.composableBuilder(
      column: $state.table.ticketID,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get templateID => $state.composableBuilder(
      column: $state.table.templateID,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get item => $state.composableBuilder(
      column: $state.table.item,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<bool> get isFlushed => $state.composableBuilder(
      column: $state.table.isFlushed,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<bool> get isDeleted => $state.composableBuilder(
      column: $state.table.isDeleted,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<bool> get isDownloaded => $state.composableBuilder(
      column: $state.table.isDownloaded,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get status => $state.composableBuilder(
      column: $state.table.status,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));
}

class $$ProcedureEntityTableTableManager extends RootTableManager<
    _$AppDatabase,
    $ProcedureEntityTable,
    ProcedureEntityData,
    $$ProcedureEntityTableFilterComposer,
    $$ProcedureEntityTableOrderingComposer,
    $$ProcedureEntityTableCreateCompanionBuilder,
    $$ProcedureEntityTableUpdateCompanionBuilder,
    (
      ProcedureEntityData,
      BaseReferences<_$AppDatabase, $ProcedureEntityTable, ProcedureEntityData>
    ),
    ProcedureEntityData,
    PrefetchHooks Function()> {
  $$ProcedureEntityTableTableManager(
      _$AppDatabase db, $ProcedureEntityTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          filteringComposer:
              $$ProcedureEntityTableFilterComposer(ComposerState(db, table)),
          orderingComposer:
              $$ProcedureEntityTableOrderingComposer(ComposerState(db, table)),
          updateCompanionCallback: ({
            Value<String> id = const Value.absent(),
            Value<String> ticketID = const Value.absent(),
            Value<String?> templateID = const Value.absent(),
            Value<ProcedureDbModel?> item = const Value.absent(),
            Value<bool> isFlushed = const Value.absent(),
            Value<bool> isDeleted = const Value.absent(),
            Value<bool> isDownloaded = const Value.absent(),
            Value<ProcedureEntityStatus> status = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              ProcedureEntityCompanion(
            id: id,
            ticketID: ticketID,
            templateID: templateID,
            item: item,
            isFlushed: isFlushed,
            isDeleted: isDeleted,
            isDownloaded: isDownloaded,
            status: status,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String id,
            required String ticketID,
            Value<String?> templateID = const Value.absent(),
            Value<ProcedureDbModel?> item = const Value.absent(),
            required bool isFlushed,
            required bool isDeleted,
            required bool isDownloaded,
            required ProcedureEntityStatus status,
            Value<int> rowid = const Value.absent(),
          }) =>
              ProcedureEntityCompanion.insert(
            id: id,
            ticketID: ticketID,
            templateID: templateID,
            item: item,
            isFlushed: isFlushed,
            isDeleted: isDeleted,
            isDownloaded: isDownloaded,
            status: status,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$ProcedureEntityTableProcessedTableManager = ProcessedTableManager<
    _$AppDatabase,
    $ProcedureEntityTable,
    ProcedureEntityData,
    $$ProcedureEntityTableFilterComposer,
    $$ProcedureEntityTableOrderingComposer,
    $$ProcedureEntityTableCreateCompanionBuilder,
    $$ProcedureEntityTableUpdateCompanionBuilder,
    (
      ProcedureEntityData,
      BaseReferences<_$AppDatabase, $ProcedureEntityTable, ProcedureEntityData>
    ),
    ProcedureEntityData,
    PrefetchHooks Function()>;
typedef $$SupportAccountEntityTableCreateCompanionBuilder
    = SupportAccountEntityCompanion Function({
  required String id,
  Value<SupportAccountDbModel?> item,
  Value<int> rowid,
});
typedef $$SupportAccountEntityTableUpdateCompanionBuilder
    = SupportAccountEntityCompanion Function({
  Value<String> id,
  Value<SupportAccountDbModel?> item,
  Value<int> rowid,
});

class $$SupportAccountEntityTableFilterComposer
    extends FilterComposer<_$AppDatabase, $SupportAccountEntityTable> {
  $$SupportAccountEntityTableFilterComposer(super.$state);
  ColumnFilters<String> get id => $state.composableBuilder(
      column: $state.table.id,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnWithTypeConverterFilters<SupportAccountDbModel?, SupportAccountDbModel,
          String>
      get item => $state.composableBuilder(
          column: $state.table.item,
          builder: (column, joinBuilders) => ColumnWithTypeConverterFilters(
              column,
              joinBuilders: joinBuilders));
}

class $$SupportAccountEntityTableOrderingComposer
    extends OrderingComposer<_$AppDatabase, $SupportAccountEntityTable> {
  $$SupportAccountEntityTableOrderingComposer(super.$state);
  ColumnOrderings<String> get id => $state.composableBuilder(
      column: $state.table.id,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get item => $state.composableBuilder(
      column: $state.table.item,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));
}

class $$SupportAccountEntityTableTableManager extends RootTableManager<
    _$AppDatabase,
    $SupportAccountEntityTable,
    SupportAccountEntityData,
    $$SupportAccountEntityTableFilterComposer,
    $$SupportAccountEntityTableOrderingComposer,
    $$SupportAccountEntityTableCreateCompanionBuilder,
    $$SupportAccountEntityTableUpdateCompanionBuilder,
    (
      SupportAccountEntityData,
      BaseReferences<_$AppDatabase, $SupportAccountEntityTable,
          SupportAccountEntityData>
    ),
    SupportAccountEntityData,
    PrefetchHooks Function()> {
  $$SupportAccountEntityTableTableManager(
      _$AppDatabase db, $SupportAccountEntityTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          filteringComposer: $$SupportAccountEntityTableFilterComposer(
              ComposerState(db, table)),
          orderingComposer: $$SupportAccountEntityTableOrderingComposer(
              ComposerState(db, table)),
          updateCompanionCallback: ({
            Value<String> id = const Value.absent(),
            Value<SupportAccountDbModel?> item = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              SupportAccountEntityCompanion(
            id: id,
            item: item,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String id,
            Value<SupportAccountDbModel?> item = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              SupportAccountEntityCompanion.insert(
            id: id,
            item: item,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$SupportAccountEntityTableProcessedTableManager
    = ProcessedTableManager<
        _$AppDatabase,
        $SupportAccountEntityTable,
        SupportAccountEntityData,
        $$SupportAccountEntityTableFilterComposer,
        $$SupportAccountEntityTableOrderingComposer,
        $$SupportAccountEntityTableCreateCompanionBuilder,
        $$SupportAccountEntityTableUpdateCompanionBuilder,
        (
          SupportAccountEntityData,
          BaseReferences<_$AppDatabase, $SupportAccountEntityTable,
              SupportAccountEntityData>
        ),
        SupportAccountEntityData,
        PrefetchHooks Function()>;
typedef $$OemDbEntityTableCreateCompanionBuilder = OemDbEntityCompanion
    Function({
  required String id,
  Value<OemEntity?> item,
  Value<List<WorkOrderTypeEntity>?> workOrderTypes,
  Value<List<StatusEntity>?> statuses,
  Value<int> rowid,
});
typedef $$OemDbEntityTableUpdateCompanionBuilder = OemDbEntityCompanion
    Function({
  Value<String> id,
  Value<OemEntity?> item,
  Value<List<WorkOrderTypeEntity>?> workOrderTypes,
  Value<List<StatusEntity>?> statuses,
  Value<int> rowid,
});

class $$OemDbEntityTableFilterComposer
    extends FilterComposer<_$AppDatabase, $OemDbEntityTable> {
  $$OemDbEntityTableFilterComposer(super.$state);
  ColumnFilters<String> get id => $state.composableBuilder(
      column: $state.table.id,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnWithTypeConverterFilters<OemEntity?, OemEntity, String> get item =>
      $state.composableBuilder(
          column: $state.table.item,
          builder: (column, joinBuilders) => ColumnWithTypeConverterFilters(
              column,
              joinBuilders: joinBuilders));

  ColumnWithTypeConverterFilters<List<WorkOrderTypeEntity>?,
          List<WorkOrderTypeEntity>, String>
      get workOrderTypes => $state.composableBuilder(
          column: $state.table.workOrderTypes,
          builder: (column, joinBuilders) => ColumnWithTypeConverterFilters(
              column,
              joinBuilders: joinBuilders));

  ColumnWithTypeConverterFilters<List<StatusEntity>?, List<StatusEntity>,
          String>
      get statuses => $state.composableBuilder(
          column: $state.table.statuses,
          builder: (column, joinBuilders) => ColumnWithTypeConverterFilters(
              column,
              joinBuilders: joinBuilders));
}

class $$OemDbEntityTableOrderingComposer
    extends OrderingComposer<_$AppDatabase, $OemDbEntityTable> {
  $$OemDbEntityTableOrderingComposer(super.$state);
  ColumnOrderings<String> get id => $state.composableBuilder(
      column: $state.table.id,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get item => $state.composableBuilder(
      column: $state.table.item,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get workOrderTypes => $state.composableBuilder(
      column: $state.table.workOrderTypes,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get statuses => $state.composableBuilder(
      column: $state.table.statuses,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));
}

class $$OemDbEntityTableTableManager extends RootTableManager<
    _$AppDatabase,
    $OemDbEntityTable,
    OemDbEntityData,
    $$OemDbEntityTableFilterComposer,
    $$OemDbEntityTableOrderingComposer,
    $$OemDbEntityTableCreateCompanionBuilder,
    $$OemDbEntityTableUpdateCompanionBuilder,
    (
      OemDbEntityData,
      BaseReferences<_$AppDatabase, $OemDbEntityTable, OemDbEntityData>
    ),
    OemDbEntityData,
    PrefetchHooks Function()> {
  $$OemDbEntityTableTableManager(_$AppDatabase db, $OemDbEntityTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          filteringComposer:
              $$OemDbEntityTableFilterComposer(ComposerState(db, table)),
          orderingComposer:
              $$OemDbEntityTableOrderingComposer(ComposerState(db, table)),
          updateCompanionCallback: ({
            Value<String> id = const Value.absent(),
            Value<OemEntity?> item = const Value.absent(),
            Value<List<WorkOrderTypeEntity>?> workOrderTypes =
                const Value.absent(),
            Value<List<StatusEntity>?> statuses = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              OemDbEntityCompanion(
            id: id,
            item: item,
            workOrderTypes: workOrderTypes,
            statuses: statuses,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String id,
            Value<OemEntity?> item = const Value.absent(),
            Value<List<WorkOrderTypeEntity>?> workOrderTypes =
                const Value.absent(),
            Value<List<StatusEntity>?> statuses = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              OemDbEntityCompanion.insert(
            id: id,
            item: item,
            workOrderTypes: workOrderTypes,
            statuses: statuses,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$OemDbEntityTableProcessedTableManager = ProcessedTableManager<
    _$AppDatabase,
    $OemDbEntityTable,
    OemDbEntityData,
    $$OemDbEntityTableFilterComposer,
    $$OemDbEntityTableOrderingComposer,
    $$OemDbEntityTableCreateCompanionBuilder,
    $$OemDbEntityTableUpdateCompanionBuilder,
    (
      OemDbEntityData,
      BaseReferences<_$AppDatabase, $OemDbEntityTable, OemDbEntityData>
    ),
    OemDbEntityData,
    PrefetchHooks Function()>;
typedef $$TicketCountEntityTableCreateCompanionBuilder
    = TicketCountEntityCompanion Function({
  required String type,
  Value<int> count,
  Value<int> rowid,
});
typedef $$TicketCountEntityTableUpdateCompanionBuilder
    = TicketCountEntityCompanion Function({
  Value<String> type,
  Value<int> count,
  Value<int> rowid,
});

class $$TicketCountEntityTableFilterComposer
    extends FilterComposer<_$AppDatabase, $TicketCountEntityTable> {
  $$TicketCountEntityTableFilterComposer(super.$state);
  ColumnFilters<String> get type => $state.composableBuilder(
      column: $state.table.type,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<int> get count => $state.composableBuilder(
      column: $state.table.count,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));
}

class $$TicketCountEntityTableOrderingComposer
    extends OrderingComposer<_$AppDatabase, $TicketCountEntityTable> {
  $$TicketCountEntityTableOrderingComposer(super.$state);
  ColumnOrderings<String> get type => $state.composableBuilder(
      column: $state.table.type,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<int> get count => $state.composableBuilder(
      column: $state.table.count,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));
}

class $$TicketCountEntityTableTableManager extends RootTableManager<
    _$AppDatabase,
    $TicketCountEntityTable,
    TicketCountEntityData,
    $$TicketCountEntityTableFilterComposer,
    $$TicketCountEntityTableOrderingComposer,
    $$TicketCountEntityTableCreateCompanionBuilder,
    $$TicketCountEntityTableUpdateCompanionBuilder,
    (
      TicketCountEntityData,
      BaseReferences<_$AppDatabase, $TicketCountEntityTable,
          TicketCountEntityData>
    ),
    TicketCountEntityData,
    PrefetchHooks Function()> {
  $$TicketCountEntityTableTableManager(
      _$AppDatabase db, $TicketCountEntityTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          filteringComposer:
              $$TicketCountEntityTableFilterComposer(ComposerState(db, table)),
          orderingComposer: $$TicketCountEntityTableOrderingComposer(
              ComposerState(db, table)),
          updateCompanionCallback: ({
            Value<String> type = const Value.absent(),
            Value<int> count = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              TicketCountEntityCompanion(
            type: type,
            count: count,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String type,
            Value<int> count = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              TicketCountEntityCompanion.insert(
            type: type,
            count: count,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$TicketCountEntityTableProcessedTableManager = ProcessedTableManager<
    _$AppDatabase,
    $TicketCountEntityTable,
    TicketCountEntityData,
    $$TicketCountEntityTableFilterComposer,
    $$TicketCountEntityTableOrderingComposer,
    $$TicketCountEntityTableCreateCompanionBuilder,
    $$TicketCountEntityTableUpdateCompanionBuilder,
    (
      TicketCountEntityData,
      BaseReferences<_$AppDatabase, $TicketCountEntityTable,
          TicketCountEntityData>
    ),
    TicketCountEntityData,
    PrefetchHooks Function()>;
typedef $$WorkOrderPartsEntityTableCreateCompanionBuilder
    = WorkOrderPartsEntityCompanion Function({
  required String workOrderId,
  required String partId,
  Value<WorkOrderPartDbModel?> value,
  Value<int> rowid,
});
typedef $$WorkOrderPartsEntityTableUpdateCompanionBuilder
    = WorkOrderPartsEntityCompanion Function({
  Value<String> workOrderId,
  Value<String> partId,
  Value<WorkOrderPartDbModel?> value,
  Value<int> rowid,
});

class $$WorkOrderPartsEntityTableFilterComposer
    extends FilterComposer<_$AppDatabase, $WorkOrderPartsEntityTable> {
  $$WorkOrderPartsEntityTableFilterComposer(super.$state);
  ColumnFilters<String> get workOrderId => $state.composableBuilder(
      column: $state.table.workOrderId,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get partId => $state.composableBuilder(
      column: $state.table.partId,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnWithTypeConverterFilters<WorkOrderPartDbModel?, WorkOrderPartDbModel,
          String>
      get value => $state.composableBuilder(
          column: $state.table.value,
          builder: (column, joinBuilders) => ColumnWithTypeConverterFilters(
              column,
              joinBuilders: joinBuilders));
}

class $$WorkOrderPartsEntityTableOrderingComposer
    extends OrderingComposer<_$AppDatabase, $WorkOrderPartsEntityTable> {
  $$WorkOrderPartsEntityTableOrderingComposer(super.$state);
  ColumnOrderings<String> get workOrderId => $state.composableBuilder(
      column: $state.table.workOrderId,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get partId => $state.composableBuilder(
      column: $state.table.partId,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get value => $state.composableBuilder(
      column: $state.table.value,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));
}

class $$WorkOrderPartsEntityTableTableManager extends RootTableManager<
    _$AppDatabase,
    $WorkOrderPartsEntityTable,
    WorkOrderPartsEntityData,
    $$WorkOrderPartsEntityTableFilterComposer,
    $$WorkOrderPartsEntityTableOrderingComposer,
    $$WorkOrderPartsEntityTableCreateCompanionBuilder,
    $$WorkOrderPartsEntityTableUpdateCompanionBuilder,
    (
      WorkOrderPartsEntityData,
      BaseReferences<_$AppDatabase, $WorkOrderPartsEntityTable,
          WorkOrderPartsEntityData>
    ),
    WorkOrderPartsEntityData,
    PrefetchHooks Function()> {
  $$WorkOrderPartsEntityTableTableManager(
      _$AppDatabase db, $WorkOrderPartsEntityTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          filteringComposer: $$WorkOrderPartsEntityTableFilterComposer(
              ComposerState(db, table)),
          orderingComposer: $$WorkOrderPartsEntityTableOrderingComposer(
              ComposerState(db, table)),
          updateCompanionCallback: ({
            Value<String> workOrderId = const Value.absent(),
            Value<String> partId = const Value.absent(),
            Value<WorkOrderPartDbModel?> value = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              WorkOrderPartsEntityCompanion(
            workOrderId: workOrderId,
            partId: partId,
            value: value,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String workOrderId,
            required String partId,
            Value<WorkOrderPartDbModel?> value = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              WorkOrderPartsEntityCompanion.insert(
            workOrderId: workOrderId,
            partId: partId,
            value: value,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$WorkOrderPartsEntityTableProcessedTableManager
    = ProcessedTableManager<
        _$AppDatabase,
        $WorkOrderPartsEntityTable,
        WorkOrderPartsEntityData,
        $$WorkOrderPartsEntityTableFilterComposer,
        $$WorkOrderPartsEntityTableOrderingComposer,
        $$WorkOrderPartsEntityTableCreateCompanionBuilder,
        $$WorkOrderPartsEntityTableUpdateCompanionBuilder,
        (
          WorkOrderPartsEntityData,
          BaseReferences<_$AppDatabase, $WorkOrderPartsEntityTable,
              WorkOrderPartsEntityData>
        ),
        WorkOrderPartsEntityData,
        PrefetchHooks Function()>;
typedef $$TimeLogEntityTableCreateCompanionBuilder = TimeLogEntityCompanion
    Function({
  required String id,
  required String workOrderId,
  Value<TimeLogDbModel?> item,
  Value<int> rowid,
});
typedef $$TimeLogEntityTableUpdateCompanionBuilder = TimeLogEntityCompanion
    Function({
  Value<String> id,
  Value<String> workOrderId,
  Value<TimeLogDbModel?> item,
  Value<int> rowid,
});

class $$TimeLogEntityTableFilterComposer
    extends FilterComposer<_$AppDatabase, $TimeLogEntityTable> {
  $$TimeLogEntityTableFilterComposer(super.$state);
  ColumnFilters<String> get id => $state.composableBuilder(
      column: $state.table.id,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get workOrderId => $state.composableBuilder(
      column: $state.table.workOrderId,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnWithTypeConverterFilters<TimeLogDbModel?, TimeLogDbModel, String>
      get item => $state.composableBuilder(
          column: $state.table.item,
          builder: (column, joinBuilders) => ColumnWithTypeConverterFilters(
              column,
              joinBuilders: joinBuilders));
}

class $$TimeLogEntityTableOrderingComposer
    extends OrderingComposer<_$AppDatabase, $TimeLogEntityTable> {
  $$TimeLogEntityTableOrderingComposer(super.$state);
  ColumnOrderings<String> get id => $state.composableBuilder(
      column: $state.table.id,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get workOrderId => $state.composableBuilder(
      column: $state.table.workOrderId,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get item => $state.composableBuilder(
      column: $state.table.item,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));
}

class $$TimeLogEntityTableTableManager extends RootTableManager<
    _$AppDatabase,
    $TimeLogEntityTable,
    TimeLogEntityData,
    $$TimeLogEntityTableFilterComposer,
    $$TimeLogEntityTableOrderingComposer,
    $$TimeLogEntityTableCreateCompanionBuilder,
    $$TimeLogEntityTableUpdateCompanionBuilder,
    (
      TimeLogEntityData,
      BaseReferences<_$AppDatabase, $TimeLogEntityTable, TimeLogEntityData>
    ),
    TimeLogEntityData,
    PrefetchHooks Function()> {
  $$TimeLogEntityTableTableManager(_$AppDatabase db, $TimeLogEntityTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          filteringComposer:
              $$TimeLogEntityTableFilterComposer(ComposerState(db, table)),
          orderingComposer:
              $$TimeLogEntityTableOrderingComposer(ComposerState(db, table)),
          updateCompanionCallback: ({
            Value<String> id = const Value.absent(),
            Value<String> workOrderId = const Value.absent(),
            Value<TimeLogDbModel?> item = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              TimeLogEntityCompanion(
            id: id,
            workOrderId: workOrderId,
            item: item,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String id,
            required String workOrderId,
            Value<TimeLogDbModel?> item = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              TimeLogEntityCompanion.insert(
            id: id,
            workOrderId: workOrderId,
            item: item,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$TimeLogEntityTableProcessedTableManager = ProcessedTableManager<
    _$AppDatabase,
    $TimeLogEntityTable,
    TimeLogEntityData,
    $$TimeLogEntityTableFilterComposer,
    $$TimeLogEntityTableOrderingComposer,
    $$TimeLogEntityTableCreateCompanionBuilder,
    $$TimeLogEntityTableUpdateCompanionBuilder,
    (
      TimeLogEntityData,
      BaseReferences<_$AppDatabase, $TimeLogEntityTable, TimeLogEntityData>
    ),
    TimeLogEntityData,
    PrefetchHooks Function()>;
typedef $$AiNoteTableTableCreateCompanionBuilder = AiNoteTableCompanion
    Function({
  required String id,
  Value<AiNoteEntity?> note,
  Value<int> rowid,
});
typedef $$AiNoteTableTableUpdateCompanionBuilder = AiNoteTableCompanion
    Function({
  Value<String> id,
  Value<AiNoteEntity?> note,
  Value<int> rowid,
});

class $$AiNoteTableTableFilterComposer
    extends FilterComposer<_$AppDatabase, $AiNoteTableTable> {
  $$AiNoteTableTableFilterComposer(super.$state);
  ColumnFilters<String> get id => $state.composableBuilder(
      column: $state.table.id,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnWithTypeConverterFilters<AiNoteEntity?, AiNoteEntity, String>
      get note => $state.composableBuilder(
          column: $state.table.note,
          builder: (column, joinBuilders) => ColumnWithTypeConverterFilters(
              column,
              joinBuilders: joinBuilders));
}

class $$AiNoteTableTableOrderingComposer
    extends OrderingComposer<_$AppDatabase, $AiNoteTableTable> {
  $$AiNoteTableTableOrderingComposer(super.$state);
  ColumnOrderings<String> get id => $state.composableBuilder(
      column: $state.table.id,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get note => $state.composableBuilder(
      column: $state.table.note,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));
}

class $$AiNoteTableTableTableManager extends RootTableManager<
    _$AppDatabase,
    $AiNoteTableTable,
    AiNoteTableData,
    $$AiNoteTableTableFilterComposer,
    $$AiNoteTableTableOrderingComposer,
    $$AiNoteTableTableCreateCompanionBuilder,
    $$AiNoteTableTableUpdateCompanionBuilder,
    (
      AiNoteTableData,
      BaseReferences<_$AppDatabase, $AiNoteTableTable, AiNoteTableData>
    ),
    AiNoteTableData,
    PrefetchHooks Function()> {
  $$AiNoteTableTableTableManager(_$AppDatabase db, $AiNoteTableTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          filteringComposer:
              $$AiNoteTableTableFilterComposer(ComposerState(db, table)),
          orderingComposer:
              $$AiNoteTableTableOrderingComposer(ComposerState(db, table)),
          updateCompanionCallback: ({
            Value<String> id = const Value.absent(),
            Value<AiNoteEntity?> note = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              AiNoteTableCompanion(
            id: id,
            note: note,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String id,
            Value<AiNoteEntity?> note = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              AiNoteTableCompanion.insert(
            id: id,
            note: note,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$AiNoteTableTableProcessedTableManager = ProcessedTableManager<
    _$AppDatabase,
    $AiNoteTableTable,
    AiNoteTableData,
    $$AiNoteTableTableFilterComposer,
    $$AiNoteTableTableOrderingComposer,
    $$AiNoteTableTableCreateCompanionBuilder,
    $$AiNoteTableTableUpdateCompanionBuilder,
    (
      AiNoteTableData,
      BaseReferences<_$AppDatabase, $AiNoteTableTable, AiNoteTableData>
    ),
    AiNoteTableData,
    PrefetchHooks Function()>;
typedef $$ProcedureSubAssetEntityTableCreateCompanionBuilder
    = ProcedureSubAssetEntityCompanion Function({
  required String id,
  required String parentAssetId,
  Value<ProcedureSubAssetDbModel?> item,
  Value<int> rowid,
});
typedef $$ProcedureSubAssetEntityTableUpdateCompanionBuilder
    = ProcedureSubAssetEntityCompanion Function({
  Value<String> id,
  Value<String> parentAssetId,
  Value<ProcedureSubAssetDbModel?> item,
  Value<int> rowid,
});

class $$ProcedureSubAssetEntityTableFilterComposer
    extends FilterComposer<_$AppDatabase, $ProcedureSubAssetEntityTable> {
  $$ProcedureSubAssetEntityTableFilterComposer(super.$state);
  ColumnFilters<String> get id => $state.composableBuilder(
      column: $state.table.id,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get parentAssetId => $state.composableBuilder(
      column: $state.table.parentAssetId,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnWithTypeConverterFilters<ProcedureSubAssetDbModel?,
          ProcedureSubAssetDbModel, String>
      get item => $state.composableBuilder(
          column: $state.table.item,
          builder: (column, joinBuilders) => ColumnWithTypeConverterFilters(
              column,
              joinBuilders: joinBuilders));
}

class $$ProcedureSubAssetEntityTableOrderingComposer
    extends OrderingComposer<_$AppDatabase, $ProcedureSubAssetEntityTable> {
  $$ProcedureSubAssetEntityTableOrderingComposer(super.$state);
  ColumnOrderings<String> get id => $state.composableBuilder(
      column: $state.table.id,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get parentAssetId => $state.composableBuilder(
      column: $state.table.parentAssetId,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get item => $state.composableBuilder(
      column: $state.table.item,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));
}

class $$ProcedureSubAssetEntityTableTableManager extends RootTableManager<
    _$AppDatabase,
    $ProcedureSubAssetEntityTable,
    ProcedureSubAssetEntityData,
    $$ProcedureSubAssetEntityTableFilterComposer,
    $$ProcedureSubAssetEntityTableOrderingComposer,
    $$ProcedureSubAssetEntityTableCreateCompanionBuilder,
    $$ProcedureSubAssetEntityTableUpdateCompanionBuilder,
    (
      ProcedureSubAssetEntityData,
      BaseReferences<_$AppDatabase, $ProcedureSubAssetEntityTable,
          ProcedureSubAssetEntityData>
    ),
    ProcedureSubAssetEntityData,
    PrefetchHooks Function()> {
  $$ProcedureSubAssetEntityTableTableManager(
      _$AppDatabase db, $ProcedureSubAssetEntityTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          filteringComposer: $$ProcedureSubAssetEntityTableFilterComposer(
              ComposerState(db, table)),
          orderingComposer: $$ProcedureSubAssetEntityTableOrderingComposer(
              ComposerState(db, table)),
          updateCompanionCallback: ({
            Value<String> id = const Value.absent(),
            Value<String> parentAssetId = const Value.absent(),
            Value<ProcedureSubAssetDbModel?> item = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              ProcedureSubAssetEntityCompanion(
            id: id,
            parentAssetId: parentAssetId,
            item: item,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String id,
            required String parentAssetId,
            Value<ProcedureSubAssetDbModel?> item = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              ProcedureSubAssetEntityCompanion.insert(
            id: id,
            parentAssetId: parentAssetId,
            item: item,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$ProcedureSubAssetEntityTableProcessedTableManager
    = ProcessedTableManager<
        _$AppDatabase,
        $ProcedureSubAssetEntityTable,
        ProcedureSubAssetEntityData,
        $$ProcedureSubAssetEntityTableFilterComposer,
        $$ProcedureSubAssetEntityTableOrderingComposer,
        $$ProcedureSubAssetEntityTableCreateCompanionBuilder,
        $$ProcedureSubAssetEntityTableUpdateCompanionBuilder,
        (
          ProcedureSubAssetEntityData,
          BaseReferences<_$AppDatabase, $ProcedureSubAssetEntityTable,
              ProcedureSubAssetEntityData>
        ),
        ProcedureSubAssetEntityData,
        PrefetchHooks Function()>;
typedef $$CustomerRequestTableTableCreateCompanionBuilder
    = CustomerRequestTableCompanion Function({
  required String id,
  Value<CustomerRequestEntity?> item,
  Value<int> rowid,
});
typedef $$CustomerRequestTableTableUpdateCompanionBuilder
    = CustomerRequestTableCompanion Function({
  Value<String> id,
  Value<CustomerRequestEntity?> item,
  Value<int> rowid,
});

class $$CustomerRequestTableTableFilterComposer
    extends FilterComposer<_$AppDatabase, $CustomerRequestTableTable> {
  $$CustomerRequestTableTableFilterComposer(super.$state);
  ColumnFilters<String> get id => $state.composableBuilder(
      column: $state.table.id,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnWithTypeConverterFilters<CustomerRequestEntity?, CustomerRequestEntity,
          String>
      get item => $state.composableBuilder(
          column: $state.table.item,
          builder: (column, joinBuilders) => ColumnWithTypeConverterFilters(
              column,
              joinBuilders: joinBuilders));
}

class $$CustomerRequestTableTableOrderingComposer
    extends OrderingComposer<_$AppDatabase, $CustomerRequestTableTable> {
  $$CustomerRequestTableTableOrderingComposer(super.$state);
  ColumnOrderings<String> get id => $state.composableBuilder(
      column: $state.table.id,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get item => $state.composableBuilder(
      column: $state.table.item,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));
}

class $$CustomerRequestTableTableTableManager extends RootTableManager<
    _$AppDatabase,
    $CustomerRequestTableTable,
    CustomerRequestTableData,
    $$CustomerRequestTableTableFilterComposer,
    $$CustomerRequestTableTableOrderingComposer,
    $$CustomerRequestTableTableCreateCompanionBuilder,
    $$CustomerRequestTableTableUpdateCompanionBuilder,
    (
      CustomerRequestTableData,
      BaseReferences<_$AppDatabase, $CustomerRequestTableTable,
          CustomerRequestTableData>
    ),
    CustomerRequestTableData,
    PrefetchHooks Function()> {
  $$CustomerRequestTableTableTableManager(
      _$AppDatabase db, $CustomerRequestTableTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          filteringComposer: $$CustomerRequestTableTableFilterComposer(
              ComposerState(db, table)),
          orderingComposer: $$CustomerRequestTableTableOrderingComposer(
              ComposerState(db, table)),
          updateCompanionCallback: ({
            Value<String> id = const Value.absent(),
            Value<CustomerRequestEntity?> item = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              CustomerRequestTableCompanion(
            id: id,
            item: item,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String id,
            Value<CustomerRequestEntity?> item = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              CustomerRequestTableCompanion.insert(
            id: id,
            item: item,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$CustomerRequestTableTableProcessedTableManager
    = ProcessedTableManager<
        _$AppDatabase,
        $CustomerRequestTableTable,
        CustomerRequestTableData,
        $$CustomerRequestTableTableFilterComposer,
        $$CustomerRequestTableTableOrderingComposer,
        $$CustomerRequestTableTableCreateCompanionBuilder,
        $$CustomerRequestTableTableUpdateCompanionBuilder,
        (
          CustomerRequestTableData,
          BaseReferences<_$AppDatabase, $CustomerRequestTableTable,
              CustomerRequestTableData>
        ),
        CustomerRequestTableData,
        PrefetchHooks Function()>;
typedef $$CustomerRequestListTableTableCreateCompanionBuilder
    = CustomerRequestListTableCompanion Function({
  required String id,
  required String type,
  Value<int> rowid,
});
typedef $$CustomerRequestListTableTableUpdateCompanionBuilder
    = CustomerRequestListTableCompanion Function({
  Value<String> id,
  Value<String> type,
  Value<int> rowid,
});

class $$CustomerRequestListTableTableFilterComposer
    extends FilterComposer<_$AppDatabase, $CustomerRequestListTableTable> {
  $$CustomerRequestListTableTableFilterComposer(super.$state);
  ColumnFilters<String> get id => $state.composableBuilder(
      column: $state.table.id,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get type => $state.composableBuilder(
      column: $state.table.type,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));
}

class $$CustomerRequestListTableTableOrderingComposer
    extends OrderingComposer<_$AppDatabase, $CustomerRequestListTableTable> {
  $$CustomerRequestListTableTableOrderingComposer(super.$state);
  ColumnOrderings<String> get id => $state.composableBuilder(
      column: $state.table.id,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get type => $state.composableBuilder(
      column: $state.table.type,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));
}

class $$CustomerRequestListTableTableTableManager extends RootTableManager<
    _$AppDatabase,
    $CustomerRequestListTableTable,
    CustomerRequestListTableData,
    $$CustomerRequestListTableTableFilterComposer,
    $$CustomerRequestListTableTableOrderingComposer,
    $$CustomerRequestListTableTableCreateCompanionBuilder,
    $$CustomerRequestListTableTableUpdateCompanionBuilder,
    (
      CustomerRequestListTableData,
      BaseReferences<_$AppDatabase, $CustomerRequestListTableTable,
          CustomerRequestListTableData>
    ),
    CustomerRequestListTableData,
    PrefetchHooks Function()> {
  $$CustomerRequestListTableTableTableManager(
      _$AppDatabase db, $CustomerRequestListTableTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          filteringComposer: $$CustomerRequestListTableTableFilterComposer(
              ComposerState(db, table)),
          orderingComposer: $$CustomerRequestListTableTableOrderingComposer(
              ComposerState(db, table)),
          updateCompanionCallback: ({
            Value<String> id = const Value.absent(),
            Value<String> type = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              CustomerRequestListTableCompanion(
            id: id,
            type: type,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String id,
            required String type,
            Value<int> rowid = const Value.absent(),
          }) =>
              CustomerRequestListTableCompanion.insert(
            id: id,
            type: type,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$CustomerRequestListTableTableProcessedTableManager
    = ProcessedTableManager<
        _$AppDatabase,
        $CustomerRequestListTableTable,
        CustomerRequestListTableData,
        $$CustomerRequestListTableTableFilterComposer,
        $$CustomerRequestListTableTableOrderingComposer,
        $$CustomerRequestListTableTableCreateCompanionBuilder,
        $$CustomerRequestListTableTableUpdateCompanionBuilder,
        (
          CustomerRequestListTableData,
          BaseReferences<_$AppDatabase, $CustomerRequestListTableTable,
              CustomerRequestListTableData>
        ),
        CustomerRequestListTableData,
        PrefetchHooks Function()>;
typedef $$CustomerRequestCountTableTableCreateCompanionBuilder
    = CustomerRequestCountTableCompanion Function({
  required String type,
  Value<int> count,
  Value<int> rowid,
});
typedef $$CustomerRequestCountTableTableUpdateCompanionBuilder
    = CustomerRequestCountTableCompanion Function({
  Value<String> type,
  Value<int> count,
  Value<int> rowid,
});

class $$CustomerRequestCountTableTableFilterComposer
    extends FilterComposer<_$AppDatabase, $CustomerRequestCountTableTable> {
  $$CustomerRequestCountTableTableFilterComposer(super.$state);
  ColumnFilters<String> get type => $state.composableBuilder(
      column: $state.table.type,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<int> get count => $state.composableBuilder(
      column: $state.table.count,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));
}

class $$CustomerRequestCountTableTableOrderingComposer
    extends OrderingComposer<_$AppDatabase, $CustomerRequestCountTableTable> {
  $$CustomerRequestCountTableTableOrderingComposer(super.$state);
  ColumnOrderings<String> get type => $state.composableBuilder(
      column: $state.table.type,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<int> get count => $state.composableBuilder(
      column: $state.table.count,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));
}

class $$CustomerRequestCountTableTableTableManager extends RootTableManager<
    _$AppDatabase,
    $CustomerRequestCountTableTable,
    CustomerRequestCountTableData,
    $$CustomerRequestCountTableTableFilterComposer,
    $$CustomerRequestCountTableTableOrderingComposer,
    $$CustomerRequestCountTableTableCreateCompanionBuilder,
    $$CustomerRequestCountTableTableUpdateCompanionBuilder,
    (
      CustomerRequestCountTableData,
      BaseReferences<_$AppDatabase, $CustomerRequestCountTableTable,
          CustomerRequestCountTableData>
    ),
    CustomerRequestCountTableData,
    PrefetchHooks Function()> {
  $$CustomerRequestCountTableTableTableManager(
      _$AppDatabase db, $CustomerRequestCountTableTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          filteringComposer: $$CustomerRequestCountTableTableFilterComposer(
              ComposerState(db, table)),
          orderingComposer: $$CustomerRequestCountTableTableOrderingComposer(
              ComposerState(db, table)),
          updateCompanionCallback: ({
            Value<String> type = const Value.absent(),
            Value<int> count = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              CustomerRequestCountTableCompanion(
            type: type,
            count: count,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String type,
            Value<int> count = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              CustomerRequestCountTableCompanion.insert(
            type: type,
            count: count,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$CustomerRequestCountTableTableProcessedTableManager
    = ProcessedTableManager<
        _$AppDatabase,
        $CustomerRequestCountTableTable,
        CustomerRequestCountTableData,
        $$CustomerRequestCountTableTableFilterComposer,
        $$CustomerRequestCountTableTableOrderingComposer,
        $$CustomerRequestCountTableTableCreateCompanionBuilder,
        $$CustomerRequestCountTableTableUpdateCompanionBuilder,
        (
          CustomerRequestCountTableData,
          BaseReferences<_$AppDatabase, $CustomerRequestCountTableTable,
              CustomerRequestCountTableData>
        ),
        CustomerRequestCountTableData,
        PrefetchHooks Function()>;
typedef $$AiAssistantTableTableCreateCompanionBuilder
    = AiAssistantTableCompanion Function({
  required String id,
  required String type,
  Value<AiAssistantEntity?> assistant,
  Value<int> rowid,
});
typedef $$AiAssistantTableTableUpdateCompanionBuilder
    = AiAssistantTableCompanion Function({
  Value<String> id,
  Value<String> type,
  Value<AiAssistantEntity?> assistant,
  Value<int> rowid,
});

class $$AiAssistantTableTableFilterComposer
    extends FilterComposer<_$AppDatabase, $AiAssistantTableTable> {
  $$AiAssistantTableTableFilterComposer(super.$state);
  ColumnFilters<String> get id => $state.composableBuilder(
      column: $state.table.id,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get type => $state.composableBuilder(
      column: $state.table.type,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnWithTypeConverterFilters<AiAssistantEntity?, AiAssistantEntity, String>
      get assistant => $state.composableBuilder(
          column: $state.table.assistant,
          builder: (column, joinBuilders) => ColumnWithTypeConverterFilters(
              column,
              joinBuilders: joinBuilders));
}

class $$AiAssistantTableTableOrderingComposer
    extends OrderingComposer<_$AppDatabase, $AiAssistantTableTable> {
  $$AiAssistantTableTableOrderingComposer(super.$state);
  ColumnOrderings<String> get id => $state.composableBuilder(
      column: $state.table.id,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get type => $state.composableBuilder(
      column: $state.table.type,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get assistant => $state.composableBuilder(
      column: $state.table.assistant,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));
}

class $$AiAssistantTableTableTableManager extends RootTableManager<
    _$AppDatabase,
    $AiAssistantTableTable,
    AiAssistantTableData,
    $$AiAssistantTableTableFilterComposer,
    $$AiAssistantTableTableOrderingComposer,
    $$AiAssistantTableTableCreateCompanionBuilder,
    $$AiAssistantTableTableUpdateCompanionBuilder,
    (
      AiAssistantTableData,
      BaseReferences<_$AppDatabase, $AiAssistantTableTable,
          AiAssistantTableData>
    ),
    AiAssistantTableData,
    PrefetchHooks Function()> {
  $$AiAssistantTableTableTableManager(
      _$AppDatabase db, $AiAssistantTableTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          filteringComposer:
              $$AiAssistantTableTableFilterComposer(ComposerState(db, table)),
          orderingComposer:
              $$AiAssistantTableTableOrderingComposer(ComposerState(db, table)),
          updateCompanionCallback: ({
            Value<String> id = const Value.absent(),
            Value<String> type = const Value.absent(),
            Value<AiAssistantEntity?> assistant = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              AiAssistantTableCompanion(
            id: id,
            type: type,
            assistant: assistant,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String id,
            required String type,
            Value<AiAssistantEntity?> assistant = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              AiAssistantTableCompanion.insert(
            id: id,
            type: type,
            assistant: assistant,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$AiAssistantTableTableProcessedTableManager = ProcessedTableManager<
    _$AppDatabase,
    $AiAssistantTableTable,
    AiAssistantTableData,
    $$AiAssistantTableTableFilterComposer,
    $$AiAssistantTableTableOrderingComposer,
    $$AiAssistantTableTableCreateCompanionBuilder,
    $$AiAssistantTableTableUpdateCompanionBuilder,
    (
      AiAssistantTableData,
      BaseReferences<_$AppDatabase, $AiAssistantTableTable,
          AiAssistantTableData>
    ),
    AiAssistantTableData,
    PrefetchHooks Function()>;
typedef $$ConnectionTableTableCreateCompanionBuilder = ConnectionTableCompanion
    Function({
  required String id,
  Value<DateTime?> lastAssetSync,
  Value<ConnectionEntity?> connection,
  Value<int> rowid,
});
typedef $$ConnectionTableTableUpdateCompanionBuilder = ConnectionTableCompanion
    Function({
  Value<String> id,
  Value<DateTime?> lastAssetSync,
  Value<ConnectionEntity?> connection,
  Value<int> rowid,
});

class $$ConnectionTableTableFilterComposer
    extends FilterComposer<_$AppDatabase, $ConnectionTableTable> {
  $$ConnectionTableTableFilterComposer(super.$state);
  ColumnFilters<String> get id => $state.composableBuilder(
      column: $state.table.id,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<DateTime> get lastAssetSync => $state.composableBuilder(
      column: $state.table.lastAssetSync,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnWithTypeConverterFilters<ConnectionEntity?, ConnectionEntity, String>
      get connection => $state.composableBuilder(
          column: $state.table.connection,
          builder: (column, joinBuilders) => ColumnWithTypeConverterFilters(
              column,
              joinBuilders: joinBuilders));
}

class $$ConnectionTableTableOrderingComposer
    extends OrderingComposer<_$AppDatabase, $ConnectionTableTable> {
  $$ConnectionTableTableOrderingComposer(super.$state);
  ColumnOrderings<String> get id => $state.composableBuilder(
      column: $state.table.id,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<DateTime> get lastAssetSync => $state.composableBuilder(
      column: $state.table.lastAssetSync,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get connection => $state.composableBuilder(
      column: $state.table.connection,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));
}

class $$ConnectionTableTableTableManager extends RootTableManager<
    _$AppDatabase,
    $ConnectionTableTable,
    ConnectionTableData,
    $$ConnectionTableTableFilterComposer,
    $$ConnectionTableTableOrderingComposer,
    $$ConnectionTableTableCreateCompanionBuilder,
    $$ConnectionTableTableUpdateCompanionBuilder,
    (
      ConnectionTableData,
      BaseReferences<_$AppDatabase, $ConnectionTableTable, ConnectionTableData>
    ),
    ConnectionTableData,
    PrefetchHooks Function()> {
  $$ConnectionTableTableTableManager(
      _$AppDatabase db, $ConnectionTableTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          filteringComposer:
              $$ConnectionTableTableFilterComposer(ComposerState(db, table)),
          orderingComposer:
              $$ConnectionTableTableOrderingComposer(ComposerState(db, table)),
          updateCompanionCallback: ({
            Value<String> id = const Value.absent(),
            Value<DateTime?> lastAssetSync = const Value.absent(),
            Value<ConnectionEntity?> connection = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              ConnectionTableCompanion(
            id: id,
            lastAssetSync: lastAssetSync,
            connection: connection,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String id,
            Value<DateTime?> lastAssetSync = const Value.absent(),
            Value<ConnectionEntity?> connection = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              ConnectionTableCompanion.insert(
            id: id,
            lastAssetSync: lastAssetSync,
            connection: connection,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$ConnectionTableTableProcessedTableManager = ProcessedTableManager<
    _$AppDatabase,
    $ConnectionTableTable,
    ConnectionTableData,
    $$ConnectionTableTableFilterComposer,
    $$ConnectionTableTableOrderingComposer,
    $$ConnectionTableTableCreateCompanionBuilder,
    $$ConnectionTableTableUpdateCompanionBuilder,
    (
      ConnectionTableData,
      BaseReferences<_$AppDatabase, $ConnectionTableTable, ConnectionTableData>
    ),
    ConnectionTableData,
    PrefetchHooks Function()>;
typedef $$CustomFieldsTableTableCreateCompanionBuilder
    = CustomFieldsTableCompanion Function({
  required String type,
  Value<List<CustomFieldEntity>?> value,
  Value<int> rowid,
});
typedef $$CustomFieldsTableTableUpdateCompanionBuilder
    = CustomFieldsTableCompanion Function({
  Value<String> type,
  Value<List<CustomFieldEntity>?> value,
  Value<int> rowid,
});

class $$CustomFieldsTableTableFilterComposer
    extends FilterComposer<_$AppDatabase, $CustomFieldsTableTable> {
  $$CustomFieldsTableTableFilterComposer(super.$state);
  ColumnFilters<String> get type => $state.composableBuilder(
      column: $state.table.type,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnWithTypeConverterFilters<List<CustomFieldEntity>?,
          List<CustomFieldEntity>, String>
      get value => $state.composableBuilder(
          column: $state.table.value,
          builder: (column, joinBuilders) => ColumnWithTypeConverterFilters(
              column,
              joinBuilders: joinBuilders));
}

class $$CustomFieldsTableTableOrderingComposer
    extends OrderingComposer<_$AppDatabase, $CustomFieldsTableTable> {
  $$CustomFieldsTableTableOrderingComposer(super.$state);
  ColumnOrderings<String> get type => $state.composableBuilder(
      column: $state.table.type,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get value => $state.composableBuilder(
      column: $state.table.value,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));
}

class $$CustomFieldsTableTableTableManager extends RootTableManager<
    _$AppDatabase,
    $CustomFieldsTableTable,
    CustomFieldsTableData,
    $$CustomFieldsTableTableFilterComposer,
    $$CustomFieldsTableTableOrderingComposer,
    $$CustomFieldsTableTableCreateCompanionBuilder,
    $$CustomFieldsTableTableUpdateCompanionBuilder,
    (
      CustomFieldsTableData,
      BaseReferences<_$AppDatabase, $CustomFieldsTableTable,
          CustomFieldsTableData>
    ),
    CustomFieldsTableData,
    PrefetchHooks Function()> {
  $$CustomFieldsTableTableTableManager(
      _$AppDatabase db, $CustomFieldsTableTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          filteringComposer:
              $$CustomFieldsTableTableFilterComposer(ComposerState(db, table)),
          orderingComposer: $$CustomFieldsTableTableOrderingComposer(
              ComposerState(db, table)),
          updateCompanionCallback: ({
            Value<String> type = const Value.absent(),
            Value<List<CustomFieldEntity>?> value = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              CustomFieldsTableCompanion(
            type: type,
            value: value,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String type,
            Value<List<CustomFieldEntity>?> value = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              CustomFieldsTableCompanion.insert(
            type: type,
            value: value,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$CustomFieldsTableTableProcessedTableManager = ProcessedTableManager<
    _$AppDatabase,
    $CustomFieldsTableTable,
    CustomFieldsTableData,
    $$CustomFieldsTableTableFilterComposer,
    $$CustomFieldsTableTableOrderingComposer,
    $$CustomFieldsTableTableCreateCompanionBuilder,
    $$CustomFieldsTableTableUpdateCompanionBuilder,
    (
      CustomFieldsTableData,
      BaseReferences<_$AppDatabase, $CustomFieldsTableTable,
          CustomFieldsTableData>
    ),
    CustomFieldsTableData,
    PrefetchHooks Function()>;
typedef $$CustomFieldValuesTableTableCreateCompanionBuilder
    = CustomFieldValuesTableCompanion Function({
  required String resourceId,
  required String type,
  required bool isShared,
  Value<List<CustomFieldValueEntity>?> value,
  Value<int> rowid,
});
typedef $$CustomFieldValuesTableTableUpdateCompanionBuilder
    = CustomFieldValuesTableCompanion Function({
  Value<String> resourceId,
  Value<String> type,
  Value<bool> isShared,
  Value<List<CustomFieldValueEntity>?> value,
  Value<int> rowid,
});

class $$CustomFieldValuesTableTableFilterComposer
    extends FilterComposer<_$AppDatabase, $CustomFieldValuesTableTable> {
  $$CustomFieldValuesTableTableFilterComposer(super.$state);
  ColumnFilters<String> get resourceId => $state.composableBuilder(
      column: $state.table.resourceId,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get type => $state.composableBuilder(
      column: $state.table.type,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<bool> get isShared => $state.composableBuilder(
      column: $state.table.isShared,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnWithTypeConverterFilters<List<CustomFieldValueEntity>?,
          List<CustomFieldValueEntity>, String>
      get value => $state.composableBuilder(
          column: $state.table.value,
          builder: (column, joinBuilders) => ColumnWithTypeConverterFilters(
              column,
              joinBuilders: joinBuilders));
}

class $$CustomFieldValuesTableTableOrderingComposer
    extends OrderingComposer<_$AppDatabase, $CustomFieldValuesTableTable> {
  $$CustomFieldValuesTableTableOrderingComposer(super.$state);
  ColumnOrderings<String> get resourceId => $state.composableBuilder(
      column: $state.table.resourceId,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get type => $state.composableBuilder(
      column: $state.table.type,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<bool> get isShared => $state.composableBuilder(
      column: $state.table.isShared,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get value => $state.composableBuilder(
      column: $state.table.value,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));
}

class $$CustomFieldValuesTableTableTableManager extends RootTableManager<
    _$AppDatabase,
    $CustomFieldValuesTableTable,
    CustomFieldValuesTableData,
    $$CustomFieldValuesTableTableFilterComposer,
    $$CustomFieldValuesTableTableOrderingComposer,
    $$CustomFieldValuesTableTableCreateCompanionBuilder,
    $$CustomFieldValuesTableTableUpdateCompanionBuilder,
    (
      CustomFieldValuesTableData,
      BaseReferences<_$AppDatabase, $CustomFieldValuesTableTable,
          CustomFieldValuesTableData>
    ),
    CustomFieldValuesTableData,
    PrefetchHooks Function()> {
  $$CustomFieldValuesTableTableTableManager(
      _$AppDatabase db, $CustomFieldValuesTableTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          filteringComposer: $$CustomFieldValuesTableTableFilterComposer(
              ComposerState(db, table)),
          orderingComposer: $$CustomFieldValuesTableTableOrderingComposer(
              ComposerState(db, table)),
          updateCompanionCallback: ({
            Value<String> resourceId = const Value.absent(),
            Value<String> type = const Value.absent(),
            Value<bool> isShared = const Value.absent(),
            Value<List<CustomFieldValueEntity>?> value = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              CustomFieldValuesTableCompanion(
            resourceId: resourceId,
            type: type,
            isShared: isShared,
            value: value,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String resourceId,
            required String type,
            required bool isShared,
            Value<List<CustomFieldValueEntity>?> value = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              CustomFieldValuesTableCompanion.insert(
            resourceId: resourceId,
            type: type,
            isShared: isShared,
            value: value,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$CustomFieldValuesTableTableProcessedTableManager
    = ProcessedTableManager<
        _$AppDatabase,
        $CustomFieldValuesTableTable,
        CustomFieldValuesTableData,
        $$CustomFieldValuesTableTableFilterComposer,
        $$CustomFieldValuesTableTableOrderingComposer,
        $$CustomFieldValuesTableTableCreateCompanionBuilder,
        $$CustomFieldValuesTableTableUpdateCompanionBuilder,
        (
          CustomFieldValuesTableData,
          BaseReferences<_$AppDatabase, $CustomFieldValuesTableTable,
              CustomFieldValuesTableData>
        ),
        CustomFieldValuesTableData,
        PrefetchHooks Function()>;
typedef $$AssetTableTableCreateCompanionBuilder = AssetTableCompanion Function({
  required String id,
  Value<String?> connectionId,
  Value<String?> parentAssetId,
  Value<bool> isSharedAsset,
  Value<AssetLastSyncEntity?> lastSync,
  Value<AssetDetailsEntity?> asset,
  Value<int> rowid,
});
typedef $$AssetTableTableUpdateCompanionBuilder = AssetTableCompanion Function({
  Value<String> id,
  Value<String?> connectionId,
  Value<String?> parentAssetId,
  Value<bool> isSharedAsset,
  Value<AssetLastSyncEntity?> lastSync,
  Value<AssetDetailsEntity?> asset,
  Value<int> rowid,
});

class $$AssetTableTableFilterComposer
    extends FilterComposer<_$AppDatabase, $AssetTableTable> {
  $$AssetTableTableFilterComposer(super.$state);
  ColumnFilters<String> get id => $state.composableBuilder(
      column: $state.table.id,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get connectionId => $state.composableBuilder(
      column: $state.table.connectionId,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get parentAssetId => $state.composableBuilder(
      column: $state.table.parentAssetId,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<bool> get isSharedAsset => $state.composableBuilder(
      column: $state.table.isSharedAsset,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnWithTypeConverterFilters<AssetLastSyncEntity?, AssetLastSyncEntity,
          String>
      get lastSync => $state.composableBuilder(
          column: $state.table.lastSync,
          builder: (column, joinBuilders) => ColumnWithTypeConverterFilters(
              column,
              joinBuilders: joinBuilders));

  ColumnWithTypeConverterFilters<AssetDetailsEntity?, AssetDetailsEntity,
          String>
      get asset => $state.composableBuilder(
          column: $state.table.asset,
          builder: (column, joinBuilders) => ColumnWithTypeConverterFilters(
              column,
              joinBuilders: joinBuilders));
}

class $$AssetTableTableOrderingComposer
    extends OrderingComposer<_$AppDatabase, $AssetTableTable> {
  $$AssetTableTableOrderingComposer(super.$state);
  ColumnOrderings<String> get id => $state.composableBuilder(
      column: $state.table.id,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get connectionId => $state.composableBuilder(
      column: $state.table.connectionId,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get parentAssetId => $state.composableBuilder(
      column: $state.table.parentAssetId,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<bool> get isSharedAsset => $state.composableBuilder(
      column: $state.table.isSharedAsset,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get lastSync => $state.composableBuilder(
      column: $state.table.lastSync,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get asset => $state.composableBuilder(
      column: $state.table.asset,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));
}

class $$AssetTableTableTableManager extends RootTableManager<
    _$AppDatabase,
    $AssetTableTable,
    AssetTableData,
    $$AssetTableTableFilterComposer,
    $$AssetTableTableOrderingComposer,
    $$AssetTableTableCreateCompanionBuilder,
    $$AssetTableTableUpdateCompanionBuilder,
    (
      AssetTableData,
      BaseReferences<_$AppDatabase, $AssetTableTable, AssetTableData>
    ),
    AssetTableData,
    PrefetchHooks Function()> {
  $$AssetTableTableTableManager(_$AppDatabase db, $AssetTableTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          filteringComposer:
              $$AssetTableTableFilterComposer(ComposerState(db, table)),
          orderingComposer:
              $$AssetTableTableOrderingComposer(ComposerState(db, table)),
          updateCompanionCallback: ({
            Value<String> id = const Value.absent(),
            Value<String?> connectionId = const Value.absent(),
            Value<String?> parentAssetId = const Value.absent(),
            Value<bool> isSharedAsset = const Value.absent(),
            Value<AssetLastSyncEntity?> lastSync = const Value.absent(),
            Value<AssetDetailsEntity?> asset = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              AssetTableCompanion(
            id: id,
            connectionId: connectionId,
            parentAssetId: parentAssetId,
            isSharedAsset: isSharedAsset,
            lastSync: lastSync,
            asset: asset,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String id,
            Value<String?> connectionId = const Value.absent(),
            Value<String?> parentAssetId = const Value.absent(),
            Value<bool> isSharedAsset = const Value.absent(),
            Value<AssetLastSyncEntity?> lastSync = const Value.absent(),
            Value<AssetDetailsEntity?> asset = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              AssetTableCompanion.insert(
            id: id,
            connectionId: connectionId,
            parentAssetId: parentAssetId,
            isSharedAsset: isSharedAsset,
            lastSync: lastSync,
            asset: asset,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$AssetTableTableProcessedTableManager = ProcessedTableManager<
    _$AppDatabase,
    $AssetTableTable,
    AssetTableData,
    $$AssetTableTableFilterComposer,
    $$AssetTableTableOrderingComposer,
    $$AssetTableTableCreateCompanionBuilder,
    $$AssetTableTableUpdateCompanionBuilder,
    (
      AssetTableData,
      BaseReferences<_$AppDatabase, $AssetTableTable, AssetTableData>
    ),
    AssetTableData,
    PrefetchHooks Function()>;
typedef $$AssetHistoryTableTableCreateCompanionBuilder
    = AssetHistoryTableCompanion Function({
  required String id,
  required String assetId,
  required DateTime createdAt,
  Value<AssetHistoryItemEntity?> assetHistory,
  Value<int> rowid,
});
typedef $$AssetHistoryTableTableUpdateCompanionBuilder
    = AssetHistoryTableCompanion Function({
  Value<String> id,
  Value<String> assetId,
  Value<DateTime> createdAt,
  Value<AssetHistoryItemEntity?> assetHistory,
  Value<int> rowid,
});

class $$AssetHistoryTableTableFilterComposer
    extends FilterComposer<_$AppDatabase, $AssetHistoryTableTable> {
  $$AssetHistoryTableTableFilterComposer(super.$state);
  ColumnFilters<String> get id => $state.composableBuilder(
      column: $state.table.id,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get assetId => $state.composableBuilder(
      column: $state.table.assetId,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<DateTime> get createdAt => $state.composableBuilder(
      column: $state.table.createdAt,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnWithTypeConverterFilters<AssetHistoryItemEntity?,
          AssetHistoryItemEntity, String>
      get assetHistory => $state.composableBuilder(
          column: $state.table.assetHistory,
          builder: (column, joinBuilders) => ColumnWithTypeConverterFilters(
              column,
              joinBuilders: joinBuilders));
}

class $$AssetHistoryTableTableOrderingComposer
    extends OrderingComposer<_$AppDatabase, $AssetHistoryTableTable> {
  $$AssetHistoryTableTableOrderingComposer(super.$state);
  ColumnOrderings<String> get id => $state.composableBuilder(
      column: $state.table.id,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get assetId => $state.composableBuilder(
      column: $state.table.assetId,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<DateTime> get createdAt => $state.composableBuilder(
      column: $state.table.createdAt,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get assetHistory => $state.composableBuilder(
      column: $state.table.assetHistory,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));
}

class $$AssetHistoryTableTableTableManager extends RootTableManager<
    _$AppDatabase,
    $AssetHistoryTableTable,
    AssetHistoryTableData,
    $$AssetHistoryTableTableFilterComposer,
    $$AssetHistoryTableTableOrderingComposer,
    $$AssetHistoryTableTableCreateCompanionBuilder,
    $$AssetHistoryTableTableUpdateCompanionBuilder,
    (
      AssetHistoryTableData,
      BaseReferences<_$AppDatabase, $AssetHistoryTableTable,
          AssetHistoryTableData>
    ),
    AssetHistoryTableData,
    PrefetchHooks Function()> {
  $$AssetHistoryTableTableTableManager(
      _$AppDatabase db, $AssetHistoryTableTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          filteringComposer:
              $$AssetHistoryTableTableFilterComposer(ComposerState(db, table)),
          orderingComposer: $$AssetHistoryTableTableOrderingComposer(
              ComposerState(db, table)),
          updateCompanionCallback: ({
            Value<String> id = const Value.absent(),
            Value<String> assetId = const Value.absent(),
            Value<DateTime> createdAt = const Value.absent(),
            Value<AssetHistoryItemEntity?> assetHistory = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              AssetHistoryTableCompanion(
            id: id,
            assetId: assetId,
            createdAt: createdAt,
            assetHistory: assetHistory,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String id,
            required String assetId,
            required DateTime createdAt,
            Value<AssetHistoryItemEntity?> assetHistory = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              AssetHistoryTableCompanion.insert(
            id: id,
            assetId: assetId,
            createdAt: createdAt,
            assetHistory: assetHistory,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$AssetHistoryTableTableProcessedTableManager = ProcessedTableManager<
    _$AppDatabase,
    $AssetHistoryTableTable,
    AssetHistoryTableData,
    $$AssetHistoryTableTableFilterComposer,
    $$AssetHistoryTableTableOrderingComposer,
    $$AssetHistoryTableTableCreateCompanionBuilder,
    $$AssetHistoryTableTableUpdateCompanionBuilder,
    (
      AssetHistoryTableData,
      BaseReferences<_$AppDatabase, $AssetHistoryTableTable,
          AssetHistoryTableData>
    ),
    AssetHistoryTableData,
    PrefetchHooks Function()>;

class $AppDatabaseManager {
  final _$AppDatabase _db;
  $AppDatabaseManager(this._db);
  $$AuthEntityTableTableManager get authEntity =>
      $$AuthEntityTableTableManager(_db, _db.authEntity);
  $$CurrentUserEntityTableTableManager get currentUserEntity =>
      $$CurrentUserEntityTableTableManager(_db, _db.currentUserEntity);
  $$TicketEntityTableTableManager get ticketEntity =>
      $$TicketEntityTableTableManager(_db, _db.ticketEntity);
  $$TicketMessageEntityTableTableManager get ticketMessageEntity =>
      $$TicketMessageEntityTableTableManager(_db, _db.ticketMessageEntity);
  $$ProcedureTemplateEntityTableTableManager get procedureTemplateEntity =>
      $$ProcedureTemplateEntityTableTableManager(
          _db, _db.procedureTemplateEntity);
  $$TicketListEntityTableTableManager get ticketListEntity =>
      $$TicketListEntityTableTableManager(_db, _db.ticketListEntity);
  $$InventoryPartEntityTableTableManager get inventoryPartEntity =>
      $$InventoryPartEntityTableTableManager(_db, _db.inventoryPartEntity);
  $$ProcedureEntityTableTableManager get procedureEntity =>
      $$ProcedureEntityTableTableManager(_db, _db.procedureEntity);
  $$SupportAccountEntityTableTableManager get supportAccountEntity =>
      $$SupportAccountEntityTableTableManager(_db, _db.supportAccountEntity);
  $$OemDbEntityTableTableManager get oemDbEntity =>
      $$OemDbEntityTableTableManager(_db, _db.oemDbEntity);
  $$TicketCountEntityTableTableManager get ticketCountEntity =>
      $$TicketCountEntityTableTableManager(_db, _db.ticketCountEntity);
  $$WorkOrderPartsEntityTableTableManager get workOrderPartsEntity =>
      $$WorkOrderPartsEntityTableTableManager(_db, _db.workOrderPartsEntity);
  $$TimeLogEntityTableTableManager get timeLogEntity =>
      $$TimeLogEntityTableTableManager(_db, _db.timeLogEntity);
  $$AiNoteTableTableTableManager get aiNoteTable =>
      $$AiNoteTableTableTableManager(_db, _db.aiNoteTable);
  $$ProcedureSubAssetEntityTableTableManager get procedureSubAssetEntity =>
      $$ProcedureSubAssetEntityTableTableManager(
          _db, _db.procedureSubAssetEntity);
  $$CustomerRequestTableTableTableManager get customerRequestTable =>
      $$CustomerRequestTableTableTableManager(_db, _db.customerRequestTable);
  $$CustomerRequestListTableTableTableManager get customerRequestListTable =>
      $$CustomerRequestListTableTableTableManager(
          _db, _db.customerRequestListTable);
  $$CustomerRequestCountTableTableTableManager get customerRequestCountTable =>
      $$CustomerRequestCountTableTableTableManager(
          _db, _db.customerRequestCountTable);
  $$AiAssistantTableTableTableManager get aiAssistantTable =>
      $$AiAssistantTableTableTableManager(_db, _db.aiAssistantTable);
  $$ConnectionTableTableTableManager get connectionTable =>
      $$ConnectionTableTableTableManager(_db, _db.connectionTable);
  $$CustomFieldsTableTableTableManager get customFieldsTable =>
      $$CustomFieldsTableTableTableManager(_db, _db.customFieldsTable);
  $$CustomFieldValuesTableTableTableManager get customFieldValuesTable =>
      $$CustomFieldValuesTableTableTableManager(
          _db, _db.customFieldValuesTable);
  $$AssetTableTableTableManager get assetTable =>
      $$AssetTableTableTableManager(_db, _db.assetTable);
  $$AssetHistoryTableTableTableManager get assetHistoryTable =>
      $$AssetHistoryTableTableTableManager(_db, _db.assetHistoryTable);
}
